<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="goldGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#f4e4a6;stop-opacity:0.3" />
      <stop offset="50%" style="stop-color:#d4af37;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#b8941f;stop-opacity:0.1" />
    </radialGradient>
    <pattern id="goldPattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <rect width="40" height="40" fill="none"/>
      <circle cx="20" cy="20" r="15" fill="url(#goldGradient)" opacity="0.4"/>
      <circle cx="0" cy="0" r="8" fill="url(#goldGradient)" opacity="0.2"/>
      <circle cx="40" cy="0" r="8" fill="url(#goldGradient)" opacity="0.2"/>
      <circle cx="0" cy="40" r="8" fill="url(#goldGradient)" opacity="0.2"/>
      <circle cx="40" cy="40" r="8" fill="url(#goldGradient)" opacity="0.2"/>
    </pattern>
  </defs>
  <rect width="200" height="200" fill="url(#goldPattern)"/>
</svg>
