{"version": 3, "sources": ["swiper.js"], "names": ["global", "factory", "exports", "module", "define", "amd", "self", "Swiper", "this", "doc", "document", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "location", "hash", "win", "window", "navigator", "userAgent", "history", "CustomEvent", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "setTimeout", "clearTimeout", "Dom7", "arr", "i", "length", "$", "selector", "context", "els", "tempParent", "html", "trim", "indexOf", "toCreate", "innerHTML", "push", "match", "split", "nodeType", "unique", "uniqueArray", "fn", "prototype", "Class", "Methods", "addClass", "className", "classes", "j", "classList", "add", "removeClass", "remove", "hasClass", "contains", "toggleClass", "toggle", "attr", "attrs", "value", "arguments$1", "arguments", "getAttribute", "attrName", "removeAttr", "removeAttribute", "data", "key", "el", "dom7ElementDataStorage", "dataKey", "transform", "elStyle", "webkitTransform", "transition", "duration", "webkitTransitionDuration", "transitionDuration", "on", "assign", "args", "len", "eventType", "targetSelector", "listener", "capture", "handleLiveEvent", "e", "target", "eventData", "dom7EventData", "unshift", "is", "apply", "parents", "k", "handleEvent", "undefined", "events", "event$1", "dom7LiveListeners", "proxyListener", "event", "dom7Listeners", "off", "handlers", "handler", "splice", "dom7proxy", "trigger", "evt", "detail", "bubbles", "cancelable", "filter", "dataIndex", "dispatchEvent", "transitionEnd", "callback", "dom", "fireCallBack", "call", "outerWidth", "<PERSON><PERSON><PERSON><PERSON>", "styles", "offsetWidth", "parseFloat", "outerHeight", "offsetHeight", "offset", "box", "getBoundingClientRect", "clientTop", "clientLeft", "scrollTop", "scrollY", "scrollLeft", "scrollX", "top", "left", "css", "props", "prop", "each", "text", "textContent", "compareWith", "matches", "webkitMatchesSelector", "msMatchesSelector", "index", "child", "previousSibling", "eq", "returnIndex", "append", "<PERSON><PERSON><PERSON><PERSON>", "tempDiv", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "prepend", "insertBefore", "next", "nextElement<PERSON><PERSON>ling", "nextAll", "nextEls", "prev", "previousElementSibling", "prevAll", "prevEls", "parent", "parentNode", "closest", "find", "foundElements", "found", "<PERSON><PERSON><PERSON><PERSON>", "toAdd", "Object", "keys", "for<PERSON>ach", "methodName", "testDiv", "ua", "Utils", "deleteProps", "obj", "object", "nextTick", "delay", "now", "getTranslate", "axis", "matrix", "curTransform", "transformMatrix", "curStyle", "WebKitCSSMatrix", "map", "a", "replace", "join", "MozTransform", "OTransform", "MsTransform", "msTransform", "toString", "m41", "m42", "parse<PERSON><PERSON><PERSON><PERSON><PERSON>", "url", "params", "param", "query", "urlToParse", "href", "paramsPart", "decodeURIComponent", "isObject", "o", "constructor", "extend", "len$1", "to", "nextSource", "keysArray", "nextIndex", "<PERSON><PERSON><PERSON>", "desc", "getOwnPropertyDescriptor", "enumerable", "Support", "touch", "Modernizr", "maxTouchPoints", "DocumentTouch", "pointerEvents", "pointer<PERSON><PERSON>bled", "PointerEvent", "prefixedPointerEvents", "msPointer<PERSON><PERSON><PERSON>", "transforms3d", "csstransforms3d", "flexbox", "observer", "passiveListener", "supportsPassive", "opts", "defineProperty", "get", "gestures", "Browser", "isIE", "isEdge", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "isUiWebView", "test", "SwiperClass", "eventsListeners", "eventName", "staticAccessors", "components", "configurable", "priority", "method", "once", "once<PERSON><PERSON><PERSON>", "f7proxy", "<PERSON><PERSON><PERSON><PERSON>", "emit", "Array", "isArray", "slice", "useModulesParams", "instanceParams", "instance", "modules", "moduleName", "useModules", "modulesParams", "moduleParams", "modulePropName", "moduleProp", "bind", "moduleEventName", "create", "set", "use", "installModule", "name", "proto", "static", "install", "m", "concat", "defineProperties", "update", "updateSize", "width", "height", "swiper", "$el", "clientWidth", "clientHeight", "isHorizontal", "isVertical", "parseInt", "size", "updateSlides", "$wrapperEl", "swiperSize", "rtl", "rtlTranslate", "wrongRTL", "isVirtual", "virtual", "enabled", "previousSlidesLength", "slides", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "slidesOffsetBefore", "offsetAfter", "slidesOffsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "spaceBetween", "slidePosition", "prevSlideSize", "slidesNumberEvenToRows", "slideSize", "virtualSize", "marginLeft", "marginTop", "marginRight", "marginBottom", "slidesPerColumn", "Math", "floor", "ceil", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerColumnFill", "max", "newSlidesGrid", "slidesPerRow", "numFullColumns", "slide", "newSlideOrderIndex", "column", "row", "-webkit-box-ordinal-group", "-moz-box-ordinal-group", "-ms-flex-order", "-webkit-order", "order", "slideStyles", "currentTransform", "currentWebKitTransform", "roundLengths", "paddingLeft", "paddingRight", "boxSizing", "paddingTop", "paddingBottom", "boxSizing$1", "swiperSlideSize", "centeredSlides", "abs", "slidesPerGroup", "effect", "setWrapperSize", "i$1", "slidesGridItem", "i$2", "slidesGridItem$1", "centerInsufficientSlides", "allSlidesSize", "slideSizeValue", "allSlidesOffset", "snap", "snapIndex", "watchOverflow", "checkOverflow", "watchSlidesProgress", "watchSlidesVisibility", "updateSlidesOffset", "updateAutoHeight", "speed", "activeSlides", "newHeight", "setTransition", "activeIndex", "swiperSlideOffset", "offsetLeft", "offsetTop", "updateSlidesProgress", "translate", "offsetCenter", "slideVisibleClass", "visibleSlidesIndexes", "visibleSlides", "slideProgress", "minTranslate", "slideBefore", "slideAfter", "progress", "updateProgress", "translatesDiff", "maxTranslate", "isBeginning", "isEnd", "wasBeginning", "wasEnd", "updateSlidesClasses", "activeSlide", "realIndex", "slideActiveClass", "loop", "slideDuplicateClass", "slideDuplicateActiveClass", "nextSlide", "slideNextClass", "prevSlide", "slidePrevClass", "slideDuplicateNextClass", "slideDuplicatePrevClass", "updateActiveIndex", "newActiveIndex", "previousIndex", "previousRealIndex", "previousSnapIndex", "normalizeSlideIndex", "updateClickedSlide", "slideFound", "clickedSlide", "clickedIndex", "slideToClickedSlide", "virtualTranslate", "currentTranslate", "setTranslate", "byController", "x", "y", "previousTranslate", "transition$1", "transitionStart", "runCallbacks", "direction", "autoHeight", "dir", "animating", "slideTo", "internal", "slideIndex", "preventInteractionOnTransition", "initialSlide", "initialized", "allowSlideNext", "allowSlidePrev", "onSlideToWrapperTransitionEnd", "destroyed", "slideToLoop", "newIndex", "loopedSlides", "slideNext", "loopFix", "_clientLeft", "slidePrev", "normalize", "val", "prevIndex", "normalizedTranslate", "normalizedSnapGrid", "prevSnap", "slideReset", "slideToClosest", "currentSnap", "slidesPerViewDynamic", "slideToIndex", "loopCreate", "loopFillGroupWithBlank", "blankSlidesNum", "blankNode", "loopAdditionalSlides", "prependSlides", "appendSlides", "cloneNode", "diff", "loop<PERSON><PERSON><PERSON>", "grabCursor", "setGrabCursor", "moving", "simulate<PERSON>ouch", "isLocked", "cursor", "unsetGrabCursor", "manipulation", "appendSlide", "prependSlide", "addSlide", "activeIndexBuffer", "baseLength", "slidesBuffer", "currentSlide", "removeSlide", "slidesIndexes", "indexToRemove", "removeAllSlides", "<PERSON><PERSON>", "device", "ios", "android", "androidChrome", "desktop", "windows", "iphone", "ipod", "ipad", "<PERSON><PERSON>", "phonegap", "os", "osVersion", "webView", "osVersionArr", "metaViewport", "minimalUi", "pixelRatio", "devicePixelRatio", "onResize", "breakpoints", "setBreakpoint", "freeMode", "newTranslate", "min", "defaults", "init", "touchEventsTarget", "edgeSwipeDetection", "edgeSwipeThreshold", "freeModeMomentum", "freeModeMomentumRatio", "freeModeMomentumBounce", "freeModeMomentumBounceRatio", "freeModeMomentumVelocityRatio", "freeModeSticky", "freeModeMinimumVelocity", "breakpointsInverse", "touchRatio", "touchAngle", "shortSwipes", "longSwipes", "longSwipesRatio", "longSwipesMs", "follow<PERSON><PERSON>", "allowTouchMove", "threshold", "touchMoveStopPropagation", "touchStartPreventDefault", "touchStartForcePreventDefault", "touchReleaseOnEdges", "uniqueNavElements", "resistance", "resistanceRatio", "preventClicks", "preventClicksPropagation", "preloadImages", "updateOnImagesReady", "swi<PERSON><PERSON><PERSON><PERSON>", "noSwiping", "noSwipingClass", "noSwipingSelector", "passiveListeners", "containerModifierClass", "slideClass", "slideBlankClass", "wrapperClass", "runCallbacksOnInit", "prototypes", "attachEvents", "touchEvents", "wrapperEl", "onTouchStart", "touchEventsData", "touches", "originalEvent", "isTouchEvent", "type", "which", "button", "isTouched", "isMoved", "allowClick", "currentX", "targetTouches", "pageX", "currentY", "pageY", "startX", "startY", "iOSEdgeSwipeDetection", "iOSEdgeSwipeThreshold", "allowTouchCallbacks", "isScrolling", "startMoving", "touchStartTime", "swipeDirection", "allowThresholdMove", "preventDefault", "formElements", "shouldPreventDefault", "onTouchMove", "preventedByNestedSwiper", "diffX", "diffY", "sqrt", "pow", "atan2", "PI", "nested", "stopPropagation", "startTranslate", "allowMomentumBounce", "disableParentSwiper", "velocities", "position", "time", "onTouchEnd", "currentPos", "touchEndTime", "timeDiff", "lastClickTime", "clickTimeout", "lastMoveEvent", "pop", "velocityEvent", "distance", "velocity", "momentumDuration", "momentumDistance", "newPosition", "afterBouncePosition", "needsLoopFix", "doBounce", "bounceAmount", "stopIndex", "groupSize", "ratio", "onClick", "stopImmediatePropagation", "start", "passive", "move", "end", "detachEvents", "breakpoint", "getBreakpoint", "currentBreakpoint", "breakpoint<PERSON>nly<PERSON><PERSON><PERSON>", "paramValue", "breakpointP<PERSON>ms", "originalParams", "directionChanged", "needsReLoop", "changeDirection", "points", "point", "sort", "b", "innerWidth", "wasLocked", "navigation", "addClasses", "classNames", "suffixes", "suffix", "removeClasses", "images", "loadImage", "imageEl", "src", "srcset", "sizes", "checkForComplete", "image", "onReady", "complete", "onload", "onerror", "imagesLoaded", "imagesToLoad", "currentSrc", "extendedDefaults", "prototypeGroup", "protoMethod", "moduleParamName", "swiperParams", "passedParams", "swipers", "containerEl", "newParams", "touchEventsTouch", "touchEventsDesktop", "__proto__", "spv", "breakLoop", "translateValue", "newDirection", "needUpdate", "currentDirection", "slideEl", "destroy", "deleteInstance", "cleanStyles", "extendDefaults", "newDefaults", "Device$1", "Support$1", "support", "Browser$1", "browser", "Resize", "resize", "resize<PERSON><PERSON>ler", "orientationChangeHandler", "Observer", "func", "MutationObserver", "WebkitMutationObserver", "attach", "options", "mutations", "observerUpdate", "requestAnimationFrame", "observe", "attributes", "childList", "characterData", "observers", "observeParents", "containerParents", "observeSlideChildren", "disconnect", "Observer$1", "Virtual", "force", "ref", "ref$1", "addSlidesBefore", "addSlidesAfter", "ref$2", "previousFrom", "from", "previousTo", "previousSlidesGrid", "renderSlide", "previousOffset", "offsetProp", "slidesAfter", "slidesBefore", "onRendered", "lazy", "load", "renderExternal", "slidesToRender", "prependIndexes", "appendIndexes", "cache", "$slideEl", "numberOfNewSlides", "newCache", "cachedIndex", "Virtual$1", "beforeInit", "overwriteParams", "Keyboard", "handle", "kc", "keyCode", "charCode", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "metaKey", "keyboard", "onlyInViewport", "inView", "windowWidth", "windowHeight", "innerHeight", "swiperOffset", "swiperCoord", "returnValue", "enable", "disable", "Keyboard$1", "Mousewheel", "lastScrollTime", "isSupported", "element", "implementation", "hasFeature", "isEventSupported", "sX", "sY", "pX", "pY", "wheelDelta", "wheelDeltaY", "wheelDeltaX", "HORIZONTAL_AXIS", "deltaY", "deltaX", "deltaMode", "spinX", "spinY", "pixelX", "pixelY", "handleMouseEnter", "mouseEntered", "handleMouseLeave", "mousewheel", "releaseOnEdges", "delta", "rtlFactor", "forceToAxis", "invert", "sensitivity", "timeout", "autoplay", "autoplayDisableOnInteraction", "stop", "getTime", "eventsTarged", "Navigation", "$nextEl", "$prevEl", "disabledClass", "lockClass", "onPrevClick", "onNextClick", "nextEl", "prevEl", "Pagination", "pagination", "current", "total", "paginationType", "bullets", "firstIndex", "lastIndex", "midIndex", "dynamicBullets", "bulletSize", "dynamicMainBullets", "dynamicBulletIndex", "bullet", "$bullet", "bulletIndex", "bulletActiveClass", "$firstDisplayedBullet", "$lastDisplayedBullet", "dynamicBulletsLength", "bulletsOffset", "formatFractionCurrent", "formatFractionTotal", "progressbarDirection", "progressbarOpposite", "scale", "scaleX", "scaleY", "renderCustom", "render", "paginationHTML", "numberOfBullets", "renderBullet", "bulletClass", "renderFraction", "currentClass", "totalClass", "renderProgressbar", "progressbarFillClass", "clickable", "clickableClass", "modifierClass", "progressbarOppositeClass", "hiddenClass", "Sc<PERSON><PERSON>", "scrollbar", "dragSize", "trackSize", "$dragEl", "newSize", "newPos", "hide", "opacity", "divider", "moveDivider", "display", "setDragPosition", "positionRatio", "clientX", "clientY", "onDragStart", "dragTimeout", "onDragMove", "onDragEnd", "snapOnRelease", "enableDraggable", "activeListener", "disableDraggable", "$swiperEl", "dragEl", "draggable", "Parallax", "setTransform", "p", "currentOpacity", "currentScale", "parallax", "parallaxEl", "$parallaxEl", "parallaxDuration", "Zoom", "getDistanceBetweenTouches", "x1", "y1", "x2", "y2", "onGestureStart", "zoom", "gesture", "fakeGestureTouched", "fakeGestureMoved", "scaleStart", "$imageEl", "$imageWrapEl", "maxRatio", "isScaling", "onGestureChange", "scaleMove", "minRatio", "onGestureEnd", "changedTouches", "touchesStart", "slideWidth", "slideHeight", "scaledWidth", "scaledHeight", "minX", "maxX", "minY", "maxY", "touchesCurrent", "prevPositionX", "prevPositionY", "prevTime", "momentumDurationX", "momentumDurationY", "momentumDistanceX", "newPositionX", "momentumDistanceY", "newPositionY", "onTransitionEnd", "out", "in", "touchX", "touchY", "translateX", "translateY", "imageWidth", "imageHeight", "translateMinX", "translateMinY", "translateMaxX", "translateMaxY", "Lazy", "loadInSlide", "loadInDuplicate", "$images", "elementClass", "loadedClass", "loadingClass", "imageIndex", "background", "slideOriginalIndex", "originalSlide", "duplicatedSlide", "slideExist", "initialImageLoaded", "elIndex", "loadPrevNext", "loadPrevNextAmount", "amount", "maxIndex", "minIndex", "Controller", "LinearSpline", "guess", "i1", "i3", "binarySearch", "array", "interpolate", "getInterpolateFunction", "c", "controller", "spline", "setTranslate$1", "multiplier", "controlledTranslate", "controlled", "control", "setControlledTranslate", "by", "inverse", "setControlledTransition", "a11y", "makeElFocusable", "addElRole", "role", "addElLabel", "label", "disableEl", "enableEl", "onEnterKey", "$targetEl", "notify", "lastSlideMessage", "nextSlideMessage", "firstSlideMessage", "prevSlideMessage", "click", "message", "notification", "liveRegion", "updateNavigation", "updatePagination", "bulletEl", "$bulletEl", "paginationBulletMessage", "History", "pushState", "hashNavigation", "paths", "get<PERSON>ath<PERSON><PERSON><PERSON>", "scrollToSlide", "replaceState", "setHistoryPopState", "pathArray", "pathname", "part", "setHistory", "slugify", "includes", "currentState", "state", "HashNavigation", "onHashCange", "newHash", "setHash", "watchState", "Autoplay", "run", "$activeSlideEl", "reverseDirection", "stopOnLastSlide", "running", "pause", "paused", "waitForTransition", "Fade", "tx", "ty", "slideOpacity", "fadeEffect", "crossFade", "eventTriggered", "triggerEvents", "C<PERSON>", "$cubeShadowEl", "swiper<PERSON><PERSON><PERSON>", "swiperHeight", "cubeEffect", "wrapperRotate", "shadow", "slideAngle", "round", "tz", "slideShadows", "shadowBefore", "shadowAfter", "-webkit-transform-origin", "-moz-transform-origin", "-ms-transform-origin", "transform-origin", "shadowOffset", "shadowAngle", "sin", "cos", "scale1", "shadowScale", "scale2", "zFactor", "Flip", "flipEffect", "limitRotation", "rotateY", "rotateX", "zIndex", "Coverflow", "coverflowEffect", "center", "rotate", "depth", "offsetMultiplier", "modifier", "translateZ", "stretch", "slideTransform", "$shadowBeforeEl", "$shadowAfterEl", "<PERSON><PERSON><PERSON><PERSON>", "Thumbs", "thumbsParams", "thumbs", "swiperCreated", "thumbsContainerClass", "onThumbClick", "thumbsSwiper", "slideThumbActiveClass", "currentIndex", "initial", "newThumbsIndex", "currentThumbsIndex", "prevThumbsIndex", "nextThumbsIndex", "thumbsToActivate", "thumbActiveClass", "hideOnClick", "toEdge", "fromEdge", "isHidden", "bulletElement", "number", "activeIndexChange", "snapIndexChange", "slidesLengthChange", "snapGridLengthChange", "dragClass", "containerClass", "zoomedSlideClass", "touchStart", "touchEnd", "doubleTap", "loadOnTransitionStart", "preloaderClass", "scroll", "scrollbarDragMove", "notificationClass", "paginationUpdate", "disableOnInteraction", "beforeTransitionStart", "slider<PERSON><PERSON><PERSON><PERSON><PERSON>", "slideChange", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;CAYC,SAAUA,EAAQC,GACE,iBAAZC,SAA0C,oBAAXC,OAAyBA,OAAOD,QAAUD,IAC9D,mBAAXG,QAAyBA,OAAOC,IAAMD,OAAOH,IACnDD,EAASA,GAAUM,MAAaC,OAASN,IAH5C,CAIEO,KAAM,WAAc,aAapB,IAAIC,EAA2B,oBAAbC,SAA4B,CAC5CC,KAAM,GACNC,iBAAkB,aAClBC,oBAAqB,aACrBC,cAAe,CACbC,KAAM,aACNC,SAAU,IAEZC,cAAe,WACb,OAAO,MAETC,iBAAkB,WAChB,MAAO,IAETC,eAAgB,WACd,OAAO,MAETC,YAAa,WACX,MAAO,CACLC,UAAW,eAGfC,cAAe,WACb,MAAO,CACLC,SAAU,GACVC,WAAY,GACZC,MAAO,GACPC,aAAc,aACdC,qBAAsB,WACpB,MAAO,MAIbC,SAAU,CAAEC,KAAM,KAChBnB,SAEAoB,EAAyB,oBAAXC,OAA0B,CAC1CrB,SAAUD,EACVuB,UAAW,CACTC,UAAW,IAEbL,SAAU,GACVM,QAAS,GACTC,YAAa,WACX,OAAO3B,MAETI,iBAAkB,aAClBC,oBAAqB,aACrBuB,iBAAkB,WAChB,MAAO,CACLC,iBAAkB,WAChB,MAAO,MAIbC,MAAO,aACPC,KAAM,aACNC,OAAQ,GACRC,WAAY,aACZC,aAAc,cACZX,OAgBAY,EAAO,SAAcC,GAGvB,IAFA,IAESC,EAAI,EAAGA,EAAID,EAAIE,OAAQD,GAAK,EAF1BrC,KAGJqC,GAAKD,EAAIC,GAIhB,OAPWrC,KAKNsC,OAASF,EAAIE,OAEXtC,MAGT,SAASuC,EAAEC,EAAUC,GACnB,IAAIL,EAAM,GACNC,EAAI,EACR,GAAIG,IAAaC,GACXD,aAAoBL,EACtB,OAAOK,EAGX,GAAIA,EAEF,GAAwB,iBAAbA,EAAuB,CAChC,IAAIE,EACAC,EACAC,EAAOJ,EAASK,OACpB,GAAyB,GAArBD,EAAKE,QAAQ,MAAkC,GAArBF,EAAKE,QAAQ,KAAW,CACpD,IAAIC,EAAW,MAQf,IAP4B,IAAxBH,EAAKE,QAAQ,SAAgBC,EAAW,MAChB,IAAxBH,EAAKE,QAAQ,SAAgBC,EAAW,SAChB,IAAxBH,EAAKE,QAAQ,QAAwC,IAAxBF,EAAKE,QAAQ,SAAgBC,EAAW,MAC1C,IAA3BH,EAAKE,QAAQ,YAAmBC,EAAW,SACf,IAA5BH,EAAKE,QAAQ,aAAoBC,EAAW,WAChDJ,EAAa1C,EAAIa,cAAciC,IACpBC,UAAYJ,EAClBP,EAAI,EAAGA,EAAIM,EAAW3B,WAAWsB,OAAQD,GAAK,EACjDD,EAAIa,KAAKN,EAAW3B,WAAWqB,SAUjC,IAFEK,EALGD,GAA2B,MAAhBD,EAAS,IAAeA,EAASU,MAAM,aAK9CT,GAAWxC,GAAKS,iBAAiB8B,EAASK,QAH3C,CAAC5C,EAAIU,eAAe6B,EAASK,OAAOM,MAAM,KAAK,KAKlDd,EAAI,EAAGA,EAAIK,EAAIJ,OAAQD,GAAK,EAC3BK,EAAIL,IAAMD,EAAIa,KAAKP,EAAIL,SAG1B,GAAIG,EAASY,UAAYZ,IAAalB,GAAOkB,IAAavC,EAE/DmC,EAAIa,KAAKT,QACJ,GAAsB,EAAlBA,EAASF,QAAcE,EAAS,GAAGY,SAE5C,IAAKf,EAAI,EAAGA,EAAIG,EAASF,OAAQD,GAAK,EACpCD,EAAIa,KAAKT,EAASH,IAIxB,OAAO,IAAIF,EAAKC,GAOlB,SAASiB,EAAOjB,GAEd,IADA,IAAIkB,EAAc,GACTjB,EAAI,EAAGA,EAAID,EAAIE,OAAQD,GAAK,GACE,IAAjCiB,EAAYR,QAAQV,EAAIC,KAAciB,EAAYL,KAAKb,EAAIC,IAEjE,OAAOiB,EATTf,EAAEgB,GAAKpB,EAAKqB,UACZjB,EAAEkB,MAAQtB,EACVI,EAAEJ,KAAOA,EA8nBT,IAAIuB,EAAU,CACZC,SApnBF,SAAkBC,GAChB,QAAyB,IAAdA,EACT,OAAO5D,KAGT,IADA,IAAI6D,EAAUD,EAAUT,MAAM,KACrBd,EAAI,EAAGA,EAAIwB,EAAQvB,OAAQD,GAAK,EACvC,IAAK,IAAIyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,OACb,IAAZ9D,KAAK8D,SAAmD,IAAtB9D,KAAK8D,GAAGC,WAA6B/D,KAAK8D,GAAGC,UAAUC,IAAIH,EAAQxB,IAGpH,OAAOrC,MA2mBPiE,YAzmBF,SAAqBL,GAEnB,IADA,IAAIC,EAAUD,EAAUT,MAAM,KACrBd,EAAI,EAAGA,EAAIwB,EAAQvB,OAAQD,GAAK,EACvC,IAAK,IAAIyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,OACb,IAAZ9D,KAAK8D,SAAmD,IAAtB9D,KAAK8D,GAAGC,WAA6B/D,KAAK8D,GAAGC,UAAUG,OAAOL,EAAQxB,IAGvH,OAAOrC,MAmmBPmE,SAjmBF,SAAkBP,GAChB,QAAK5D,KAAK,IACHA,KAAK,GAAG+D,UAAUK,SAASR,IAgmBlCS,YA9lBF,SAAqBT,GAEnB,IADA,IAAIC,EAAUD,EAAUT,MAAM,KACrBd,EAAI,EAAGA,EAAIwB,EAAQvB,OAAQD,GAAK,EACvC,IAAK,IAAIyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,OACb,IAAZ9D,KAAK8D,SAAmD,IAAtB9D,KAAK8D,GAAGC,WAA6B/D,KAAK8D,GAAGC,UAAUO,OAAOT,EAAQxB,IAGvH,OAAOrC,MAwlBPuE,KAtlBF,SAAcC,EAAOC,GACnB,IAAIC,EAAcC,UAElB,GAAyB,IAArBA,UAAUrC,QAAiC,iBAAVkC,EAEnC,OAAIxE,KAAK,GAAaA,KAAK,GAAG4E,aAAaJ,QAC3C,EAIF,IAAK,IAAInC,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EACpC,GAA2B,IAAvBqC,EAAYpC,OAEdtC,KAAKqC,GAAGnB,aAAasD,EAAOC,QAI5B,IAAK,IAAII,KAAYL,EACnBxE,KAAKqC,GAAGwC,GAAYL,EAAMK,GAC1B7E,KAAKqC,GAAGnB,aAAa2D,EAAUL,EAAMK,IAI3C,OAAO7E,MAgkBP8E,WA7jBF,SAAoBP,GAClB,IAAK,IAAIlC,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EACpCrC,KAAKqC,GAAG0C,gBAAgBR,GAE1B,OAAOvE,MA0jBPgF,KAxjBF,SAAcC,EAAKR,GACjB,IAAIS,EACJ,QAAqB,IAAVT,EAAX,CAkBA,IAAK,IAAIpC,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,GACpC6C,EAAKlF,KAAKqC,IACF8C,yBAA0BD,EAAGC,uBAAyB,IAC9DD,EAAGC,uBAAuBF,GAAOR,EAEnC,OAAOzE,KApBL,GAFAkF,EAAKlF,KAAK,GAEF,CACN,GAAIkF,EAAGC,wBAA2BF,KAAOC,EAAGC,uBAC1C,OAAOD,EAAGC,uBAAuBF,GAGnC,IAAIG,EAAUF,EAAGN,aAAc,QAAUK,GACzC,OAAIG,QAGJ,IA2iBJC,UA5hBF,SAAmBA,GACjB,IAAK,IAAIhD,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAAG,CACvC,IAAIiD,EAAUtF,KAAKqC,GAAGpB,MACtBqE,EAAQC,gBAAkBF,EAC1BC,EAAQD,UAAYA,EAEtB,OAAOrF,MAuhBPwF,WArhBF,SAAoBC,GACM,iBAAbA,IACTA,GAAsB,MAExB,IAAK,IAAIpD,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAAG,CACvC,IAAIiD,EAAUtF,KAAKqC,GAAGpB,MACtBqE,EAAQI,yBAA2BD,EACnCH,EAAQK,mBAAqBF,EAE/B,OAAOzF,MA6gBP4F,GA1gBF,WAIE,IAHA,IAAIC,EAEAC,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GACzC,IAAIC,EAAYF,EAAK,GACjBG,EAAiBH,EAAK,GACtBI,EAAWJ,EAAK,GAChBK,EAAUL,EAAK,GAOnB,SAASM,EAAgBC,GACvB,IAAIC,EAASD,EAAEC,OACf,GAAKA,EAAL,CACA,IAAIC,EAAYF,EAAEC,OAAOE,eAAiB,GAI1C,GAHID,EAAUzD,QAAQuD,GAAK,GACzBE,EAAUE,QAAQJ,GAEhB9D,EAAE+D,GAAQI,GAAGT,GAAmBC,EAASS,MAAML,EAAQC,QAGzD,IADA,IAAIK,EAAUrE,EAAE+D,GAAQM,UACfC,EAAI,EAAGA,EAAID,EAAQtE,OAAQuE,GAAK,EACnCtE,EAAEqE,EAAQC,IAAIH,GAAGT,IAAmBC,EAASS,MAAMC,EAAQC,GAAIN,IAIzE,SAASO,EAAYT,GACnB,IAAIE,EAAYF,GAAKA,EAAEC,QAASD,EAAEC,OAAOE,eAAsB,GAC3DD,EAAUzD,QAAQuD,GAAK,GACzBE,EAAUE,QAAQJ,GAEpBH,EAASS,MAAM3G,KAAMuG,GA1BA,mBAAZT,EAAK,KACEE,GAAfH,EAASC,GAAyB,GAAII,EAAWL,EAAO,GAAIM,EAAUN,EAAO,GAC9EI,OAAiBc,GAEdZ,IAAWA,GAAU,GA0B1B,IAFA,IACIrC,EADAkD,EAAShB,EAAU7C,MAAM,KAEpBd,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAAG,CACvC,IAAI6C,EAAKlF,KAAKqC,GACd,GAAK4D,EAaH,IAAKnC,EAAI,EAAGA,EAAIkD,EAAO1E,OAAQwB,GAAK,EAAG,CACrC,IAAImD,EAAUD,EAAOlD,GAChBoB,EAAGgC,oBAAqBhC,EAAGgC,kBAAoB,IAC/ChC,EAAGgC,kBAAkBD,KAAY/B,EAAGgC,kBAAkBD,GAAW,IACtE/B,EAAGgC,kBAAkBD,GAAShE,KAAK,CACjCiD,SAAUA,EACViB,cAAef,IAEjBlB,EAAG9E,iBAAiB6G,EAASb,EAAiBD,QApBhD,IAAKrC,EAAI,EAAGA,EAAIkD,EAAO1E,OAAQwB,GAAK,EAAG,CACrC,IAAIsD,EAAQJ,EAAOlD,GACdoB,EAAGmC,gBAAiBnC,EAAGmC,cAAgB,IACvCnC,EAAGmC,cAAcD,KAAUlC,EAAGmC,cAAcD,GAAS,IAC1DlC,EAAGmC,cAAcD,GAAOnE,KAAK,CAC3BiD,SAAUA,EACViB,cAAeL,IAEjB5B,EAAG9E,iBAAiBgH,EAAON,EAAaX,IAgB9C,OAAOnG,MAycPsH,IAvcF,WAIE,IAHA,IAAIzB,EAEAC,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GACzC,IAAIC,EAAYF,EAAK,GACjBG,EAAiBH,EAAK,GACtBI,EAAWJ,EAAK,GAChBK,EAAUL,EAAK,GACI,mBAAZA,EAAK,KACEE,GAAfH,EAASC,GAAyB,GAAII,EAAWL,EAAO,GAAIM,EAAUN,EAAO,GAC9EI,OAAiBc,GAEdZ,IAAWA,GAAU,GAG1B,IADA,IAAIa,EAAShB,EAAU7C,MAAM,KACpBd,EAAI,EAAGA,EAAI2E,EAAO1E,OAAQD,GAAK,EAEtC,IADA,IAAI+E,EAAQJ,EAAO3E,GACVyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,EAAG,CACvC,IAAIoB,EAAKlF,KAAK8D,GACVyD,OAAW,EAMf,IALKtB,GAAkBf,EAAGmC,cACxBE,EAAWrC,EAAGmC,cAAcD,GACnBnB,GAAkBf,EAAGgC,oBAC9BK,EAAWrC,EAAGgC,kBAAkBE,IAE9BG,GAAYA,EAASjF,OACvB,IAAK,IAAIuE,EAAIU,EAASjF,OAAS,EAAQ,GAALuE,EAAQA,GAAK,EAAG,CAChD,IAAIW,EAAUD,EAASV,GACnBX,GAAYsB,EAAQtB,WAAaA,GACnChB,EAAG7E,oBAAoB+G,EAAOI,EAAQL,cAAehB,GACrDoB,EAASE,OAAOZ,EAAG,IACVX,GAAYsB,EAAQtB,UAAYsB,EAAQtB,SAASwB,WAAaF,EAAQtB,SAASwB,YAAcxB,GACtGhB,EAAG7E,oBAAoB+G,EAAOI,EAAQL,cAAehB,GACrDoB,EAASE,OAAOZ,EAAG,IACTX,IACVhB,EAAG7E,oBAAoB+G,EAAOI,EAAQL,cAAehB,GACrDoB,EAASE,OAAOZ,EAAG,KAM7B,OAAO7G,MA6ZP2H,QA3ZF,WAEE,IADA,IAAI7B,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAIzC,IAFA,IAAIiB,EAASlB,EAAK,GAAG3C,MAAM,KACvBoD,EAAYT,EAAK,GACZzD,EAAI,EAAGA,EAAI2E,EAAO1E,OAAQD,GAAK,EAEtC,IADA,IAAI+E,EAAQJ,EAAO3E,GACVyB,EAAI,EAAGA,EAAI9D,KAAKsC,OAAQwB,GAAK,EAAG,CACvC,IAAIoB,EAAKlF,KAAK8D,GACV8D,OAAM,EACV,IACEA,EAAM,IAAItG,EAAIK,YAAYyF,EAAO,CAC/BS,OAAQtB,EACRuB,SAAS,EACTC,YAAY,IAEd,MAAO1B,IACPuB,EAAM3H,EAAIW,YAAY,UAClBC,UAAUuG,GAAO,GAAM,GAC3BQ,EAAIC,OAAStB,EAGfrB,EAAGsB,cAAgBV,EAAKkC,OAAO,SAAUhD,EAAMiD,GAAa,OAAmB,EAAZA,IACnE/C,EAAGgD,cAAcN,GACjB1C,EAAGsB,cAAgB,UACZtB,EAAGsB,cAGd,OAAOxG,MA+XPmI,cA7XF,SAAuBC,GACrB,IAEI/F,EAFA2E,EAAS,CAAC,sBAAuB,iBACjCqB,EAAMrI,KAEV,SAASsI,EAAajC,GAEpB,GAAIA,EAAEC,SAAWtG,KAEjB,IADAoI,EAASG,KAAKvI,KAAMqG,GACfhE,EAAI,EAAGA,EAAI2E,EAAO1E,OAAQD,GAAK,EAClCgG,EAAIf,IAAIN,EAAO3E,GAAIiG,GAGvB,GAAIF,EACF,IAAK/F,EAAI,EAAGA,EAAI2E,EAAO1E,OAAQD,GAAK,EAClCgG,EAAIzC,GAAGoB,EAAO3E,GAAIiG,GAGtB,OAAOtI,MA6WPwI,WA3WF,SAAoBC,GAClB,GAAkB,EAAdzI,KAAKsC,OAAY,CACnB,GAAImG,EAAgB,CAElB,IAAIC,EAAS1I,KAAK0I,SAClB,OAAO1I,KAAK,GAAG2I,YAAcC,WAAWF,EAAO7G,iBAAiB,iBAAmB+G,WAAWF,EAAO7G,iBAAiB,gBAExH,OAAO7B,KAAK,GAAG2I,YAEjB,OAAO,MAmWPE,YAjWF,SAAqBJ,GACnB,GAAkB,EAAdzI,KAAKsC,OAAY,CACnB,GAAImG,EAAgB,CAElB,IAAIC,EAAS1I,KAAK0I,SAClB,OAAO1I,KAAK,GAAG8I,aAAeF,WAAWF,EAAO7G,iBAAiB,eAAiB+G,WAAWF,EAAO7G,iBAAiB,kBAEvH,OAAO7B,KAAK,GAAG8I,aAEjB,OAAO,MAyVPC,OAvVF,WACE,GAAkB,EAAd/I,KAAKsC,OAAY,CACnB,IAAI4C,EAAKlF,KAAK,GACVgJ,EAAM9D,EAAG+D,wBACT9I,EAAOF,EAAIE,KACX+I,EAAYhE,EAAGgE,WAAa/I,EAAK+I,WAAa,EAC9CC,EAAajE,EAAGiE,YAAchJ,EAAKgJ,YAAc,EACjDC,EAAYlE,IAAO5D,EAAMA,EAAI+H,QAAUnE,EAAGkE,UAC1CE,EAAapE,IAAO5D,EAAMA,EAAIiI,QAAUrE,EAAGoE,WAC/C,MAAO,CACLE,IAAMR,EAAIQ,IAAMJ,EAAaF,EAC7BO,KAAOT,EAAIS,KAAOH,EAAcH,GAIpC,OAAO,MAyUPO,IAnUF,SAAaC,EAAOlF,GAClB,IAAIpC,EACJ,GAAyB,IAArBsC,UAAUrC,OAAc,CAC1B,GAAqB,iBAAVqH,EAEJ,CACL,IAAKtH,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAEhC,IAAK,IAAIuH,KAAQD,EACf3J,KAAKqC,GAAGpB,MAAM2I,GAAQD,EAAMC,GAGhC,OAAO5J,KARP,GAAIA,KAAK,GAAM,OAAOsB,EAAIM,iBAAiB5B,KAAK,GAAI,MAAM6B,iBAAiB8H,GAW/E,GAAyB,IAArBhF,UAAUrC,QAAiC,iBAAVqH,EAAoB,CACvD,IAAKtH,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAChCrC,KAAKqC,GAAGpB,MAAM0I,GAASlF,EAEzB,OAAOzE,KAET,OAAOA,MA+SP6J,KA5SF,SAAczB,GAEZ,IAAKA,EAAY,OAAOpI,KAExB,IAAK,IAAIqC,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAEpC,IAA2C,IAAvC+F,EAASG,KAAKvI,KAAKqC,GAAIA,EAAGrC,KAAKqC,IAEjC,OAAOrC,KAIX,OAAOA,MAiSP4C,KA9RF,SAAcA,GACZ,QAAoB,IAATA,EACT,OAAO5C,KAAK,GAAKA,KAAK,GAAGgD,eAAY+D,EAGvC,IAAK,IAAI1E,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EACpCrC,KAAKqC,GAAGW,UAAYJ,EAEtB,OAAO5C,MAuRP8J,KApRF,SAAcA,GACZ,QAAoB,IAATA,EACT,OAAI9J,KAAK,GACAA,KAAK,GAAG+J,YAAYlH,OAEtB,KAGT,IAAK,IAAIR,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EACpCrC,KAAKqC,GAAG0H,YAAcD,EAExB,OAAO9J,MA0QP0G,GAxQF,SAAYlE,GACV,IACIwH,EACA3H,EAFA6C,EAAKlF,KAAK,GAGd,IAAKkF,QAA0B,IAAb1C,EAA4B,OAAO,EACrD,GAAwB,iBAAbA,EAAuB,CAChC,GAAI0C,EAAG+E,QAAW,OAAO/E,EAAG+E,QAAQzH,GAC/B,GAAI0C,EAAGgF,sBAAyB,OAAOhF,EAAGgF,sBAAsB1H,GAChE,GAAI0C,EAAGiF,kBAAqB,OAAOjF,EAAGiF,kBAAkB3H,GAG7D,IADAwH,EAAczH,EAAEC,GACXH,EAAI,EAAGA,EAAI2H,EAAY1H,OAAQD,GAAK,EACvC,GAAI2H,EAAY3H,KAAO6C,EAAM,OAAO,EAEtC,OAAO,EACF,GAAI1C,IAAavC,EAAO,OAAOiF,IAAOjF,EACxC,GAAIuC,IAAalB,EAAO,OAAO4D,IAAO5D,EAE3C,GAAIkB,EAASY,UAAYZ,aAAoBL,EAAM,CAEjD,IADA6H,EAAcxH,EAASY,SAAW,CAACZ,GAAYA,EAC1CH,EAAI,EAAGA,EAAI2H,EAAY1H,OAAQD,GAAK,EACvC,GAAI2H,EAAY3H,KAAO6C,EAAM,OAAO,EAEtC,OAAO,EAET,OAAO,GAgPPkF,MA9OF,WACE,IACI/H,EADAgI,EAAQrK,KAAK,GAEjB,GAAIqK,EAAO,CAGT,IAFAhI,EAAI,EAEuC,QAAnCgI,EAAQA,EAAMC,kBACG,IAAnBD,EAAMjH,WAAkBf,GAAK,GAEnC,OAAOA,IAsOTkI,GAjOF,SAAYH,GACV,QAAqB,IAAVA,EAAyB,OAAOpK,KAC3C,IACIwK,EADAlI,EAAStC,KAAKsC,OAElB,OACS,IAAIH,EADDG,EAAS,EAAjB8H,EACc,GAEdA,EAAQ,GACVI,EAAclI,EAAS8H,GACL,EAAqB,GACvB,CAACpK,KAAKwK,IAER,CAACxK,KAAKoK,MAsNtBK,OApNF,WAEE,IADA,IAGIC,EAHA5E,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAIzC,IAAK,IAAIc,EAAI,EAAGA,EAAIf,EAAKxD,OAAQuE,GAAK,EAAG,CACvC6D,EAAW5E,EAAKe,GAChB,IAAK,IAAIxE,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EACpC,GAAwB,iBAAbqI,EAAuB,CAChC,IAAIC,EAAU1K,EAAIa,cAAc,OAEhC,IADA6J,EAAQ3H,UAAY0H,EACbC,EAAQC,YACb5K,KAAKqC,GAAGwI,YAAYF,EAAQC,iBAEzB,GAAIF,aAAoBvI,EAC7B,IAAK,IAAI2B,EAAI,EAAGA,EAAI4G,EAASpI,OAAQwB,GAAK,EACxC9D,KAAKqC,GAAGwI,YAAYH,EAAS5G,SAG/B9D,KAAKqC,GAAGwI,YAAYH,GAK1B,OAAO1K,MA4LP8K,QA1LF,SAAiBJ,GACf,IAAIrI,EACAyB,EACJ,IAAKzB,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAChC,GAAwB,iBAAbqI,EAAuB,CAChC,IAAIC,EAAU1K,EAAIa,cAAc,OAEhC,IADA6J,EAAQ3H,UAAY0H,EACf5G,EAAI6G,EAAQ3J,WAAWsB,OAAS,EAAQ,GAALwB,EAAQA,GAAK,EACnD9D,KAAKqC,GAAG0I,aAAaJ,EAAQ3J,WAAW8C,GAAI9D,KAAKqC,GAAGrB,WAAW,SAE5D,GAAI0J,aAAoBvI,EAC7B,IAAK2B,EAAI,EAAGA,EAAI4G,EAASpI,OAAQwB,GAAK,EACpC9D,KAAKqC,GAAG0I,aAAaL,EAAS5G,GAAI9D,KAAKqC,GAAGrB,WAAW,SAGvDhB,KAAKqC,GAAG0I,aAAaL,EAAU1K,KAAKqC,GAAGrB,WAAW,IAGtD,OAAOhB,MAyKPgL,KAvKF,SAAcxI,GACZ,OAAkB,EAAdxC,KAAKsC,OACHE,EACExC,KAAK,GAAGiL,oBAAsB1I,EAAEvC,KAAK,GAAGiL,oBAAoBvE,GAAGlE,GAC1D,IAAIL,EAAK,CAACnC,KAAK,GAAGiL,qBAEpB,IAAI9I,EAAK,IAGdnC,KAAK,GAAGiL,mBAA6B,IAAI9I,EAAK,CAACnC,KAAK,GAAGiL,qBACpD,IAAI9I,EAAK,IAEX,IAAIA,EAAK,KA4JhB+I,QA1JF,SAAiB1I,GACf,IAAI2I,EAAU,GACVjG,EAAKlF,KAAK,GACd,IAAKkF,EAAM,OAAO,IAAI/C,EAAK,IAC3B,KAAO+C,EAAG+F,oBAAoB,CAC5B,IAAID,EAAO9F,EAAG+F,mBACVzI,EACED,EAAEyI,GAAMtE,GAAGlE,IAAa2I,EAAQlI,KAAK+H,GAClCG,EAAQlI,KAAK+H,GACtB9F,EAAK8F,EAEP,OAAO,IAAI7I,EAAKgJ,IAgJhBC,KA9IF,SAAc5I,GACZ,GAAkB,EAAdxC,KAAKsC,OAAY,CACnB,IAAI4C,EAAKlF,KAAK,GACd,OAAIwC,EACE0C,EAAGmG,wBAA0B9I,EAAE2C,EAAGmG,wBAAwB3E,GAAGlE,GACxD,IAAIL,EAAK,CAAC+C,EAAGmG,yBAEf,IAAIlJ,EAAK,IAGd+C,EAAGmG,uBAAiC,IAAIlJ,EAAK,CAAC+C,EAAGmG,yBAC9C,IAAIlJ,EAAK,IAElB,OAAO,IAAIA,EAAK,KAkIhBmJ,QAhIF,SAAiB9I,GACf,IAAI+I,EAAU,GACVrG,EAAKlF,KAAK,GACd,IAAKkF,EAAM,OAAO,IAAI/C,EAAK,IAC3B,KAAO+C,EAAGmG,wBAAwB,CAChC,IAAID,EAAOlG,EAAGmG,uBACV7I,EACED,EAAE6I,GAAM1E,GAAGlE,IAAa+I,EAAQtI,KAAKmI,GAClCG,EAAQtI,KAAKmI,GACtBlG,EAAKkG,EAEP,OAAO,IAAIjJ,EAAKoJ,IAsHhBC,OApHF,SAAgBhJ,GAEd,IADA,IAAIoE,EAAU,GACLvE,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EACT,OAAvBrC,KAAKqC,GAAGoJ,aACNjJ,EACED,EAAEvC,KAAKqC,GAAGoJ,YAAY/E,GAAGlE,IAAaoE,EAAQ3D,KAAKjD,KAAKqC,GAAGoJ,YAE/D7E,EAAQ3D,KAAKjD,KAAKqC,GAAGoJ,aAI3B,OAAOlJ,EAAEc,EAAOuD,KA0GhBA,QAxGF,SAAiBpE,GAEf,IADA,IAAIoE,EAAU,GACLvE,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAEpC,IADA,IAAImJ,EAASxL,KAAKqC,GAAGoJ,WACdD,GACDhJ,EACED,EAAEiJ,GAAQ9E,GAAGlE,IAAaoE,EAAQ3D,KAAKuI,GAE3C5E,EAAQ3D,KAAKuI,GAEfA,EAASA,EAAOC,WAGpB,OAAOlJ,EAAEc,EAAOuD,KA4FhB8E,QA1FF,SAAiBlJ,GACf,IAAIkJ,EAAU1L,KACd,YAAwB,IAAbwC,EACF,IAAIL,EAAK,KAEbuJ,EAAQhF,GAAGlE,KACdkJ,EAAUA,EAAQ9E,QAAQpE,GAAU+H,GAAG,IAElCmB,IAmFPC,KAjFF,SAAcnJ,GAEZ,IADA,IAAIoJ,EAAgB,GACXvJ,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAEpC,IADA,IAAIwJ,EAAQ7L,KAAKqC,GAAG3B,iBAAiB8B,GAC5BsB,EAAI,EAAGA,EAAI+H,EAAMvJ,OAAQwB,GAAK,EACrC8H,EAAc3I,KAAK4I,EAAM/H,IAG7B,OAAO,IAAI3B,EAAKyJ,IA0EhB7K,SAxEF,SAAkByB,GAEhB,IADA,IAAIzB,EAAW,GACNsB,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAGpC,IAFA,IAAIrB,EAAahB,KAAKqC,GAAGrB,WAEhB8C,EAAI,EAAGA,EAAI9C,EAAWsB,OAAQwB,GAAK,EACrCtB,EAEiC,IAA3BxB,EAAW8C,GAAGV,UAAkBb,EAAEvB,EAAW8C,IAAI4C,GAAGlE,IAC7DzB,EAASkC,KAAKjC,EAAW8C,IAFM,IAA3B9C,EAAW8C,GAAGV,UAAkBrC,EAASkC,KAAKjC,EAAW8C,IAMnE,OAAO,IAAI3B,EAAKkB,EAAOtC,KA4DvBmD,OA1DF,WACE,IAAK,IAAI7B,EAAI,EAAGA,EAAIrC,KAAKsC,OAAQD,GAAK,EAChCrC,KAAKqC,GAAGoJ,YAAczL,KAAKqC,GAAGoJ,WAAWK,YAAY9L,KAAKqC,IAEhE,OAAOrC,MAuDPgE,IArDF,WAEE,IADA,IAAI8B,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAEzC,IACI1D,EACAyB,EACJ,IAAKzB,EAAI,EAAGA,EAAIyD,EAAKxD,OAAQD,GAAK,EAAG,CACnC,IAAI0J,EAAQxJ,EAAEuD,EAAKzD,IACnB,IAAKyB,EAAI,EAAGA,EAAIiI,EAAMzJ,OAAQwB,GAAK,EAL3B9D,KAAAA,KAMEsC,QAAUyJ,EAAMjI,GANlB9D,KAOFsC,QAAU,EAGlB,OAVUtC,MAkDV0I,OA3VF,WACE,OAAI1I,KAAK,GAAasB,EAAIM,iBAAiB5B,KAAK,GAAI,MAC7C,KA4VTgM,OAAOC,KAAKvI,GAASwI,QAAQ,SAAUC,GACrC5J,EAAEgB,GAAG4I,GAAczI,EAAQyI,KAG7B,IAkIUlL,EAJAA,EAVJmL,EAuDEC,EA3KJC,GAAQ,CACVC,YAAa,SAAqBC,GAChC,IAAIC,EAASD,EACbR,OAAOC,KAAKQ,GAAQP,QAAQ,SAAUjH,GACpC,IACEwH,EAAOxH,GAAO,KACd,MAAOoB,IAGT,WACSoG,EAAOxH,GACd,MAAOoB,QAKbqG,SAAU,SAAkBtE,EAAUuE,GAGpC,YAFe,IAAVA,IAAmBA,EAAQ,GAEzB1K,WAAWmG,EAAUuE,IAE9BC,IAAK,WACH,OAAO7K,KAAK6K,OAEdC,aAAc,SAAsB3H,EAAI4H,GAGtC,IAAIC,EACAC,EACAC,OAJU,IAATH,IAAkBA,EAAO,KAM9B,IAAII,EAAW5L,EAAIM,iBAAiBsD,EAAI,MA+BxC,OA7BI5D,EAAI6L,iBAE+B,GADrCH,EAAeE,EAAS7H,WAAa6H,EAAS3H,iBAC7BpC,MAAM,KAAKb,SAC1B0K,EAAeA,EAAa7J,MAAM,MAAMiK,IAAI,SAAUC,GAAK,OAAOA,EAAEC,QAAQ,IAAK,OAASC,KAAK,OAIjGN,EAAkB,IAAI3L,EAAI6L,gBAAiC,SAAjBH,EAA0B,GAAKA,IAGzED,GADAE,EAAkBC,EAASM,cAAgBN,EAASO,YAAcP,EAASQ,aAAeR,EAASS,aAAeT,EAAS7H,WAAa6H,EAASrL,iBAAiB,aAAayL,QAAQ,aAAc,uBAC5KM,WAAWzK,MAAM,KAG/B,MAAT2J,IAEyBE,EAAvB1L,EAAI6L,gBAAkCF,EAAgBY,IAE/B,KAAlBd,EAAOzK,OAAgCsG,WAAWmE,EAAO,KAE5CnE,WAAWmE,EAAO,KAE7B,MAATD,IAEyBE,EAAvB1L,EAAI6L,gBAAkCF,EAAgBa,IAE/B,KAAlBf,EAAOzK,OAAgCsG,WAAWmE,EAAO,KAE5CnE,WAAWmE,EAAO,KAEnCC,GAAgB,GAEzBe,cAAe,SAAuBC,GACpC,IAEI3L,EACA4L,EACAC,EACA5L,EALA6L,EAAQ,GACRC,EAAaJ,GAAO1M,EAAIF,SAASiN,KAKrC,GAA0B,iBAAfD,GAA2BA,EAAW9L,OAK/C,IAFAA,GADA2L,GADAG,GAAwC,EAA3BA,EAAWtL,QAAQ,KAAYsL,EAAWd,QAAQ,QAAS,IAAM,IAC1DnK,MAAM,KAAK6E,OAAO,SAAUsG,GAAc,MAAsB,KAAfA,KACrDhM,OAEXD,EAAI,EAAGA,EAAIC,EAAQD,GAAK,EAC3B6L,EAAQD,EAAO5L,GAAGiL,QAAQ,QAAS,IAAInK,MAAM,KAC7CgL,EAAMI,mBAAmBL,EAAM,UAA2B,IAAbA,EAAM,QAAqBnH,EAAYwH,mBAAmBL,EAAM,KAAO,GAGxH,OAAOC,GAETK,SAAU,SAAkBC,GAC1B,MAAoB,iBAANA,GAAwB,OAANA,GAAcA,EAAEC,aAAeD,EAAEC,cAAgB1C,QAEnF2C,OAAQ,WAEN,IADA,IAAI7I,EAAO,GAAI8I,EAAQjK,UAAUrC,OACzBsM,KAAU9I,EAAM8I,GAAUjK,UAAWiK,GAG7C,IADA,IAAIC,EAAK7C,OAAOlG,EAAK,IACZzD,EAAI,EAAGA,EAAIyD,EAAKxD,OAAQD,GAAK,EAAG,CACvC,IAAIyM,EAAahJ,EAAKzD,GACtB,GAAIyM,MAAAA,EAEF,IADA,IAAIC,EAAY/C,OAAOC,KAAKD,OAAO8C,IAC1BE,EAAY,EAAGjJ,EAAMgJ,EAAUzM,OAAQ0M,EAAYjJ,EAAKiJ,GAAa,EAAG,CAC/E,IAAIC,EAAUF,EAAUC,GACpBE,EAAOlD,OAAOmD,yBAAyBL,EAAYG,QAC1ClI,IAATmI,GAAsBA,EAAKE,aACzB9C,GAAMkC,SAASK,EAAGI,KAAa3C,GAAMkC,SAASM,EAAWG,IAC3D3C,GAAMqC,OAAOE,EAAGI,GAAUH,EAAWG,KAC3B3C,GAAMkC,SAASK,EAAGI,KAAa3C,GAAMkC,SAASM,EAAWG,KACnEJ,EAAGI,GAAW,GACd3C,GAAMqC,OAAOE,EAAGI,GAAUH,EAAWG,KAErCJ,EAAGI,GAAWH,EAAWG,KAMnC,OAAOJ,IAIPQ,IACEjD,EAAUnM,EAAIa,cAAc,OACzB,CACLwO,MAAQhO,EAAIiO,YAAqC,IAAxBjO,EAAIiO,UAAUD,UACK,EAA/BhO,EAAIE,UAAUgO,gBAAwB,iBAAkBlO,GAASA,EAAImO,eAAiBxP,aAAeqB,EAAImO,eAGtHC,iBAAkBpO,EAAIE,UAAUmO,gBAAkBrO,EAAIsO,cAAiB,mBAAoBtO,EAAIE,WAA4C,EAA/BF,EAAIE,UAAUgO,gBAC1HK,wBAAyBvO,EAAIE,UAAUsO,iBAEvCtK,YACMvE,EAAQmL,EAAQnL,MACZ,eAAgBA,GAAS,qBAAsBA,GAAS,kBAAmBA,GAErF8O,aAAezO,EAAIiO,YAA+C,IAAlCjO,EAAIiO,UAAUS,kBACxC/O,EAAQmL,EAAQnL,MACZ,sBAAuBA,GAAS,mBAAoBA,GAAS,iBAAkBA,GAAS,kBAAmBA,GAAS,gBAAiBA,GAG/IgP,QAAU,WAGR,IAFA,IAAIhP,EAAQmL,EAAQnL,MAChByH,EAAS,yKAA2KvF,MAAM,KACrLd,EAAI,EAAGA,EAAIqG,EAAOpG,OAAQD,GAAK,EACtC,GAAIqG,EAAOrG,KAAMpB,EAAS,OAAO,EAEnC,OAAO,EANA,GASTiP,SACU,qBAAsB5O,GAAO,2BAA4BA,EAGnE6O,gBAAkB,WAChB,IAAIC,GAAkB,EACtB,IACE,IAAIC,EAAOrE,OAAOsE,eAAe,GAAI,UAAW,CAE9CC,IAAK,WACHH,GAAkB,KAGtB9O,EAAIlB,iBAAiB,sBAAuB,KAAMiQ,GAClD,MAAOhK,IAGT,OAAO+J,EAbQ,GAgBjBI,SACS,mBAAoBlP,IAK7BmP,EAKK,CACLC,OAAQpP,EAAIE,UAAUC,UAAUyB,MAAM,eAAiB5B,EAAIE,UAAUC,UAAUyB,MAAM,SACrFyN,SAAUrP,EAAIE,UAAUC,UAAUyB,MAAM,SACxC0N,UANIvE,EAAK/K,EAAIE,UAAUC,UAAUoP,cACD,GAAxBxE,EAAGvJ,QAAQ,WAAkBuJ,EAAGvJ,QAAQ,UAAY,GAAKuJ,EAAGvJ,QAAQ,WAAa,GAMzFgO,YAAa,+CAA+CC,KAAKzP,EAAIE,UAAUC,YAI/EuP,EAAc,SAAqB/C,QACrB,IAAXA,IAAoBA,EAAS,IAElC,IAAInO,EAAOE,KACXF,EAAKmO,OAASA,EAGdnO,EAAKmR,gBAAkB,GAEnBnR,EAAKmO,QAAUnO,EAAKmO,OAAOrI,IAC7BoG,OAAOC,KAAKnM,EAAKmO,OAAOrI,IAAIsG,QAAQ,SAAUgF,GAC5CpR,EAAK8F,GAAGsL,EAAWpR,EAAKmO,OAAOrI,GAAGsL,OAKpCC,EAAkB,CAAEC,WAAY,CAAEC,cAAc,IAEpDL,EAAYxN,UAAUoC,GAAK,SAAaoB,EAAQQ,EAAS8J,GACvD,IAAIxR,EAAOE,KACX,GAAuB,mBAAZwH,EAA0B,OAAO1H,EAC5C,IAAIyR,EAASD,EAAW,UAAY,OAKpC,OAJAtK,EAAO7D,MAAM,KAAK+I,QAAQ,SAAU9E,GAC7BtH,EAAKmR,gBAAgB7J,KAAUtH,EAAKmR,gBAAgB7J,GAAS,IAClEtH,EAAKmR,gBAAgB7J,GAAOmK,GAAQ/J,KAE/B1H,GAGTkR,EAAYxN,UAAUgO,KAAO,SAAexK,EAAQQ,EAAS8J,GAC3D,IAAIxR,EAAOE,KACX,GAAuB,mBAAZwH,EAA0B,OAAO1H,EAC5C,SAAS2R,IAEL,IADA,IAAI3L,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAE3CyB,EAAQb,MAAM7G,EAAMgG,GACpBhG,EAAKwH,IAAIN,EAAQyK,GACbA,EAAYC,gBACPD,EAAYC,QAIvB,OADAD,EAAYC,QAAUlK,EACf1H,EAAK8F,GAAGoB,EAAQyK,EAAaH,IAGtCN,EAAYxN,UAAU8D,IAAM,SAAcN,EAAQQ,GAChD,IAAI1H,EAAOE,KACX,OAAKF,EAAKmR,iBACVjK,EAAO7D,MAAM,KAAK+I,QAAQ,SAAU9E,QACX,IAAZI,EACT1H,EAAKmR,gBAAgB7J,GAAS,GACrBtH,EAAKmR,gBAAgB7J,IAAUtH,EAAKmR,gBAAgB7J,GAAO9E,QACpExC,EAAKmR,gBAAgB7J,GAAO8E,QAAQ,SAAUyF,EAAcvH,IACtDuH,IAAiBnK,GAAYmK,EAAaD,SAAWC,EAAaD,UAAYlK,IAChF1H,EAAKmR,gBAAgB7J,GAAOK,OAAO2C,EAAO,OAK3CtK,GAGTkR,EAAYxN,UAAUoO,KAAO,WAEzB,IADA,IAAI9L,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAE3C,IAEIiB,EACAhC,EACAvC,EAJA3C,EAAOE,KACX,OAAKF,EAAKmR,kBAIa,iBAAZnL,EAAK,IAAmB+L,MAAMC,QAAQhM,EAAK,KACpDkB,EAASlB,EAAK,GACdd,EAAOc,EAAKiM,MAAM,EAAGjM,EAAKxD,QAC1BG,EAAU3C,IAEVkH,EAASlB,EAAK,GAAGkB,OACjBhC,EAAOc,EAAK,GAAGd,KACfvC,EAAUqD,EAAK,GAAGrD,SAAW3C,IAEb+R,MAAMC,QAAQ9K,GAAUA,EAASA,EAAO7D,MAAM,MACpD+I,QAAQ,SAAU9E,GAC5B,GAAItH,EAAKmR,iBAAmBnR,EAAKmR,gBAAgB7J,GAAQ,CACvD,IAAIG,EAAW,GACfzH,EAAKmR,gBAAgB7J,GAAO8E,QAAQ,SAAUyF,GAC5CpK,EAAStE,KAAK0O,KAEhBpK,EAAS2E,QAAQ,SAAUyF,GACzBA,EAAahL,MAAMlE,EAASuC,SAI3BlF,GAGTkR,EAAYxN,UAAUwO,iBAAmB,SAA2BC,GAClE,IAAIC,EAAWlS,KACVkS,EAASC,SACdnG,OAAOC,KAAKiG,EAASC,SAASjG,QAAQ,SAAUkG,GAC9C,IAAIzS,EAASuS,EAASC,QAAQC,GAE1BzS,EAAOsO,QACT3B,GAAMqC,OAAOsD,EAAgBtS,EAAOsO,WAK1C+C,EAAYxN,UAAU6O,WAAa,SAAqBC,QAC7B,IAAlBA,IAA2BA,EAAgB,IAElD,IAAIJ,EAAWlS,KACVkS,EAASC,SACdnG,OAAOC,KAAKiG,EAASC,SAASjG,QAAQ,SAAUkG,GAC9C,IAAIzS,EAASuS,EAASC,QAAQC,GAC1BG,EAAeD,EAAcF,IAAe,GAE5CzS,EAAOuS,UACTlG,OAAOC,KAAKtM,EAAOuS,UAAUhG,QAAQ,SAAUsG,GAC7C,IAAIC,EAAa9S,EAAOuS,SAASM,GAE/BN,EAASM,GADe,mBAAfC,EACkBA,EAAWC,KAAKR,GAEhBO,IAK7B9S,EAAOiG,IAAMsM,EAAStM,IACxBoG,OAAOC,KAAKtM,EAAOiG,IAAIsG,QAAQ,SAAUyG,GACvCT,EAAStM,GAAG+M,EAAiBhT,EAAOiG,GAAG+M,MAKvChT,EAAOiT,QACTjT,EAAOiT,OAAOF,KAAKR,EAAnBvS,CAA6B4S,MAKnCpB,EAAgBC,WAAWyB,IAAM,SAAUzB,GAC7BpR,KACD8S,KADC9S,KAEN8S,IAAI1B,IAGZJ,EAAY+B,cAAgB,SAAwBpT,GAEhD,IADA,IAAIsO,EAAS,GAAIlI,EAAMpB,UAAUrC,OAAS,EAC1B,EAARyD,KAAYkI,EAAQlI,GAAQpB,UAAWoB,EAAM,GAEvD,IAAItC,EAAQzD,KACPyD,EAAMD,UAAU2O,UAAW1O,EAAMD,UAAU2O,QAAU,IAC1D,IAAIa,EAAOrT,EAAOqT,MAAWhH,OAAOC,KAAKxI,EAAMD,UAAU2O,SAAe,OAAI,IAAO7F,GAAMM,MAkBzF,OAjBAnJ,EAAMD,UAAU2O,QAAQa,GAAQrT,GAErBsT,OACTjH,OAAOC,KAAKtM,EAAOsT,OAAO/G,QAAQ,SAAUjH,GAC1CxB,EAAMD,UAAUyB,GAAOtF,EAAOsT,MAAMhO,KAIpCtF,EAAOuT,QACTlH,OAAOC,KAAKtM,EAAOuT,QAAQhH,QAAQ,SAAUjH,GAC3CxB,EAAMwB,GAAOtF,EAAOuT,OAAOjO,KAI3BtF,EAAOwT,SACTxT,EAAOwT,QAAQxM,MAAMlD,EAAOwK,GAEvBxK,GAGTuN,EAAY8B,IAAM,SAAcnT,GAE5B,IADA,IAAIsO,EAAS,GAAIlI,EAAMpB,UAAUrC,OAAS,EAC1B,EAARyD,KAAYkI,EAAQlI,GAAQpB,UAAWoB,EAAM,GAEvD,IAAItC,EAAQzD,KACZ,OAAI6R,MAAMC,QAAQnS,IAChBA,EAAOuM,QAAQ,SAAUkH,GAAK,OAAO3P,EAAMsP,cAAcK,KAClD3P,GAEFA,EAAMsP,cAAcpM,MAAMlD,EAAO,CAAE9D,GAAS0T,OAAQpF,KAG7DjC,OAAOsH,iBAAkBtC,EAAaG,GAslBtC,IAAIoC,EAAS,CACXC,WArlBF,WACE,IACIC,EACAC,EAFAC,EAAS3T,KAGT4T,EAAMD,EAAOC,IAEfH,OADiC,IAAxBE,EAAO1F,OAAOwF,MACfE,EAAO1F,OAAOwF,MAEdG,EAAI,GAAGC,YAGfH,OADkC,IAAzBC,EAAO1F,OAAOyF,OACdC,EAAO1F,OAAOyF,OAEdE,EAAI,GAAGE,aAEH,IAAVL,GAAeE,EAAOI,gBAA+B,IAAXL,GAAgBC,EAAOK,eAKtEP,EAAQA,EAAQQ,SAASL,EAAIlK,IAAI,gBAAiB,IAAMuK,SAASL,EAAIlK,IAAI,iBAAkB,IAC3FgK,EAASA,EAASO,SAASL,EAAIlK,IAAI,eAAgB,IAAMuK,SAASL,EAAIlK,IAAI,kBAAmB,IAE7F4C,GAAMqC,OAAOgF,EAAQ,CACnBF,MAAOA,EACPC,OAAQA,EACRQ,KAAMP,EAAOI,eAAiBN,EAAQC,MA4jBxCS,aAxjBF,WACE,IAAIR,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAEhBmG,EAAaT,EAAOS,WACpBC,EAAaV,EAAOO,KACpBI,EAAMX,EAAOY,aACbC,EAAWb,EAAOa,SAClBC,EAAYd,EAAOe,SAAWzG,EAAOyG,QAAQC,QAC7CC,EAAuBH,EAAYd,EAAOe,QAAQG,OAAOvS,OAASqR,EAAOkB,OAAOvS,OAChFuS,EAAST,EAAWrT,SAAU,IAAO4S,EAAO1F,OAAiB,YAC7D6G,EAAeL,EAAYd,EAAOe,QAAQG,OAAOvS,OAASuS,EAAOvS,OACjEyS,EAAW,GACXC,EAAa,GACbC,EAAkB,GAElBC,EAAejH,EAAOkH,mBACE,mBAAjBD,IACTA,EAAejH,EAAOkH,mBAAmB5M,KAAKoL,IAGhD,IAAIyB,EAAcnH,EAAOoH,kBACE,mBAAhBD,IACTA,EAAcnH,EAAOoH,kBAAkB9M,KAAKoL,IAG9C,IAAI2B,EAAyB3B,EAAOoB,SAASzS,OACzCiT,EAA2B5B,EAAOoB,SAASzS,OAE3CkT,EAAevH,EAAOuH,aACtBC,GAAiBP,EACjBQ,EAAgB,EAChBtL,EAAQ,EACZ,QAA0B,IAAfiK,EAAX,CAaA,IAAIsB,EAaAC,EAvBwB,iBAAjBJ,GAA0D,GAA7BA,EAAa1S,QAAQ,OAC3D0S,EAAgB5M,WAAW4M,EAAalI,QAAQ,IAAK,KAAO,IAAO+G,GAGrEV,EAAOkC,aAAeL,EAGlBlB,EAAOO,EAAOnL,IAAI,CAAEoM,WAAY,GAAIC,UAAW,KAC5ClB,EAAOnL,IAAI,CAAEsM,YAAa,GAAIC,aAAc,KAGtB,EAAzBhI,EAAOiI,kBAEPP,EADEQ,KAAKC,MAAMtB,EAAe7G,EAAOiI,mBAAqBpB,EAAenB,EAAO1F,OAAOiI,gBAC5DpB,EAEAqB,KAAKE,KAAKvB,EAAe7G,EAAOiI,iBAAmBjI,EAAOiI,gBAExD,SAAzBjI,EAAOqI,eAA2D,QAA/BrI,EAAOsI,sBAC5CZ,EAAyBQ,KAAKK,IAAIb,EAAwB1H,EAAOqI,cAAgBrI,EAAOiI,mBAS5F,IAHA,IAqIIO,EArIAP,EAAkBjI,EAAOiI,gBACzBQ,EAAef,EAAyBO,EACxCS,EAAiBR,KAAKC,MAAMtB,EAAe7G,EAAOiI,iBAC7C7T,EAAI,EAAGA,EAAIyS,EAAczS,GAAK,EAAG,CACxCuT,EAAY,EACZ,IAAIgB,EAAQ/B,EAAOtK,GAAGlI,GACtB,GAA6B,EAAzB4L,EAAOiI,gBAAqB,CAE9B,IAAIW,OAAqB,EACrBC,OAAS,EACTC,OAAM,EACyB,WAA/B9I,EAAOsI,qBAETQ,EAAM1U,GADNyU,EAASX,KAAKC,MAAM/T,EAAI6T,IACJA,GACPS,EAATG,GAA4BA,IAAWH,GAAkBI,IAAQb,EAAkB,IAE1EA,IADXa,GAAO,KAELA,EAAM,EACND,GAAU,GAGdD,EAAqBC,EAAWC,EAAMpB,EAA0BO,EAChEU,EACGlN,IAAI,CACHsN,4BAA6BH,EAC7BI,yBAA0BJ,EAC1BK,iBAAkBL,EAClBM,gBAAiBN,EACjBO,MAAOP,KAIXC,EAASzU,GADT0U,EAAMZ,KAAKC,MAAM/T,EAAIqU,IACDA,EAEtBE,EACGlN,IACE,WAAaiK,EAAOI,eAAiB,MAAQ,QACrC,IAARgD,GAAa9I,EAAOuH,cAAoBvH,EAAmB,aAAI,MAEjE1J,KAAK,qBAAsBuS,GAC3BvS,KAAK,kBAAmBwS,GAE7B,GAA6B,SAAzBH,EAAMlN,IAAI,WAAd,CAEA,GAA6B,SAAzBuE,EAAOqI,cAA0B,CACnC,IAAIe,EAAc/V,EAAIM,iBAAiBgV,EAAM,GAAI,MAC7CU,EAAmBV,EAAM,GAAG3V,MAAMoE,UAClCkS,EAAyBX,EAAM,GAAG3V,MAAMsE,gBAO5C,GANI+R,IACFV,EAAM,GAAG3V,MAAMoE,UAAY,QAEzBkS,IACFX,EAAM,GAAG3V,MAAMsE,gBAAkB,QAE/B0I,EAAOuJ,aACT5B,EAAYjC,EAAOI,eACf6C,EAAMpO,YAAW,GACjBoO,EAAM/N,aAAY,QAGtB,GAAI8K,EAAOI,eAAgB,CACzB,IAAIN,EAAQ7K,WAAWyO,EAAYxV,iBAAiB,UAChD4V,EAAc7O,WAAWyO,EAAYxV,iBAAiB,iBACtD6V,EAAe9O,WAAWyO,EAAYxV,iBAAiB,kBACvDiU,EAAalN,WAAWyO,EAAYxV,iBAAiB,gBACrDmU,EAAcpN,WAAWyO,EAAYxV,iBAAiB,iBACtD8V,EAAYN,EAAYxV,iBAAiB,cAE3C+T,EADE+B,GAA2B,eAAdA,EACHlE,EAAQqC,EAAaE,EAErBvC,EAAQgE,EAAcC,EAAe5B,EAAaE,MAE3D,CACL,IAAItC,EAAS9K,WAAWyO,EAAYxV,iBAAiB,WACjD+V,EAAahP,WAAWyO,EAAYxV,iBAAiB,gBACrDgW,EAAgBjP,WAAWyO,EAAYxV,iBAAiB,mBACxDkU,EAAYnN,WAAWyO,EAAYxV,iBAAiB,eACpDoU,EAAerN,WAAWyO,EAAYxV,iBAAiB,kBACvDiW,EAAcT,EAAYxV,iBAAiB,cAE7C+T,EADEkC,GAA+B,eAAhBA,EACLpE,EAASqC,EAAYE,EAErBvC,EAASkE,EAAaC,EAAgB9B,EAAYE,EAIhEqB,IACFV,EAAM,GAAG3V,MAAMoE,UAAYiS,GAEzBC,IACFX,EAAM,GAAG3V,MAAMsE,gBAAkBgS,GAE/BtJ,EAAOuJ,eAAgB5B,EAAYO,KAAKC,MAAMR,SAElDA,GAAavB,GAAepG,EAAOqI,cAAgB,GAAKd,GAAiBvH,EAAOqI,cAC5ErI,EAAOuJ,eAAgB5B,EAAYO,KAAKC,MAAMR,IAE9Cf,EAAOxS,KACLsR,EAAOI,eACTc,EAAOxS,GAAGpB,MAAMwS,MAAQmC,EAAY,KAEpCf,EAAOxS,GAAGpB,MAAMyS,OAASkC,EAAY,MAIvCf,EAAOxS,KACTwS,EAAOxS,GAAG0V,gBAAkBnC,GAE9BX,EAAgBhS,KAAK2S,GAGjB3H,EAAO+J,gBACTvC,EAAgBA,EAAiBG,EAAY,EAAMF,EAAgB,EAAKF,EAClD,IAAlBE,GAA6B,IAANrT,IAAWoT,EAAgBA,EAAiBpB,EAAa,EAAKmB,GAC/E,IAANnT,IAAWoT,EAAgBA,EAAiBpB,EAAa,EAAKmB,GAC9DW,KAAK8B,IAAIxC,GAAiB,OAAYA,EAAgB,GACtDxH,EAAOuJ,eAAgB/B,EAAgBU,KAAKC,MAAMX,IAClD,EAAUxH,EAAOiK,gBAAmB,GAAKnD,EAAS9R,KAAKwS,GAC3DT,EAAW/R,KAAKwS,KAEZxH,EAAOuJ,eAAgB/B,EAAgBU,KAAKC,MAAMX,IAClD,EAAUxH,EAAOiK,gBAAmB,GAAKnD,EAAS9R,KAAKwS,GAC3DT,EAAW/R,KAAKwS,GAChBA,EAAgBA,EAAgBG,EAAYJ,GAG9C7B,EAAOkC,aAAeD,EAAYJ,EAElCE,EAAgBE,EAEhBxL,GAAS,GAcX,GAZAuJ,EAAOkC,YAAcM,KAAKK,IAAI7C,EAAOkC,YAAaxB,GAAce,EAI9Dd,GAAOE,IAA+B,UAAlBvG,EAAOkK,QAAwC,cAAlBlK,EAAOkK,SACxD/D,EAAW1K,IAAI,CAAE+J,MAASE,EAAOkC,YAAc5H,EAAOuH,aAAgB,OAEnEnG,GAAQY,UAAWhC,EAAOmK,iBACzBzE,EAAOI,eAAkBK,EAAW1K,IAAI,CAAE+J,MAASE,EAAOkC,YAAc5H,EAAOuH,aAAgB,OAC5FpB,EAAW1K,IAAI,CAAEgK,OAAUC,EAAOkC,YAAc5H,EAAOuH,aAAgB,QAGnD,EAAzBvH,EAAOiI,kBACTvC,EAAOkC,aAAeD,EAAY3H,EAAOuH,cAAgBG,EACzDhC,EAAOkC,YAAcM,KAAKE,KAAK1C,EAAOkC,YAAc5H,EAAOiI,iBAAmBjI,EAAOuH,aACjF7B,EAAOI,eAAkBK,EAAW1K,IAAI,CAAE+J,MAASE,EAAOkC,YAAc5H,EAAOuH,aAAgB,OAC5FpB,EAAW1K,IAAI,CAAEgK,OAAUC,EAAOkC,YAAc5H,EAAOuH,aAAgB,OAC1EvH,EAAO+J,gBAAgB,CACzBvB,EAAgB,GAChB,IAAK,IAAI4B,EAAM,EAAGA,EAAMtD,EAASzS,OAAQ+V,GAAO,EAAG,CACjD,IAAIC,EAAiBvD,EAASsD,GAC1BpK,EAAOuJ,eAAgBc,EAAiBnC,KAAKC,MAAMkC,IACnDvD,EAASsD,GAAO1E,EAAOkC,YAAcd,EAAS,IAAM0B,EAAcxT,KAAKqV,GAE7EvD,EAAW0B,EAKf,IAAKxI,EAAO+J,eAAgB,CAC1BvB,EAAgB,GAChB,IAAK,IAAI8B,EAAM,EAAGA,EAAMxD,EAASzS,OAAQiW,GAAO,EAAG,CACjD,IAAIC,EAAmBzD,EAASwD,GAC5BtK,EAAOuJ,eAAgBgB,EAAmBrC,KAAKC,MAAMoC,IACrDzD,EAASwD,IAAQ5E,EAAOkC,YAAcxB,GACxCoC,EAAcxT,KAAKuV,GAGvBzD,EAAW0B,EACmF,EAA1FN,KAAKC,MAAMzC,EAAOkC,YAAcxB,GAAc8B,KAAKC,MAAMrB,EAASA,EAASzS,OAAS,KACtFyS,EAAS9R,KAAK0Q,EAAOkC,YAAcxB,GAYvC,GATwB,IAApBU,EAASzS,SAAgByS,EAAW,CAAC,IAEb,IAAxB9G,EAAOuH,eACL7B,EAAOI,eACLO,EAAOO,EAAOnL,IAAI,CAAEoM,WAAaN,EAAe,OAC7CX,EAAOnL,IAAI,CAAEsM,YAAcR,EAAe,OAC1CX,EAAOnL,IAAI,CAAEuM,aAAeT,EAAe,QAGlDvH,EAAOwK,yBAA0B,CACnC,IAAIC,EAAgB,EAKpB,GAJAzD,EAAgB/I,QAAQ,SAAUyM,GAChCD,GAAiBC,GAAkB1K,EAAOuH,aAAevH,EAAOuH,aAAe,MAEjFkD,GAAiBzK,EAAOuH,cACJnB,EAAY,CAC9B,IAAIuE,GAAmBvE,EAAaqE,GAAiB,EACrD3D,EAAS7I,QAAQ,SAAU2M,EAAMC,GAC/B/D,EAAS+D,GAAaD,EAAOD,IAE/B5D,EAAW9I,QAAQ,SAAU2M,EAAMC,GACjC9D,EAAW8D,GAAaD,EAAOD,KAKrCtM,GAAMqC,OAAOgF,EAAQ,CACnBkB,OAAQA,EACRE,SAAUA,EACVC,WAAYA,EACZC,gBAAiBA,IAGfH,IAAiBF,GACnBjB,EAAO/B,KAAK,sBAEVmD,EAASzS,SAAWgT,IAClB3B,EAAO1F,OAAO8K,eAAiBpF,EAAOqF,gBAC1CrF,EAAO/B,KAAK,yBAEVoD,EAAW1S,SAAWiT,GACxB5B,EAAO/B,KAAK,2BAGV3D,EAAOgL,qBAAuBhL,EAAOiL,wBACvCvF,EAAOwF,uBAiSTC,iBA7RF,SAA2BC,GACzB,IAGIhX,EAHAsR,EAAS3T,KACTsZ,EAAe,GACfC,EAAY,EAQhB,GANqB,iBAAVF,EACT1F,EAAO6F,cAAcH,IACF,IAAVA,GACT1F,EAAO6F,cAAc7F,EAAO1F,OAAOoL,OAGD,SAAhC1F,EAAO1F,OAAOqI,eAA0D,EAA9B3C,EAAO1F,OAAOqI,cAC1D,IAAKjU,EAAI,EAAGA,EAAI8T,KAAKE,KAAK1C,EAAO1F,OAAOqI,eAAgBjU,GAAK,EAAG,CAC9D,IAAI+H,EAAQuJ,EAAO8F,YAAcpX,EACjC,GAAI+H,EAAQuJ,EAAOkB,OAAOvS,OAAU,MACpCgX,EAAarW,KAAK0Q,EAAOkB,OAAOtK,GAAGH,GAAO,SAG5CkP,EAAarW,KAAK0Q,EAAOkB,OAAOtK,GAAGoJ,EAAO8F,aAAa,IAIzD,IAAKpX,EAAI,EAAGA,EAAIiX,EAAahX,OAAQD,GAAK,EACxC,QAA+B,IAApBiX,EAAajX,GAAoB,CAC1C,IAAIqR,EAAS4F,EAAajX,GAAGyG,aAC7ByQ,EAAqBA,EAAT7F,EAAqBA,EAAS6F,EAK1CA,GAAa5F,EAAOS,WAAW1K,IAAI,SAAW6P,EAAY,OAgQ9DJ,mBA7PF,WAGE,IAFA,IACItE,EADS7U,KACO6U,OACXxS,EAAI,EAAGA,EAAIwS,EAAOvS,OAAQD,GAAK,EACtCwS,EAAOxS,GAAGqX,kBAHC1Z,KAG0B+T,eAAiBc,EAAOxS,GAAGsX,WAAa9E,EAAOxS,GAAGuX,WA0PzFC,qBAtPF,SAA+BC,QACV,IAAdA,IAAuBA,EAAa9Z,MAAQA,KAAK8Z,WAAc,GAEpE,IAAInG,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAEhB4G,EAASlB,EAAOkB,OAChBP,EAAMX,EAAOY,aAEjB,GAAsB,IAAlBM,EAAOvS,OAAX,MAC2C,IAAhCuS,EAAO,GAAG6E,mBAAqC/F,EAAOwF,qBAEjE,IAAIY,GAAgBD,EAChBxF,IAAOyF,EAAeD,GAG1BjF,EAAO5Q,YAAYgK,EAAO+L,mBAE1BrG,EAAOsG,qBAAuB,GAC9BtG,EAAOuG,cAAgB,GAEvB,IAAK,IAAI7X,EAAI,EAAGA,EAAIwS,EAAOvS,OAAQD,GAAK,EAAG,CACzC,IAAIuU,EAAQ/B,EAAOxS,GACf8X,GACDJ,GAAgB9L,EAAO+J,eAAiBrE,EAAOyG,eAAiB,GAAMxD,EAAM8C,oBAC1E9C,EAAMmB,gBAAkB9J,EAAOuH,cACpC,GAAIvH,EAAOiL,sBAAuB,CAChC,IAAImB,IAAgBN,EAAenD,EAAM8C,mBACrCY,EAAaD,EAAc1G,EAAOsB,gBAAgB5S,IACtB,GAAfgY,GAAoBA,EAAc1G,EAAOO,MAC/B,EAAboG,GAAkBA,GAAc3G,EAAOO,MACvCmG,GAAe,GAAKC,GAAc3G,EAAOO,QAErDP,EAAOuG,cAAcjX,KAAK2T,GAC1BjD,EAAOsG,qBAAqBhX,KAAKZ,GACjCwS,EAAOtK,GAAGlI,GAAGsB,SAASsK,EAAO+L,oBAGjCpD,EAAM2D,SAAWjG,GAAO6F,EAAgBA,EAE1CxG,EAAOuG,cAAgB3X,EAAEoR,EAAOuG,iBA+MhCM,eA5MF,SAAyBV,QACJ,IAAdA,IAAuBA,EAAa9Z,MAAQA,KAAK8Z,WAAc,GAEpE,IAAInG,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAEhBwM,EAAiB9G,EAAO+G,eAAiB/G,EAAOyG,eAChDG,EAAW5G,EAAO4G,SAClBI,EAAchH,EAAOgH,YACrBC,EAAQjH,EAAOiH,MACfC,EAAeF,EACfG,EAASF,EACU,IAAnBH,EAGFG,EADAD,IADAJ,EAAW,IAKXI,GADAJ,GAAYT,EAAYnG,EAAOyG,gBAAkB,IACvB,EAC1BQ,EAAoB,GAAZL,GAEVjO,GAAMqC,OAAOgF,EAAQ,CACnB4G,SAAUA,EACVI,YAAaA,EACbC,MAAOA,KAGL3M,EAAOgL,qBAAuBhL,EAAOiL,wBAAyBvF,EAAOkG,qBAAqBC,GAE1Fa,IAAgBE,GAClBlH,EAAO/B,KAAK,yBAEVgJ,IAAUE,GACZnH,EAAO/B,KAAK,oBAETiJ,IAAiBF,GAAiBG,IAAWF,IAChDjH,EAAO/B,KAAK,YAGd+B,EAAO/B,KAAK,WAAY2I,IAsKxBQ,oBAnKF,WACE,IAWIC,EAXArH,EAAS3T,KAET6U,EAASlB,EAAOkB,OAChB5G,EAAS0F,EAAO1F,OAChBmG,EAAaT,EAAOS,WACpBqF,EAAc9F,EAAO8F,YACrBwB,EAAYtH,EAAOsH,UACnBxG,EAAYd,EAAOe,SAAWzG,EAAOyG,QAAQC,QAEjDE,EAAO5Q,YAAcgK,EAAuB,iBAAI,IAAOA,EAAqB,eAAI,IAAOA,EAAqB,eAAI,IAAOA,EAAgC,0BAAI,IAAOA,EAA8B,wBAAI,IAAOA,EAA8B,0BAIvO+M,EADEvG,EACYd,EAAOS,WAAWzI,KAAM,IAAOsC,EAAiB,WAAI,6BAAgCwL,EAAc,MAElG5E,EAAOtK,GAAGkP,IAId9V,SAASsK,EAAOiN,kBAExBjN,EAAOkN,OAELH,EAAY7W,SAAS8J,EAAOmN,qBAC9BhH,EACGrT,SAAU,IAAOkN,EAAiB,WAAI,SAAYA,EAA0B,oBAAI,8BAAiCgN,EAAY,MAC7HtX,SAASsK,EAAOoN,2BAEnBjH,EACGrT,SAAU,IAAOkN,EAAiB,WAAI,IAAOA,EAA0B,oBAAI,6BAAgCgN,EAAY,MACvHtX,SAASsK,EAAOoN,4BAIvB,IAAIC,EAAYN,EAAY9P,QAAS,IAAO+C,EAAiB,YAAI1D,GAAG,GAAG5G,SAASsK,EAAOsN,gBACnFtN,EAAOkN,MAA6B,IAArBG,EAAUhZ,SAC3BgZ,EAAYzG,EAAOtK,GAAG,IACZ5G,SAASsK,EAAOsN,gBAG5B,IAAIC,EAAYR,EAAY1P,QAAS,IAAO2C,EAAiB,YAAI1D,GAAG,GAAG5G,SAASsK,EAAOwN,gBACnFxN,EAAOkN,MAA6B,IAArBK,EAAUlZ,SAC3BkZ,EAAY3G,EAAOtK,IAAI,IACb5G,SAASsK,EAAOwN,gBAExBxN,EAAOkN,OAELG,EAAUnX,SAAS8J,EAAOmN,qBAC5BhH,EACGrT,SAAU,IAAOkN,EAAiB,WAAI,SAAYA,EAA0B,oBAAI,8BAAkCqN,EAAU/W,KAAK,2BAA8B,MAC/JZ,SAASsK,EAAOyN,yBAEnBtH,EACGrT,SAAU,IAAOkN,EAAiB,WAAI,IAAOA,EAA0B,oBAAI,6BAAiCqN,EAAU/W,KAAK,2BAA8B,MACzJZ,SAASsK,EAAOyN,yBAEjBF,EAAUrX,SAAS8J,EAAOmN,qBAC5BhH,EACGrT,SAAU,IAAOkN,EAAiB,WAAI,SAAYA,EAA0B,oBAAI,8BAAkCuN,EAAUjX,KAAK,2BAA8B,MAC/JZ,SAASsK,EAAO0N,yBAEnBvH,EACGrT,SAAU,IAAOkN,EAAiB,WAAI,IAAOA,EAA0B,oBAAI,6BAAiCuN,EAAUjX,KAAK,2BAA8B,MACzJZ,SAASsK,EAAO0N,2BAoGvBC,kBA/FF,SAA4BC,GAC1B,IASI/C,EATAnF,EAAS3T,KACT8Z,EAAYnG,EAAOY,aAAeZ,EAAOmG,WAAanG,EAAOmG,UAC7D9E,EAAarB,EAAOqB,WACpBD,EAAWpB,EAAOoB,SAClB9G,EAAS0F,EAAO1F,OAChB6N,EAAgBnI,EAAO8F,YACvBsC,EAAoBpI,EAAOsH,UAC3Be,EAAoBrI,EAAOmF,UAC3BW,EAAcoC,EAElB,QAA2B,IAAhBpC,EAA6B,CACtC,IAAK,IAAIpX,EAAI,EAAGA,EAAI2S,EAAW1S,OAAQD,GAAK,OACT,IAAtB2S,EAAW3S,EAAI,GACpByX,GAAa9E,EAAW3S,IAAMyX,EAAY9E,EAAW3S,EAAI,IAAO2S,EAAW3S,EAAI,GAAK2S,EAAW3S,IAAM,EACvGoX,EAAcpX,EACLyX,GAAa9E,EAAW3S,IAAMyX,EAAY9E,EAAW3S,EAAI,KAClEoX,EAAcpX,EAAI,GAEXyX,GAAa9E,EAAW3S,KACjCoX,EAAcpX,GAId4L,EAAOgO,sBACLxC,EAAc,QAA4B,IAAhBA,KAA+BA,EAAc,GAS/E,IALEX,EADiC,GAA/B/D,EAASjS,QAAQgX,GACP/E,EAASjS,QAAQgX,GAEjB3D,KAAKC,MAAMqD,EAAcxL,EAAOiK,kBAE7BnD,EAASzS,SAAUwW,EAAY/D,EAASzS,OAAS,GAC9DmX,IAAgBqC,EAApB,CASA,IAAIb,EAAYhH,SAASN,EAAOkB,OAAOtK,GAAGkP,GAAalV,KAAK,4BAA8BkV,EAAa,IAEvGnN,GAAMqC,OAAOgF,EAAQ,CACnBmF,UAAWA,EACXmC,UAAWA,EACXa,cAAeA,EACfrC,YAAaA,IAEf9F,EAAO/B,KAAK,qBACZ+B,EAAO/B,KAAK,mBACRmK,IAAsBd,GACxBtH,EAAO/B,KAAK,mBAEd+B,EAAO/B,KAAK,oBArBNkH,IAAckD,IAChBrI,EAAOmF,UAAYA,EACnBnF,EAAO/B,KAAK,qBA2DhBsK,mBArCF,SAA6B7V,GAC3B,IAAIsN,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAChB2I,EAAQrU,EAAE8D,EAAEC,QAAQoF,QAAS,IAAOuC,EAAiB,YAAI,GACzDkO,GAAa,EACjB,GAAIvF,EACF,IAAK,IAAIvU,EAAI,EAAGA,EAAIsR,EAAOkB,OAAOvS,OAAQD,GAAK,EACzCsR,EAAOkB,OAAOxS,KAAOuU,IAASuF,GAAa,GAInD,IAAIvF,IAASuF,EAUX,OAFAxI,EAAOyI,kBAAerV,OACtB4M,EAAO0I,kBAAetV,GARtB4M,EAAOyI,aAAexF,EAClBjD,EAAOe,SAAWf,EAAO1F,OAAOyG,QAAQC,QAC1ChB,EAAO0I,aAAepI,SAAS1R,EAAEqU,GAAOrS,KAAK,2BAA4B,IAEzEoP,EAAO0I,aAAe9Z,EAAEqU,GAAOxM,QAO/B6D,EAAOqO,0BAA+CvV,IAAxB4M,EAAO0I,cAA8B1I,EAAO0I,eAAiB1I,EAAO8F,aACpG9F,EAAO2I,wBAuFX,IAAIxC,EAAY,CACdjN,aAxEF,SAAuBC,QACP,IAATA,IAAkBA,EAAO9M,KAAK+T,eAAiB,IAAM,KAE1D,IAEI9F,EAFSjO,KAEOiO,OAChBqG,EAHStU,KAGIuU,aACbuF,EAJS9Z,KAIU8Z,UACnB1F,EALSpU,KAKWoU,WAExB,GAAInG,EAAOsO,iBACT,OAAOjI,GAAOwF,EAAYA,EAG5B,IAAI0C,EAAmBlQ,GAAMO,aAAauH,EAAW,GAAItH,GAGzD,OAFIwH,IAAOkI,GAAoBA,GAExBA,GAAoB,GAwD3BC,aArDF,SAAuB3C,EAAW4C,GAChC,IAAI/I,EAAS3T,KACTsU,EAAMX,EAAOY,aACbtG,EAAS0F,EAAO1F,OAChBmG,EAAaT,EAAOS,WACpBmG,EAAW5G,EAAO4G,SAClBoC,EAAI,EACJC,EAAI,EAGJjJ,EAAOI,eACT4I,EAAIrI,GAAOwF,EAAYA,EAEvB8C,EAAI9C,EAGF7L,EAAOuJ,eACTmF,EAAIxG,KAAKC,MAAMuG,GACfC,EAAIzG,KAAKC,MAAMwG,IAGZ3O,EAAOsO,mBACNlN,GAAQU,aAAgBqE,EAAW/O,UAAW,eAAiBsX,EAAI,OAASC,EAAI,YAC7ExI,EAAW/O,UAAW,aAAesX,EAAI,OAASC,EAAI,QAE/DjJ,EAAOkJ,kBAAoBlJ,EAAOmG,UAClCnG,EAAOmG,UAAYnG,EAAOI,eAAiB4I,EAAIC,EAI/C,IAAInC,EAAiB9G,EAAO+G,eAAiB/G,EAAOyG,gBAC7B,IAAnBK,EACY,GAECX,EAAYnG,EAAOyG,gBAAkB,KAElCG,GAClB5G,EAAO6G,eAAeV,GAGxBnG,EAAO/B,KAAK,eAAgB+B,EAAOmG,UAAW4C,IAc9CtC,aAXF,WACE,OAASpa,KAAK+U,SAAS,IAWvB2F,aARF,WACE,OAAS1a,KAAK+U,SAAS/U,KAAK+U,SAASzS,OAAS,KAoFhD,IAAIwa,EAAe,CACjBtD,cA3EF,SAAwB/T,EAAUiX,GACnB1c,KAENoU,WAAW5O,WAAWC,GAFhBzF,KAIN4R,KAAK,gBAAiBnM,EAAUiX,IAuEvCK,gBApEF,SAA0BC,EAAcC,QAChB,IAAjBD,IAA0BA,GAAe,GAE9C,IAAIrJ,EAAS3T,KACTyZ,EAAc9F,EAAO8F,YACrBxL,EAAS0F,EAAO1F,OAChB6N,EAAgBnI,EAAOmI,cACvB7N,EAAOiP,YACTvJ,EAAOyF,mBAGT,IAAI+D,EAAMF,EASV,GARKE,IACgCA,EAAjBrB,EAAdrC,EAAqC,OAChCA,EAAcqC,EAAuB,OACjC,SAGfnI,EAAO/B,KAAK,mBAERoL,GAAgBvD,IAAgBqC,EAAe,CACjD,GAAY,UAARqB,EAEF,YADAxJ,EAAO/B,KAAK,6BAGd+B,EAAO/B,KAAK,8BACA,SAARuL,EACFxJ,EAAO/B,KAAK,4BAEZ+B,EAAO/B,KAAK,8BAwChBzJ,cAnCF,SAA0B6U,EAAcC,QAChB,IAAjBD,IAA0BA,GAAe,GAE9C,IAAIrJ,EAAS3T,KACTyZ,EAAc9F,EAAO8F,YACrBqC,EAAgBnI,EAAOmI,cAC3BnI,EAAOyJ,WAAY,EACnBzJ,EAAO6F,cAAc,GAErB,IAAI2D,EAAMF,EASV,GARKE,IACgCA,EAAjBrB,EAAdrC,EAAqC,OAChCA,EAAcqC,EAAuB,OACjC,SAGfnI,EAAO/B,KAAK,iBAERoL,GAAgBvD,IAAgBqC,EAAe,CACjD,GAAY,UAARqB,EAEF,YADAxJ,EAAO/B,KAAK,2BAGd+B,EAAO/B,KAAK,4BACA,SAARuL,EACFxJ,EAAO/B,KAAK,0BAEZ+B,EAAO/B,KAAK,6BA2QlB,IAAIgF,EAAQ,CACVyG,QAjQF,SAAkBjT,EAAOiP,EAAO2D,EAAcM,QAC7B,IAAVlT,IAAmBA,EAAQ,QACjB,IAAViP,IAAmBA,EAAQrZ,KAAKiO,OAAOoL,YACtB,IAAjB2D,IAA0BA,GAAe,GAE9C,IAAIrJ,EAAS3T,KACTud,EAAanT,EACbmT,EAAa,IAAKA,EAAa,GAEnC,IAAItP,EAAS0F,EAAO1F,OAChB8G,EAAWpB,EAAOoB,SAClBC,EAAarB,EAAOqB,WACpB8G,EAAgBnI,EAAOmI,cACvBrC,EAAc9F,EAAO8F,YACrBnF,EAAMX,EAAOY,aACjB,GAAIZ,EAAOyJ,WAAanP,EAAOuP,+BAC7B,OAAO,EAGT,IAAI1E,EAAY3C,KAAKC,MAAMmH,EAAatP,EAAOiK,gBAC3CY,GAAa/D,EAASzS,SAAUwW,EAAY/D,EAASzS,OAAS,IAE7DmX,GAAexL,EAAOwP,cAAgB,MAAQ3B,GAAiB,IAAMkB,GACxErJ,EAAO/B,KAAK,0BAGd,IAuBIqL,EAvBAnD,GAAa/E,EAAS+D,GAM1B,GAHAnF,EAAO6G,eAAeV,GAGlB7L,EAAOgO,oBACT,IAAK,IAAI5Z,EAAI,EAAGA,EAAI2S,EAAW1S,OAAQD,GAAK,GACrC8T,KAAKC,MAAkB,IAAZ0D,IAAoB3D,KAAKC,MAAsB,IAAhBpB,EAAW3S,MACxDkb,EAAalb,GAKnB,GAAIsR,EAAO+J,aAAeH,IAAe9D,EAAa,CACpD,IAAK9F,EAAOgK,gBAAkB7D,EAAYnG,EAAOmG,WAAaA,EAAYnG,EAAOyG,eAC/E,OAAO,EAET,IAAKzG,EAAOiK,gBAAkB9D,EAAYnG,EAAOmG,WAAaA,EAAYnG,EAAO+G,iBAC1EjB,GAAe,KAAO8D,EAAc,OAAO,EAWpD,OANgCN,EAAfxD,EAAb8D,EAAwC,OACnCA,EAAa9D,EAA2B,OAC9B,QAIdnF,IAAQwF,IAAcnG,EAAOmG,YAAgBxF,GAAOwF,IAAcnG,EAAOmG,WAC5EnG,EAAOiI,kBAAkB2B,GAErBtP,EAAOiP,YACTvJ,EAAOyF,mBAETzF,EAAOoH,sBACe,UAAlB9M,EAAOkK,QACTxE,EAAO8I,aAAa3C,GAEJ,UAAdmD,IACFtJ,EAAOoJ,gBAAgBC,EAAcC,GACrCtJ,EAAOxL,cAAc6U,EAAcC,KAE9B,IAGK,IAAV5D,GAAgBhK,GAAQ7J,YAS1BmO,EAAO6F,cAAcH,GACrB1F,EAAO8I,aAAa3C,GACpBnG,EAAOiI,kBAAkB2B,GACzB5J,EAAOoH,sBACPpH,EAAO/B,KAAK,wBAAyByH,EAAOiE,GAC5C3J,EAAOoJ,gBAAgBC,EAAcC,GAChCtJ,EAAOyJ,YACVzJ,EAAOyJ,WAAY,EACdzJ,EAAOkK,gCACVlK,EAAOkK,8BAAgC,SAAuBxX,GACvDsN,IAAUA,EAAOmK,WAClBzX,EAAEC,SAAWtG,OACjB2T,EAAOS,WAAW,GAAG/T,oBAAoB,gBAAiBsT,EAAOkK,+BACjElK,EAAOS,WAAW,GAAG/T,oBAAoB,sBAAuBsT,EAAOkK,+BACvElK,EAAOkK,8BAAgC,YAChClK,EAAOkK,8BACdlK,EAAOxL,cAAc6U,EAAcC,MAGvCtJ,EAAOS,WAAW,GAAGhU,iBAAiB,gBAAiBuT,EAAOkK,+BAC9DlK,EAAOS,WAAW,GAAGhU,iBAAiB,sBAAuBuT,EAAOkK,kCA5BtElK,EAAO6F,cAAc,GACrB7F,EAAO8I,aAAa3C,GACpBnG,EAAOiI,kBAAkB2B,GACzB5J,EAAOoH,sBACPpH,EAAO/B,KAAK,wBAAyByH,EAAOiE,GAC5C3J,EAAOoJ,gBAAgBC,EAAcC,GACrCtJ,EAAOxL,cAAc6U,EAAcC,KA0B9B,IAwJPc,YArJF,SAAsB3T,EAAOiP,EAAO2D,EAAcM,QACjC,IAAVlT,IAAmBA,EAAQ,QACjB,IAAViP,IAAmBA,EAAQrZ,KAAKiO,OAAOoL,YACtB,IAAjB2D,IAA0BA,GAAe,GAE9C,IACIgB,EAAW5T,EAKf,OANapK,KAEFiO,OAAOkN,OAChB6C,GAHWhe,KAGQie,cAHRje,KAMCqd,QAAQW,EAAU3E,EAAO2D,EAAcM,IA2IrDY,UAvIF,SAAoB7E,EAAO2D,EAAcM,QACxB,IAAVjE,IAAmBA,EAAQrZ,KAAKiO,OAAOoL,YACtB,IAAjB2D,IAA0BA,GAAe,GAE9C,IAAIrJ,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAChBmP,EAAYzJ,EAAOyJ,UACvB,OAAInP,EAAOkN,MACLiC,IACJzJ,EAAOwK,UAEPxK,EAAOyK,YAAczK,EAAOS,WAAW,GAAGjL,WACnCwK,EAAO0J,QAAQ1J,EAAO8F,YAAcxL,EAAOiK,eAAgBmB,EAAO2D,EAAcM,IAElF3J,EAAO0J,QAAQ1J,EAAO8F,YAAcxL,EAAOiK,eAAgBmB,EAAO2D,EAAcM,IA0HvFe,UAtHF,SAAoBhF,EAAO2D,EAAcM,QACxB,IAAVjE,IAAmBA,EAAQrZ,KAAKiO,OAAOoL,YACtB,IAAjB2D,IAA0BA,GAAe,GAE9C,IAAIrJ,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAChBmP,EAAYzJ,EAAOyJ,UACnBrI,EAAWpB,EAAOoB,SAClBC,EAAarB,EAAOqB,WACpBT,EAAeZ,EAAOY,aAE1B,GAAItG,EAAOkN,KAAM,CACf,GAAIiC,EAAa,OAAO,EACxBzJ,EAAOwK,UAEPxK,EAAOyK,YAAczK,EAAOS,WAAW,GAAGjL,WAG5C,SAASmV,EAAUC,GACjB,OAAIA,EAAM,GAAapI,KAAKC,MAAMD,KAAK8B,IAAIsG,IACpCpI,KAAKC,MAAMmI,GAEpB,IAMIC,EANAC,EAAsBH,EALV/J,EAAeZ,EAAOmG,WAAanG,EAAOmG,WAMtD4E,EAAqB3J,EAAS3H,IAAI,SAAUmR,GAAO,OAAOD,EAAUC,KAIpEI,GAHuB3J,EAAW5H,IAAI,SAAUmR,GAAO,OAAOD,EAAUC,KAE1DxJ,EAAS2J,EAAmB5b,QAAQ2b,IACvC1J,EAAS2J,EAAmB5b,QAAQ2b,GAAuB,IAM1E,YAJwB,IAAbE,IACTH,EAAYxJ,EAAWlS,QAAQ6b,IACf,IAAKH,EAAY7K,EAAO8F,YAAc,GAEjD9F,EAAO0J,QAAQmB,EAAWnF,EAAO2D,EAAcM,IAsFtDsB,WAlFF,SAAqBvF,EAAO2D,EAAcM,GAKxC,YAJe,IAAVjE,IAAmBA,EAAQrZ,KAAKiO,OAAOoL,YACtB,IAAjB2D,IAA0BA,GAAe,GAEjChd,KACCqd,QADDrd,KACgByZ,YAAaJ,EAAO2D,EAAcM,IA8E/DuB,eA1EF,SAAyBxF,EAAO2D,EAAcM,QAC7B,IAAVjE,IAAmBA,EAAQrZ,KAAKiO,OAAOoL,YACtB,IAAjB2D,IAA0BA,GAAe,GAE9C,IAAIrJ,EAAS3T,KACToK,EAAQuJ,EAAO8F,YACfX,EAAY3C,KAAKC,MAAMhM,EAAQuJ,EAAO1F,OAAOiK,gBAEjD,GAAIY,EAAYnF,EAAOoB,SAASzS,OAAS,EAAG,CAC1C,IAAIwX,EAAYnG,EAAOY,aAAeZ,EAAOmG,WAAanG,EAAOmG,UAE7DgF,EAAcnL,EAAOoB,SAAS+D,IACnBnF,EAAOoB,SAAS+D,EAAY,GAECgG,GAAe,EAAtDhF,EAAYgF,IACf1U,EAAQuJ,EAAO1F,OAAOiK,gBAI1B,OAAOvE,EAAO0J,QAAQjT,EAAOiP,EAAO2D,EAAcM,IAwDlDhB,oBArDF,WACE,IAMIrB,EANAtH,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAChBmG,EAAaT,EAAOS,WAEpBkC,EAAyC,SAAzBrI,EAAOqI,cAA2B3C,EAAOoL,uBAAyB9Q,EAAOqI,cACzF0I,EAAerL,EAAO0I,aAE1B,GAAIpO,EAAOkN,KAAM,CACf,GAAIxH,EAAOyJ,UAAa,OACxBnC,EAAYhH,SAAS1R,EAAEoR,EAAOyI,cAAc7X,KAAK,2BAA4B,IACzE0J,EAAO+J,eAENgH,EAAerL,EAAOsK,aAAgB3H,EAAgB,GACnD0I,EAAgBrL,EAAOkB,OAAOvS,OAASqR,EAAOsK,aAAiB3H,EAAgB,GAEnF3C,EAAOwK,UACPa,EAAe5K,EACZrT,SAAU,IAAOkN,EAAiB,WAAI,6BAAgCgN,EAAY,WAAehN,EAA0B,oBAAI,KAC/H1D,GAAG,GACHH,QAEHkC,GAAMI,SAAS,WACbiH,EAAO0J,QAAQ2B,MAGjBrL,EAAO0J,QAAQ2B,GAERA,EAAerL,EAAOkB,OAAOvS,OAASgU,GAC/C3C,EAAOwK,UACPa,EAAe5K,EACZrT,SAAU,IAAOkN,EAAiB,WAAI,6BAAgCgN,EAAY,WAAehN,EAA0B,oBAAI,KAC/H1D,GAAG,GACHH,QAEHkC,GAAMI,SAAS,WACbiH,EAAO0J,QAAQ2B,MAGjBrL,EAAO0J,QAAQ2B,QAGjBrL,EAAO0J,QAAQ2B,KA0GnB,IAAI7D,EAAO,CACT8D,WA7FF,WACE,IAAItL,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAChBmG,EAAaT,EAAOS,WAExBA,EAAWrT,SAAU,IAAOkN,EAAiB,WAAI,IAAOA,EAA0B,qBAAI/J,SAEtF,IAAI2Q,EAAST,EAAWrT,SAAU,IAAOkN,EAAiB,YAE1D,GAAIA,EAAOiR,uBAAwB,CACjC,IAAIC,EAAiBlR,EAAOiK,eAAkBrD,EAAOvS,OAAS2L,EAAOiK,eACrE,GAAIiH,IAAmBlR,EAAOiK,eAAgB,CAC5C,IAAK,IAAI7V,EAAI,EAAGA,EAAI8c,EAAgB9c,GAAK,EAAG,CAC1C,IAAI+c,EAAY7c,EAAEtC,EAAIa,cAAc,QAAQ6C,SAAWsK,EAAiB,WAAI,IAAOA,EAAsB,iBACzGmG,EAAW3J,OAAO2U,GAEpBvK,EAAST,EAAWrT,SAAU,IAAOkN,EAAiB,aAI7B,SAAzBA,EAAOqI,eAA6BrI,EAAOgQ,eAAgBhQ,EAAOgQ,aAAepJ,EAAOvS,QAE5FqR,EAAOsK,aAAehK,SAAShG,EAAOgQ,cAAgBhQ,EAAOqI,cAAe,IAC5E3C,EAAOsK,cAAgBhQ,EAAOoR,qBAC1B1L,EAAOsK,aAAepJ,EAAOvS,SAC/BqR,EAAOsK,aAAepJ,EAAOvS,QAG/B,IAAIgd,EAAgB,GAChBC,EAAe,GACnB1K,EAAOhL,KAAK,SAAUO,EAAOlF,GAC3B,IAAI0R,EAAQrU,EAAE2C,GACVkF,EAAQuJ,EAAOsK,cAAgBsB,EAAatc,KAAKiC,GACjDkF,EAAQyK,EAAOvS,QAAU8H,GAASyK,EAAOvS,OAASqR,EAAOsK,cAAgBqB,EAAcrc,KAAKiC,GAChG0R,EAAMrS,KAAK,0BAA2B6F,KAExC,IAAK,IAAIiO,EAAM,EAAGA,EAAMkH,EAAajd,OAAQ+V,GAAO,EAClDjE,EAAW3J,OAAOlI,EAAEgd,EAAalH,GAAKmH,WAAU,IAAO7b,SAASsK,EAAOmN,sBAEzE,IAAK,IAAI7C,EAAM+G,EAAchd,OAAS,EAAU,GAAPiW,EAAUA,GAAO,EACxDnE,EAAWtJ,QAAQvI,EAAE+c,EAAc/G,GAAKiH,WAAU,IAAO7b,SAASsK,EAAOmN,uBAsD3E+C,QAlDF,WACE,IASIH,EATArK,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAChBwL,EAAc9F,EAAO8F,YACrB5E,EAASlB,EAAOkB,OAChBoJ,EAAetK,EAAOsK,aACtBL,EAAiBjK,EAAOiK,eACxBD,EAAiBhK,EAAOgK,eACxB5I,EAAWpB,EAAOoB,SAClBT,EAAMX,EAAOY,aAEjBZ,EAAOiK,gBAAiB,EACxBjK,EAAOgK,gBAAiB,EAExB,IACI8B,GADiB1K,EAAS0E,GACH9F,EAAO9G,eAI9B4M,EAAcwE,GAChBD,EAAYnJ,EAAOvS,OAAyB,EAAf2b,EAAqBxE,EAClDuE,GAAYC,EACOtK,EAAO0J,QAAQW,EAAU,GAAG,GAAO,IACzB,IAATyB,GAClB9L,EAAO8I,cAAcnI,GAAOX,EAAOmG,UAAYnG,EAAOmG,WAAa2F,KAElC,SAAzBxR,EAAOqI,eAA0D,EAAf2H,GAAfxE,GAAqCA,GAAe5E,EAAOvS,OAAS2b,KAEjHD,GAAYnJ,EAAOvS,OAASmX,EAAcwE,EAC1CD,GAAYC,EACStK,EAAO0J,QAAQW,EAAU,GAAG,GAAO,IACzB,IAATyB,GACpB9L,EAAO8I,cAAcnI,GAAOX,EAAOmG,UAAYnG,EAAOmG,WAAa2F,IAGvE9L,EAAOiK,eAAiBA,EACxBjK,EAAOgK,eAAiBA,GAexB+B,YAZF,WACE,IACItL,EADSpU,KACWoU,WACpBnG,EAFSjO,KAEOiO,OAChB4G,EAHS7U,KAGO6U,OACpBT,EAAWrT,SAAU,IAAOkN,EAAiB,WAAI,IAAOA,EAA0B,oBAAI,KAAQA,EAAiB,WAAI,IAAOA,EAAsB,iBAAI/J,SACpJ2Q,EAAO/P,WAAW,6BAyBpB,IAAI6a,EAAa,CACfC,cAjBF,SAAwBC,GAEtB,KAAIxQ,GAAQC,QADCtP,KACgBiO,OAAO6R,eADvB9f,KACgDiO,OAAO8K,eADvD/Y,KAC+E+f,UAA5F,CACA,IAAI7a,EAFSlF,KAEGkF,GAChBA,EAAGjE,MAAM+e,OAAS,OAClB9a,EAAGjE,MAAM+e,OAASH,EAAS,mBAAqB,eAChD3a,EAAGjE,MAAM+e,OAASH,EAAS,eAAiB,YAC5C3a,EAAGjE,MAAM+e,OAASH,EAAS,WAAa,SAWxCI,gBARF,WAEM5Q,GAAQC,OADCtP,KACgBiO,OAAO8K,eADvB/Y,KAC+C+f,WAD/C/f,KAENkF,GAAGjE,MAAM+e,OAAS,MAqK3B,IAAIE,EAAe,CACjBC,YA9JF,SAAsBtL,GACpB,IAAIlB,EAAS3T,KACToU,EAAaT,EAAOS,WACpBnG,EAAS0F,EAAO1F,OAIpB,GAHIA,EAAOkN,MACTxH,EAAO+L,cAEa,iBAAX7K,GAAuB,WAAYA,EAC5C,IAAK,IAAIxS,EAAI,EAAGA,EAAIwS,EAAOvS,OAAQD,GAAK,EAClCwS,EAAOxS,IAAM+R,EAAW3J,OAAOoK,EAAOxS,SAG5C+R,EAAW3J,OAAOoK,GAEhB5G,EAAOkN,MACTxH,EAAOsL,aAEHhR,EAAOiC,UAAYb,GAAQa,UAC/ByD,EAAOJ,UA6IT6M,aAzIF,SAAuBvL,GACrB,IAAIlB,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAChBmG,EAAaT,EAAOS,WACpBqF,EAAc9F,EAAO8F,YAErBxL,EAAOkN,MACTxH,EAAO+L,cAET,IAAI7D,EAAiBpC,EAAc,EACnC,GAAsB,iBAAX5E,GAAuB,WAAYA,EAAQ,CACpD,IAAK,IAAIxS,EAAI,EAAGA,EAAIwS,EAAOvS,OAAQD,GAAK,EAClCwS,EAAOxS,IAAM+R,EAAWtJ,QAAQ+J,EAAOxS,IAE7CwZ,EAAiBpC,EAAc5E,EAAOvS,YAEtC8R,EAAWtJ,QAAQ+J,GAEjB5G,EAAOkN,MACTxH,EAAOsL,aAEHhR,EAAOiC,UAAYb,GAAQa,UAC/ByD,EAAOJ,SAETI,EAAO0J,QAAQxB,EAAgB,GAAG,IAkHlCwE,SA/GF,SAAmBjW,EAAOyK,GACxB,IAAIlB,EAAS3T,KACToU,EAAaT,EAAOS,WACpBnG,EAAS0F,EAAO1F,OAEhBqS,EADc3M,EAAO8F,YAErBxL,EAAOkN,OACTmF,GAAqB3M,EAAOsK,aAC5BtK,EAAO+L,cACP/L,EAAOkB,OAAST,EAAWrT,SAAU,IAAOkN,EAAiB,aAE/D,IAAIsS,EAAa5M,EAAOkB,OAAOvS,OAC/B,GAAI8H,GAAS,EACXuJ,EAAOyM,aAAavL,QAGtB,GAAa0L,GAATnW,EACFuJ,EAAOwM,YAAYtL,OADrB,CAOA,IAHA,IAAIgH,EAAqCzR,EAApBkW,EAA4BA,EAAoB,EAAIA,EAErEE,EAAe,GACVne,EAAIke,EAAa,EAAQnW,GAAL/H,EAAYA,GAAK,EAAG,CAC/C,IAAIoe,EAAe9M,EAAOkB,OAAOtK,GAAGlI,GACpCoe,EAAavc,SACbsc,EAAa/Z,QAAQga,GAGvB,GAAsB,iBAAX5L,GAAuB,WAAYA,EAAQ,CACpD,IAAK,IAAIwD,EAAM,EAAGA,EAAMxD,EAAOvS,OAAQ+V,GAAO,EACxCxD,EAAOwD,IAAQjE,EAAW3J,OAAOoK,EAAOwD,IAE9CwD,EAAqCzR,EAApBkW,EAA4BA,EAAoBzL,EAAOvS,OAASge,OAEjFlM,EAAW3J,OAAOoK,GAGpB,IAAK,IAAI0D,EAAM,EAAGA,EAAMiI,EAAale,OAAQiW,GAAO,EAClDnE,EAAW3J,OAAO+V,EAAajI,IAG7BtK,EAAOkN,MACTxH,EAAOsL,aAEHhR,EAAOiC,UAAYb,GAAQa,UAC/ByD,EAAOJ,SAELtF,EAAOkN,KACTxH,EAAO0J,QAAQxB,EAAiBlI,EAAOsK,aAAc,GAAG,GAExDtK,EAAO0J,QAAQxB,EAAgB,GAAG,KA6DpC6E,YAzDF,SAAsBC,GACpB,IAAIhN,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAChBmG,EAAaT,EAAOS,WAGpBkM,EAFc3M,EAAO8F,YAGrBxL,EAAOkN,OACTmF,GAAqB3M,EAAOsK,aAC5BtK,EAAO+L,cACP/L,EAAOkB,OAAST,EAAWrT,SAAU,IAAOkN,EAAiB,aAE/D,IACI2S,EADA/E,EAAiByE,EAGrB,GAA6B,iBAAlBK,GAA8B,WAAYA,EAAe,CAClE,IAAK,IAAIte,EAAI,EAAGA,EAAIse,EAAcre,OAAQD,GAAK,EAC7Cue,EAAgBD,EAActe,GAC1BsR,EAAOkB,OAAO+L,IAAkBjN,EAAOkB,OAAOtK,GAAGqW,GAAe1c,SAChE0c,EAAgB/E,IAAkBA,GAAkB,GAE1DA,EAAiB1F,KAAKK,IAAIqF,EAAgB,QAE1C+E,EAAgBD,EACZhN,EAAOkB,OAAO+L,IAAkBjN,EAAOkB,OAAOtK,GAAGqW,GAAe1c,SAChE0c,EAAgB/E,IAAkBA,GAAkB,GACxDA,EAAiB1F,KAAKK,IAAIqF,EAAgB,GAGxC5N,EAAOkN,MACTxH,EAAOsL,aAGHhR,EAAOiC,UAAYb,GAAQa,UAC/ByD,EAAOJ,SAELtF,EAAOkN,KACTxH,EAAO0J,QAAQxB,EAAiBlI,EAAOsK,aAAc,GAAG,GAExDtK,EAAO0J,QAAQxB,EAAgB,GAAG,IAmBpCgF,gBAfF,WAIE,IAHA,IAEIF,EAAgB,GACXte,EAAI,EAAGA,EAHHrC,KAGc6U,OAAOvS,OAAQD,GAAK,EAC7Cse,EAAc1d,KAAKZ,GAJRrC,KAMN0gB,YAAYC,KAWjBG,EAAU,WACZ,IAAIzU,EAAK/K,EAAIE,UAAUC,UAEnBsf,EAAS,CACXC,KAAK,EACLC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,QAASlgB,EAAIkgB,SAAWlgB,EAAImgB,SAC5BA,SAAUngB,EAAIkgB,SAAWlgB,EAAImgB,UAG3BL,EAAU/U,EAAGnJ,MAAM,qCACnB+d,EAAU5U,EAAGnJ,MAAM,+BACnBqe,EAAOlV,EAAGnJ,MAAM,wBAChBoe,EAAOjV,EAAGnJ,MAAM,2BAChBme,GAAUE,GAAQlV,EAAGnJ,MAAM,8BA+C/B,GA3CIke,IACFL,EAAOW,GAAK,UACZX,EAAOY,UAAYP,EAAQ,GAC3BL,EAAOK,SAAU,GAGfH,IAAYG,IACdL,EAAOW,GAAK,UACZX,EAAOY,UAAYV,EAAQ,GAC3BF,EAAOE,SAAU,EACjBF,EAAOG,cAAsD,GAAtC7U,EAAGwE,cAAc/N,QAAQ,YAE9Cye,GAAQF,GAAUC,KACpBP,EAAOW,GAAK,MACZX,EAAOC,KAAM,GAGXK,IAAWC,IACbP,EAAOY,UAAYN,EAAO,GAAG/T,QAAQ,KAAM,KAC3CyT,EAAOM,QAAS,GAEdE,IACFR,EAAOY,UAAYJ,EAAK,GAAGjU,QAAQ,KAAM,KACzCyT,EAAOQ,MAAO,GAEZD,IACFP,EAAOY,UAAYL,EAAK,GAAKA,EAAK,GAAGhU,QAAQ,KAAM,KAAO,KAC1DyT,EAAOM,QAAS,GAGdN,EAAOC,KAAOD,EAAOY,WAAuC,GAA1BtV,EAAGvJ,QAAQ,aACR,OAAnCie,EAAOY,UAAUxe,MAAM,KAAK,KAC9B4d,EAAOY,UAAYtV,EAAGwE,cAAc1N,MAAM,YAAY,GAAGA,MAAM,KAAK,IAKxE4d,EAAOI,UAAYJ,EAAOW,IAAMX,EAAOE,SAAWF,EAAOa,SAGzDb,EAAOa,SAAWP,GAAUE,GAAQD,IAASjV,EAAGnJ,MAAM,8BAGlD6d,EAAOW,IAAoB,QAAdX,EAAOW,GAAc,CACpC,IAAIG,EAAed,EAAOY,UAAUxe,MAAM,KACtC2e,EAAe7hB,EAAIQ,cAAc,yBACrCsgB,EAAOgB,WAAahB,EAAOa,UACrBN,GAAQD,KACU,EAAlBQ,EAAa,IAAW,EAA2B,GAAL,EAAlBA,EAAa,GAAoC,EAAJ,EAAlBA,EAAa,KACrEC,GAA8E,GAA9DA,EAAald,aAAa,WAAW9B,QAAQ,cAOpE,OAHAie,EAAOiB,WAAa1gB,EAAI2gB,kBAAoB,EAGrClB,EAhFI,GAsnBb,SAASmB,IACP,IAAIvO,EAAS3T,KAETiO,EAAS0F,EAAO1F,OAChB/I,EAAKyO,EAAOzO,GAEhB,IAAIA,GAAyB,IAAnBA,EAAGyD,YAAb,CAGIsF,EAAOkU,aACTxO,EAAOyO,gBAIT,IAAIzE,EAAiBhK,EAAOgK,eACxBC,EAAiBjK,EAAOiK,eACxB7I,EAAWpB,EAAOoB,SAStB,GANApB,EAAOgK,gBAAiB,EACxBhK,EAAOiK,gBAAiB,EAExBjK,EAAOH,aACPG,EAAOQ,eAEHlG,EAAOoU,SAAU,CACnB,IAAIC,EAAenM,KAAKoM,IAAIpM,KAAKK,IAAI7C,EAAOmG,UAAWnG,EAAO+G,gBAAiB/G,EAAOyG,gBACtFzG,EAAO8I,aAAa6F,GACpB3O,EAAOiI,oBACPjI,EAAOoH,sBAEH9M,EAAOiP,YACTvJ,EAAOyF,wBAGTzF,EAAOoH,uBACuB,SAAzB9M,EAAOqI,eAAmD,EAAvBrI,EAAOqI,gBAAsB3C,EAAOiH,QAAUjH,EAAO1F,OAAO+J,eAClGrE,EAAO0J,QAAQ1J,EAAOkB,OAAOvS,OAAS,EAAG,GAAG,GAAO,GAEnDqR,EAAO0J,QAAQ1J,EAAO8F,YAAa,GAAG,GAAO,GAIjD9F,EAAOiK,eAAiBA,EACxBjK,EAAOgK,eAAiBA,EAEpBhK,EAAO1F,OAAO8K,eAAiBhE,IAAapB,EAAOoB,UACrDpB,EAAOqF,iBA6TX,IAEIwJ,EAAW,CACbC,MAAM,EACNxF,UAAW,aACXyF,kBAAmB,YACnBjF,aAAc,EACdpE,MAAO,IAEPmE,gCAAgC,EAGhCmF,oBAAoB,EACpBC,mBAAoB,GAGpBP,UAAU,EACVQ,kBAAkB,EAClBC,sBAAuB,EACvBC,wBAAwB,EACxBC,4BAA6B,EAC7BC,8BAA+B,EAC/BC,gBAAgB,EAChBC,wBAAyB,IAGzBjG,YAAY,EAGZ9E,gBAAgB,EAGhBmE,kBAAkB,EAGlBpE,OAAQ,QAGRgK,iBAAapb,EACbqc,oBAAoB,EAGpB5N,aAAc,EACdc,cAAe,EACfJ,gBAAiB,EACjBK,oBAAqB,SACrB2B,eAAgB,EAChBF,gBAAgB,EAChB7C,mBAAoB,EACpBE,kBAAmB,EACnB4G,qBAAqB,EACrBxD,0BAA0B,EAG1BM,eAAe,EAGfvB,cAAc,EAGd6L,WAAY,EACZC,WAAY,GACZxD,eAAe,EACfyD,aAAa,EACbC,YAAY,EACZC,gBAAiB,GACjBC,aAAc,IACdC,cAAc,EACdC,gBAAgB,EAChBC,UAAW,EACXC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,+BAA+B,EAC/BC,qBAAqB,EAGrBC,mBAAmB,EAGnBC,YAAY,EACZC,gBAAiB,IAGjBnL,qBAAqB,EACrBC,uBAAuB,EAGvByG,YAAY,EAGZ0E,eAAe,EACfC,0BAA0B,EAC1BhI,qBAAqB,EAGrBiI,eAAe,EACfC,qBAAqB,EAGrBrJ,MAAM,EACNkE,qBAAsB,EACtBpB,aAAc,KACdiB,wBAAwB,EAGxBtB,gBAAgB,EAChBD,gBAAgB,EAChB8G,aAAc,KACdC,WAAW,EACXC,eAAgB,oBAChBC,kBAAmB,KAGnBC,kBAAkB,EAGlBC,uBAAwB,oBACxBC,WAAY,eACZC,gBAAiB,+BACjB9J,iBAAkB,sBAClBG,0BAA2B,gCAC3BrB,kBAAmB,uBACnBoB,oBAAqB,yBACrBG,eAAgB,oBAChBG,wBAAyB,8BACzBD,eAAgB,oBAChBE,wBAAyB,8BACzBsJ,aAAc,iBAGdC,oBAAoB,GAKlBC,EAAa,CACf5R,OAAQA,EACRuG,UAAWA,EACXtU,WAAYsX,EACZlG,MAAOA,EACPuE,KAAMA,EACNwE,WAAYA,EACZO,aAAcA,EACdlZ,OAtWW,CACXoe,aAxFF,WACE,IAAIzR,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAChBoX,EAAc1R,EAAO0R,YACrBngB,EAAKyO,EAAOzO,GACZogB,EAAY3R,EAAO2R,UAGrB3R,EAAO4R,aAzmBX,SAAuBne,GACrB,IAAIuM,EAAS3T,KACTgF,EAAO2O,EAAO6R,gBACdvX,EAAS0F,EAAO1F,OAChBwX,EAAU9R,EAAO8R,QACrB,IAAI9R,EAAOyJ,YAAanP,EAAOuP,+BAA/B,CAGA,IAAInX,EAAIe,EAGR,GAFIf,EAAEqf,gBAAiBrf,EAAIA,EAAEqf,eAC7B1gB,EAAK2gB,aAA0B,eAAXtf,EAAEuf,MACjB5gB,EAAK2gB,gBAAgB,UAAWtf,IAAiB,IAAZA,EAAEwf,WACvC7gB,EAAK2gB,cAAgB,WAAYtf,GAAgB,EAAXA,EAAEyf,QACzC9gB,EAAK+gB,WAAa/gB,EAAKghB,SAC3B,GAAI/X,EAAOyW,WAAaniB,EAAE8D,EAAEC,QAAQoF,QAAQuC,EAAO2W,kBAAoB3W,EAAO2W,kBAAqB,IAAO3W,EAAqB,gBAAI,GACjI0F,EAAOsS,YAAa,OAGtB,IAAIhY,EAAOwW,cACJliB,EAAE8D,GAAGqF,QAAQuC,EAAOwW,cAAc,GADzC,CAIAgB,EAAQS,SAAsB,eAAX7f,EAAEuf,KAAwBvf,EAAE8f,cAAc,GAAGC,MAAQ/f,EAAE+f,MAC1EX,EAAQY,SAAsB,eAAXhgB,EAAEuf,KAAwBvf,EAAE8f,cAAc,GAAGG,MAAQjgB,EAAEigB,MAC1E,IAAIC,EAASd,EAAQS,SACjBM,EAASf,EAAQY,SAIjB1D,EAAqB1U,EAAO0U,oBAAsB1U,EAAOwY,sBACzD7D,EAAqB3U,EAAO2U,oBAAsB3U,EAAOyY,sBAC7D,IACE/D,KACK4D,GAAU3D,GACX2D,GAAUjlB,EAAIU,OAAOyR,MAAQmP,GAHnC,CAuBA,GAfAtW,GAAMqC,OAAO3J,EAAM,CACjB+gB,WAAW,EACXC,SAAS,EACTW,qBAAqB,EACrBC,iBAAa7f,EACb8f,iBAAa9f,IAGf0e,EAAQc,OAASA,EACjBd,EAAQe,OAASA,EACjBxhB,EAAK8hB,eAAiBxa,GAAMM,MAC5B+G,EAAOsS,YAAa,EACpBtS,EAAOH,aACPG,EAAOoT,oBAAiBhgB,EACD,EAAnBkH,EAAO4V,YAAiB7e,EAAKgiB,oBAAqB,GACvC,eAAX3gB,EAAEuf,KAAuB,CAC3B,IAAIqB,GAAiB,EACjB1kB,EAAE8D,EAAEC,QAAQI,GAAG1B,EAAKkiB,gBAAiBD,GAAiB,GAExDhnB,EAAIK,eACDiC,EAAEtC,EAAIK,eAAeoG,GAAG1B,EAAKkiB,eAC7BjnB,EAAIK,gBAAkB+F,EAAEC,QAE3BrG,EAAIK,cAAcC,OAGpB,IAAI4mB,EAAuBF,GAAkBtT,EAAOiQ,gBAAkB3V,EAAO8V,0BACzE9V,EAAO+V,+BAAiCmD,IAC1C9gB,EAAE4gB,iBAGNtT,EAAO/B,KAAK,aAAcvL,OAmiBWqM,KAAKiB,GACxCA,EAAOyT,YAjiBX,SAAsBhgB,GACpB,IAAIuM,EAAS3T,KACTgF,EAAO2O,EAAO6R,gBACdvX,EAAS0F,EAAO1F,OAChBwX,EAAU9R,EAAO8R,QACjBnR,EAAMX,EAAOY,aACblO,EAAIe,EAER,GADIf,EAAEqf,gBAAiBrf,EAAIA,EAAEqf,eACxB1gB,EAAK+gB,WAMV,IAAI/gB,EAAK2gB,cAA2B,cAAXtf,EAAEuf,KAA3B,CACA,IAAIQ,EAAmB,cAAX/f,EAAEuf,KAAuBvf,EAAE8f,cAAc,GAAGC,MAAQ/f,EAAE+f,MAC9DE,EAAmB,cAAXjgB,EAAEuf,KAAuBvf,EAAE8f,cAAc,GAAGG,MAAQjgB,EAAEigB,MAClE,GAAIjgB,EAAEghB,wBAGJ,OAFA5B,EAAQc,OAASH,OACjBX,EAAQe,OAASF,GAGnB,IAAK3S,EAAOiQ,eAYV,OAVAjQ,EAAOsS,YAAa,OAChBjhB,EAAK+gB,YACPzZ,GAAMqC,OAAO8W,EAAS,CACpBc,OAAQH,EACRI,OAAQF,EACRJ,SAAUE,EACVC,SAAUC,IAEZthB,EAAK8hB,eAAiBxa,GAAMM,QAIhC,GAAI5H,EAAK2gB,cAAgB1X,EAAOgW,sBAAwBhW,EAAOkN,KAC7D,GAAIxH,EAAOK,cAET,GACGsS,EAAQb,EAAQe,QAAU7S,EAAOmG,WAAanG,EAAO+G,gBAClD4L,EAAQb,EAAQe,QAAU7S,EAAOmG,WAAanG,EAAOyG,eAIzD,OAFApV,EAAK+gB,WAAY,OACjB/gB,EAAKghB,SAAU,QAGZ,GACJI,EAAQX,EAAQc,QAAU5S,EAAOmG,WAAanG,EAAO+G,gBAClD0L,EAAQX,EAAQc,QAAU5S,EAAOmG,WAAanG,EAAOyG,eAEzD,OAGJ,GAAIpV,EAAK2gB,cAAgB1lB,EAAIK,eACvB+F,EAAEC,SAAWrG,EAAIK,eAAiBiC,EAAE8D,EAAEC,QAAQI,GAAG1B,EAAKkiB,cAGxD,OAFAliB,EAAKghB,SAAU,OACfrS,EAAOsS,YAAa,GAOxB,GAHIjhB,EAAK2hB,qBACPhT,EAAO/B,KAAK,YAAavL,KAEvBA,EAAE8f,eAA0C,EAAzB9f,EAAE8f,cAAc7jB,QAAvC,CAEAmjB,EAAQS,SAAWE,EACnBX,EAAQY,SAAWC,EAEnB,IAKMhD,EALFgE,EAAQ7B,EAAQS,SAAWT,EAAQc,OACnCgB,EAAQ9B,EAAQY,SAAWZ,EAAQe,OACvC,KAAI7S,EAAO1F,OAAO4V,WAAa1N,KAAKqR,KAAMrR,KAAKsR,IAAKH,EAAO,GAAQnR,KAAKsR,IAAKF,EAAO,IAAQ5T,EAAO1F,OAAO4V,WAsB1G,QApBgC,IAArB7e,EAAK4hB,cAETjT,EAAOI,gBAAkB0R,EAAQY,WAAaZ,EAAQe,QAAY7S,EAAOK,cAAgByR,EAAQS,WAAaT,EAAQc,OACzHvhB,EAAK4hB,aAAc,EAGsB,IAApCU,EAAQA,EAAUC,EAAQA,IAC7BjE,EAA6D,IAA/CnN,KAAKuR,MAAMvR,KAAK8B,IAAIsP,GAAQpR,KAAK8B,IAAIqP,IAAiBnR,KAAKwR,GACzE3iB,EAAK4hB,YAAcjT,EAAOI,eAAiBuP,EAAarV,EAAOqV,WAAc,GAAKA,EAAarV,EAAOqV,aAIxGte,EAAK4hB,aACPjT,EAAO/B,KAAK,oBAAqBvL,QAEH,IAArBrB,EAAK6hB,cACVpB,EAAQS,WAAaT,EAAQc,QAAUd,EAAQY,WAAaZ,EAAQe,SACtExhB,EAAK6hB,aAAc,IAGnB7hB,EAAK4hB,YACP5hB,EAAK+gB,WAAY,OAGnB,GAAK/gB,EAAK6hB,YAAV,CAGAlT,EAAOsS,YAAa,EACpB5f,EAAE4gB,iBACEhZ,EAAO6V,2BAA6B7V,EAAO2Z,QAC7CvhB,EAAEwhB,kBAGC7iB,EAAKghB,UACJ/X,EAAOkN,MACTxH,EAAOwK,UAETnZ,EAAK8iB,eAAiBnU,EAAO9G,eAC7B8G,EAAO6F,cAAc,GACjB7F,EAAOyJ,WACTzJ,EAAOS,WAAWzM,QAAQ,qCAE5B3C,EAAK+iB,qBAAsB,GAEvB9Z,EAAO0R,aAAyC,IAA1BhM,EAAOgK,iBAAqD,IAA1BhK,EAAOiK,gBACjEjK,EAAOiM,eAAc,GAEvBjM,EAAO/B,KAAK,kBAAmBvL,IAEjCsN,EAAO/B,KAAK,aAAcvL,GAC1BrB,EAAKghB,SAAU,EAEf,IAAIvG,EAAO9L,EAAOI,eAAiBuT,EAAQC,EAC3C9B,EAAQhG,KAAOA,EAEfA,GAAQxR,EAAOoV,WACX/O,IAAOmL,GAAQA,GAEnB9L,EAAOoT,eAAwB,EAAPtH,EAAW,OAAS,OAC5Cza,EAAKwX,iBAAmBiD,EAAOza,EAAK8iB,eAEpC,IAAIE,GAAsB,EACtB5D,EAAkBnW,EAAOmW,gBA0B7B,GAzBInW,EAAOgW,sBACTG,EAAkB,GAER,EAAP3E,GAAYza,EAAKwX,iBAAmB7I,EAAOyG,gBAC9C4N,GAAsB,EAClB/Z,EAAOkW,aAAcnf,EAAKwX,iBAAoB7I,EAAOyG,eAAiB,EAAMjE,KAAKsR,KAAO9T,EAAOyG,eAAiBpV,EAAK8iB,eAAiBrI,EAAO2E,KACxI3E,EAAO,GAAKza,EAAKwX,iBAAmB7I,EAAO+G,iBACpDsN,GAAsB,EAClB/Z,EAAOkW,aAAcnf,EAAKwX,iBAAoB7I,EAAO+G,eAAiB,EAAMvE,KAAKsR,IAAM9T,EAAO+G,eAAiB1V,EAAK8iB,eAAiBrI,EAAO2E,KAG9I4D,IACF3hB,EAAEghB,yBAA0B,IAIzB1T,EAAOgK,gBAA4C,SAA1BhK,EAAOoT,gBAA6B/hB,EAAKwX,iBAAmBxX,EAAK8iB,iBAC7F9iB,EAAKwX,iBAAmBxX,EAAK8iB,iBAE1BnU,EAAOiK,gBAA4C,SAA1BjK,EAAOoT,gBAA6B/hB,EAAKwX,iBAAmBxX,EAAK8iB,iBAC7F9iB,EAAKwX,iBAAmBxX,EAAK8iB,gBAKR,EAAnB7Z,EAAO4V,UAAe,CACxB,KAAI1N,KAAK8B,IAAIwH,GAAQxR,EAAO4V,WAAa7e,EAAKgiB,oBAW5C,YADAhiB,EAAKwX,iBAAmBxX,EAAK8iB,gBAT7B,IAAK9iB,EAAKgiB,mBAMR,OALAhiB,EAAKgiB,oBAAqB,EAC1BvB,EAAQc,OAASd,EAAQS,SACzBT,EAAQe,OAASf,EAAQY,SACzBrhB,EAAKwX,iBAAmBxX,EAAK8iB,oBAC7BrC,EAAQhG,KAAO9L,EAAOI,eAAiB0R,EAAQS,SAAWT,EAAQc,OAASd,EAAQY,SAAWZ,EAAQe,QASvGvY,EAAO0V,gBAGR1V,EAAOoU,UAAYpU,EAAOgL,qBAAuBhL,EAAOiL,yBAC1DvF,EAAOiI,oBACPjI,EAAOoH,uBAEL9M,EAAOoU,WAEsB,IAA3Brd,EAAKijB,WAAW3lB,QAClB0C,EAAKijB,WAAWhlB,KAAK,CACnBilB,SAAUzC,EAAQ9R,EAAOI,eAAiB,SAAW,UACrDoU,KAAMnjB,EAAK8hB,iBAGf9hB,EAAKijB,WAAWhlB,KAAK,CACnBilB,SAAUzC,EAAQ9R,EAAOI,eAAiB,WAAa,YACvDoU,KAAM7b,GAAMM,SAIhB+G,EAAO6G,eAAexV,EAAKwX,kBAE3B7I,EAAO8I,aAAazX,EAAKwX,2BA/LnBxX,EAAK6hB,aAAe7hB,EAAK4hB,aAC3BjT,EAAO/B,KAAK,oBAAqBvL,IAuhBFqM,KAAKiB,GACtCA,EAAOyU,WAvVX,SAAqBhhB,GACnB,IAAIuM,EAAS3T,KACTgF,EAAO2O,EAAO6R,gBAEdvX,EAAS0F,EAAO1F,OAChBwX,EAAU9R,EAAO8R,QACjBnR,EAAMX,EAAOY,aACbH,EAAaT,EAAOS,WACpBY,EAAarB,EAAOqB,WACpBD,EAAWpB,EAAOoB,SAClB1O,EAAIe,EAMR,GALIf,EAAEqf,gBAAiBrf,EAAIA,EAAEqf,eACzB1gB,EAAK2hB,qBACPhT,EAAO/B,KAAK,WAAYvL,GAE1BrB,EAAK2hB,qBAAsB,GACtB3hB,EAAK+gB,UAMR,OALI/gB,EAAKghB,SAAW/X,EAAO0R,YACzBhM,EAAOiM,eAAc,GAEvB5a,EAAKghB,SAAU,OACfhhB,EAAK6hB,aAAc,GAIjB5Y,EAAO0R,YAAc3a,EAAKghB,SAAWhhB,EAAK+gB,aAAwC,IAA1BpS,EAAOgK,iBAAqD,IAA1BhK,EAAOiK,iBACnGjK,EAAOiM,eAAc,GAIvB,IAmCIyI,EAnCAC,EAAehc,GAAMM,MACrB2b,EAAWD,EAAetjB,EAAK8hB,eAwBnC,GArBInT,EAAOsS,aACTtS,EAAOuI,mBAAmB7V,GAC1BsN,EAAO/B,KAAK,MAAOvL,GACfkiB,EAAW,KAA6C,IAArCD,EAAetjB,EAAKwjB,gBACrCxjB,EAAKyjB,cAAgBvmB,aAAa8C,EAAKyjB,cAC3CzjB,EAAKyjB,aAAenc,GAAMI,SAAS,WAC5BiH,IAAUA,EAAOmK,WACtBnK,EAAO/B,KAAK,QAASvL,IACpB,MAEDkiB,EAAW,KAAQD,EAAetjB,EAAKwjB,cAAiB,MACtDxjB,EAAKyjB,cAAgBvmB,aAAa8C,EAAKyjB,cAC3C9U,EAAO/B,KAAK,YAAavL,KAI7BrB,EAAKwjB,cAAgBlc,GAAMM,MAC3BN,GAAMI,SAAS,WACRiH,EAAOmK,YAAanK,EAAOsS,YAAa,MAG1CjhB,EAAK+gB,YAAc/gB,EAAKghB,UAAYrS,EAAOoT,gBAAmC,IAAjBtB,EAAQhG,MAAcza,EAAKwX,mBAAqBxX,EAAK8iB,eAIrH,OAHA9iB,EAAK+gB,WAAY,EACjB/gB,EAAKghB,SAAU,OACfhhB,EAAK6hB,aAAc,GAcrB,GAXA7hB,EAAK+gB,WAAY,EACjB/gB,EAAKghB,SAAU,EACfhhB,EAAK6hB,aAAc,EAIjBwB,EADEpa,EAAO0V,aACIrP,EAAMX,EAAOmG,WAAanG,EAAOmG,WAEhC9U,EAAKwX,iBAGjBvO,EAAOoU,SAAX,CACE,GAAIgG,GAAc1U,EAAOyG,eAEvB,YADAzG,EAAO0J,QAAQ1J,EAAO8F,aAGxB,GAAI4O,GAAc1U,EAAO+G,eAMvB,YALI/G,EAAOkB,OAAOvS,OAASyS,EAASzS,OAClCqR,EAAO0J,QAAQtI,EAASzS,OAAS,GAEjCqR,EAAO0J,QAAQ1J,EAAOkB,OAAOvS,OAAS,IAK1C,GAAI2L,EAAO4U,iBAAkB,CAC3B,GAA6B,EAAzB7d,EAAKijB,WAAW3lB,OAAY,CAC9B,IAAIomB,EAAgB1jB,EAAKijB,WAAWU,MAChCC,EAAgB5jB,EAAKijB,WAAWU,MAEhCE,EAAWH,EAAcR,SAAWU,EAAcV,SAClDC,EAAOO,EAAcP,KAAOS,EAAcT,KAC9CxU,EAAOmV,SAAWD,EAAWV,EAC7BxU,EAAOmV,UAAY,EACf3S,KAAK8B,IAAItE,EAAOmV,UAAY7a,EAAOkV,0BACrCxP,EAAOmV,SAAW,IAIT,IAAPX,GAAmD,IAApC7b,GAAMM,MAAQ8b,EAAcP,QAC7CxU,EAAOmV,SAAW,QAGpBnV,EAAOmV,SAAW,EAEpBnV,EAAOmV,UAAY7a,EAAOgV,8BAE1Bje,EAAKijB,WAAW3lB,OAAS,EACzB,IAAIymB,EAAmB,IAAO9a,EAAO6U,sBACjCkG,EAAmBrV,EAAOmV,SAAWC,EAErCE,EAActV,EAAOmG,UAAYkP,EACjC1U,IAAO2U,GAAeA,GAE1B,IACIC,EAEAC,EAHAC,GAAW,EAEXC,EAA2C,GAA5BlT,KAAK8B,IAAItE,EAAOmV,UAAiB7a,EAAO+U,4BAE3D,GAAIiG,EAActV,EAAO+G,eACnBzM,EAAO8U,wBACLkG,EAActV,EAAO+G,gBAAkB2O,IACzCJ,EAActV,EAAO+G,eAAiB2O,GAExCH,EAAsBvV,EAAO+G,eAC7B0O,GAAW,EACXpkB,EAAK+iB,qBAAsB,GAE3BkB,EAActV,EAAO+G,eAEnBzM,EAAOkN,MAAQlN,EAAO+J,iBAAkBmR,GAAe,QACtD,GAAIF,EAActV,EAAOyG,eAC1BnM,EAAO8U,wBACLkG,EAActV,EAAOyG,eAAiBiP,IACxCJ,EAActV,EAAOyG,eAAiBiP,GAExCH,EAAsBvV,EAAOyG,eAC7BgP,GAAW,EACXpkB,EAAK+iB,qBAAsB,GAE3BkB,EAActV,EAAOyG,eAEnBnM,EAAOkN,MAAQlN,EAAO+J,iBAAkBmR,GAAe,QACtD,GAAIlb,EAAOiV,eAAgB,CAEhC,IADA,IAAI5H,EACKxX,EAAI,EAAGA,EAAIiR,EAASzS,OAAQwB,GAAK,EACxC,GAAIiR,EAASjR,IAAMmlB,EAAa,CAC9B3N,EAAYxX,EACZ,MASJmlB,IAJEA,EADE9S,KAAK8B,IAAIlD,EAASuG,GAAa2N,GAAe9S,KAAK8B,IAAIlD,EAASuG,EAAY,GAAK2N,IAA0C,SAA1BtV,EAAOoT,eAC5FhS,EAASuG,GAETvG,EAASuG,EAAY,IAUvC,GANI6N,GACFxV,EAAOnC,KAAK,gBAAiB,WAC3BmC,EAAOwK,YAIa,IAApBxK,EAAOmV,SAEPC,EADEzU,EACiB6B,KAAK8B,MAAMgR,EAActV,EAAOmG,WAAanG,EAAOmV,UAEpD3S,KAAK8B,KAAKgR,EAActV,EAAOmG,WAAanG,EAAOmV,eAEnE,GAAI7a,EAAOiV,eAEhB,YADAvP,EAAOkL,iBAIL5Q,EAAO8U,wBAA0BqG,GACnCzV,EAAO6G,eAAe0O,GACtBvV,EAAO6F,cAAcuP,GACrBpV,EAAO8I,aAAawM,GACpBtV,EAAOoJ,iBAAgB,EAAMpJ,EAAOoT,gBACpCpT,EAAOyJ,WAAY,EACnBhJ,EAAWjM,cAAc,WAClBwL,IAAUA,EAAOmK,WAAc9Y,EAAK+iB,sBACzCpU,EAAO/B,KAAK,kBAEZ+B,EAAO6F,cAAcvL,EAAOoL,OAC5B1F,EAAO8I,aAAayM,GACpB9U,EAAWjM,cAAc,WAClBwL,IAAUA,EAAOmK,WACtBnK,EAAOxL,sBAGFwL,EAAOmV,UAChBnV,EAAO6G,eAAeyO,GACtBtV,EAAO6F,cAAcuP,GACrBpV,EAAO8I,aAAawM,GACpBtV,EAAOoJ,iBAAgB,EAAMpJ,EAAOoT,gBAC/BpT,EAAOyJ,YACVzJ,EAAOyJ,WAAY,EACnBhJ,EAAWjM,cAAc,WAClBwL,IAAUA,EAAOmK,WACtBnK,EAAOxL,oBAIXwL,EAAO6G,eAAeyO,GAGxBtV,EAAOiI,oBACPjI,EAAOoH,2BACF,GAAI9M,EAAOiV,eAEhB,YADAvP,EAAOkL,mBAIJ5Q,EAAO4U,kBAAoB0F,GAAYta,EAAOyV,gBACjD/P,EAAO6G,iBACP7G,EAAOiI,oBACPjI,EAAOoH,2BAnJX,CA2JA,IAFA,IAAIuO,EAAY,EACZC,EAAY5V,EAAOsB,gBAAgB,GAC9B5S,EAAI,EAAGA,EAAI2S,EAAW1S,OAAQD,GAAK4L,EAAOiK,oBACI,IAA1ClD,EAAW3S,EAAI4L,EAAOiK,gBAC3BmQ,GAAcrT,EAAW3S,IAAMgmB,EAAarT,EAAW3S,EAAI4L,EAAOiK,kBAEpEqR,EAAYvU,GADZsU,EAAYjnB,GACe4L,EAAOiK,gBAAkBlD,EAAW3S,IAExDgmB,GAAcrT,EAAW3S,KAClCinB,EAAYjnB,EACZknB,EAAYvU,EAAWA,EAAW1S,OAAS,GAAK0S,EAAWA,EAAW1S,OAAS,IAKnF,IAAIknB,GAASnB,EAAarT,EAAWsU,IAAcC,EAEnD,GAAIhB,EAAWta,EAAOyV,aAAc,CAElC,IAAKzV,EAAOuV,WAEV,YADA7P,EAAO0J,QAAQ1J,EAAO8F,aAGM,SAA1B9F,EAAOoT,iBACLyC,GAASvb,EAAOwV,gBAAmB9P,EAAO0J,QAAQiM,EAAYrb,EAAOiK,gBAClEvE,EAAO0J,QAAQiM,IAEM,SAA1B3V,EAAOoT,iBACLyC,EAAS,EAAIvb,EAAOwV,gBAAoB9P,EAAO0J,QAAQiM,EAAYrb,EAAOiK,gBACvEvE,EAAO0J,QAAQiM,QAEnB,CAEL,IAAKrb,EAAOsV,YAEV,YADA5P,EAAO0J,QAAQ1J,EAAO8F,aAGM,SAA1B9F,EAAOoT,gBACTpT,EAAO0J,QAAQiM,EAAYrb,EAAOiK,gBAEN,SAA1BvE,EAAOoT,gBACTpT,EAAO0J,QAAQiM,MA6Ec5W,KAAKiB,GAGtCA,EAAO8V,QAxBT,SAAkBpjB,GACHrG,KACDimB,aADCjmB,KAEAiO,OAAOoW,eAAiBhe,EAAE4gB,iBAF1BjnB,KAGAiO,OAAOqW,0BAHPtkB,KAG0Cod,YACnD/W,EAAEwhB,kBACFxhB,EAAEqjB,8BAkBmBhX,KAAKiB,GAE9B,IAAIrN,EAAsC,cAA7B2H,EAAOyU,kBAAoCxd,EAAKogB,EACzDnf,IAAY8H,EAAO2Z,OAIrB,GAAKvY,GAAQC,QAAUD,GAAQK,gBAAiBL,GAAQQ,sBAIjD,CACL,GAAIR,GAAQC,MAAO,CACjB,IAAIa,IAAwC,eAAtBkV,EAAYsE,QAA0Bta,GAAQc,kBAAmBlC,EAAO4W,mBAAmB,CAAE+E,SAAS,EAAMzjB,SAAS,GAC3IG,EAAOlG,iBAAiBilB,EAAYsE,MAAOhW,EAAO4R,aAAcpV,GAChE7J,EAAOlG,iBAAiBilB,EAAYwE,KAAMlW,EAAOyT,YAAa/X,GAAQc,gBAAkB,CAAEyZ,SAAS,EAAOzjB,QAASA,GAAYA,GAC/HG,EAAOlG,iBAAiBilB,EAAYyE,IAAKnW,EAAOyU,WAAYjY,IAEzDlC,EAAO6R,gBAAkBgB,EAAOE,MAAQF,EAAOG,SAAahT,EAAO6R,gBAAkBzQ,GAAQC,OAASwR,EAAOE,OAChH1a,EAAOlG,iBAAiB,YAAauT,EAAO4R,cAAc,GAC1DtlB,EAAIG,iBAAiB,YAAauT,EAAOyT,YAAajhB,GACtDlG,EAAIG,iBAAiB,UAAWuT,EAAOyU,YAAY,SAbrD9hB,EAAOlG,iBAAiBilB,EAAYsE,MAAOhW,EAAO4R,cAAc,GAChEtlB,EAAIG,iBAAiBilB,EAAYwE,KAAMlW,EAAOyT,YAAajhB,GAC3DlG,EAAIG,iBAAiBilB,EAAYyE,IAAKnW,EAAOyU,YAAY,IAevDna,EAAOoW,eAAiBpW,EAAOqW,2BACjChe,EAAOlG,iBAAiB,QAASuT,EAAO8V,SAAS,GAKrD9V,EAAO/N,GAAIkb,EAAOE,KAAOF,EAAOG,QAAU,0CAA4C,wBAA0BiB,GAAU,IA6C1H6H,aA1CF,WACE,IAAIpW,EAAS3T,KAETiO,EAAS0F,EAAO1F,OAChBoX,EAAc1R,EAAO0R,YACrBngB,EAAKyO,EAAOzO,GACZogB,EAAY3R,EAAO2R,UAEnBhf,EAAsC,cAA7B2H,EAAOyU,kBAAoCxd,EAAKogB,EACzDnf,IAAY8H,EAAO2Z,OAIrB,GAAKvY,GAAQC,QAAUD,GAAQK,gBAAiBL,GAAQQ,sBAIjD,CACL,GAAIR,GAAQC,MAAO,CACjB,IAAIa,IAAwC,iBAAtBkV,EAAYsE,QAA4Bta,GAAQc,kBAAmBlC,EAAO4W,mBAAmB,CAAE+E,SAAS,EAAMzjB,SAAS,GAC7IG,EAAOjG,oBAAoBglB,EAAYsE,MAAOhW,EAAO4R,aAAcpV,GACnE7J,EAAOjG,oBAAoBglB,EAAYwE,KAAMlW,EAAOyT,YAAajhB,GACjEG,EAAOjG,oBAAoBglB,EAAYyE,IAAKnW,EAAOyU,WAAYjY,IAE5DlC,EAAO6R,gBAAkBgB,EAAOE,MAAQF,EAAOG,SAAahT,EAAO6R,gBAAkBzQ,GAAQC,OAASwR,EAAOE,OAChH1a,EAAOjG,oBAAoB,YAAasT,EAAO4R,cAAc,GAC7DtlB,EAAII,oBAAoB,YAAasT,EAAOyT,YAAajhB,GACzDlG,EAAII,oBAAoB,UAAWsT,EAAOyU,YAAY,SAbxD9hB,EAAOjG,oBAAoBglB,EAAYsE,MAAOhW,EAAO4R,cAAc,GACnEtlB,EAAII,oBAAoBglB,EAAYwE,KAAMlW,EAAOyT,YAAajhB,GAC9DlG,EAAII,oBAAoBglB,EAAYyE,IAAKnW,EAAOyU,YAAY,IAe1Dna,EAAOoW,eAAiBpW,EAAOqW,2BACjChe,EAAOjG,oBAAoB,QAASsT,EAAO8V,SAAS,GAKxD9V,EAAOrM,IAAKwZ,EAAOE,KAAOF,EAAOG,QAAU,0CAA4C,wBAA0BiB,KA0WjHC,YAlRgB,CAAEC,cAhFpB,WACE,IAAIzO,EAAS3T,KACTyZ,EAAc9F,EAAO8F,YACrBiE,EAAc/J,EAAO+J,YACrBO,EAAetK,EAAOsK,kBAAoC,IAAjBA,IAA0BA,EAAe,GACtF,IAAIhQ,EAAS0F,EAAO1F,OAChBkU,EAAclU,EAAOkU,YACzB,GAAKA,KAAgBA,GAAmD,IAApCnW,OAAOC,KAAKkW,GAAa7f,QAA7D,CAGA,IAAI0nB,EAAarW,EAAOsW,cAAc9H,GAEtC,GAAI6H,GAAcrW,EAAOuW,oBAAsBF,EAAY,CACzD,IAAIG,EAAuBH,KAAc7H,EAAcA,EAAY6H,QAAcjjB,EAC7EojB,GACF,CAAC,gBAAiB,eAAgB,kBAAkBje,QAAQ,SAAUgC,GACpE,IAAIkc,EAAaD,EAAqBjc,QACZ,IAAfkc,IAITD,EAAqBjc,GAHT,kBAAVA,GAA6C,SAAfkc,GAAwC,SAAfA,EAEtC,kBAAVlc,EACqBtF,WAAWwhB,GAEXnW,SAASmW,EAAY,IAJrB,UASpC,IAAIC,EAAmBF,GAAwBxW,EAAO2W,eAClDC,EAAmBF,EAAiBpN,WAAaoN,EAAiBpN,YAAchP,EAAOgP,UACvFuN,EAAcvc,EAAOkN,OAASkP,EAAiB/T,gBAAkBrI,EAAOqI,eAAiBiU,GAEzFA,GAAoB7M,GACtB/J,EAAO8W,kBAGTne,GAAMqC,OAAOgF,EAAO1F,OAAQoc,GAE5B/d,GAAMqC,OAAOgF,EAAQ,CACnBiQ,eAAgBjQ,EAAO1F,OAAO2V,eAC9BjG,eAAgBhK,EAAO1F,OAAO0P,eAC9BC,eAAgBjK,EAAO1F,OAAO2P,iBAGhCjK,EAAOuW,kBAAoBF,EAEvBQ,GAAe9M,IACjB/J,EAAO+L,cACP/L,EAAOsL,aACPtL,EAAOQ,eACPR,EAAO0J,QAAS5D,EAAcwE,EAAgBtK,EAAOsK,aAAc,GAAG,IAGxEtK,EAAO/B,KAAK,aAAcyY,MA2BoBJ,cAvBlD,SAAwB9H,GAGtB,GAAKA,EAAL,CACA,IAAI6H,GAAa,EACbU,EAAS,GACb1e,OAAOC,KAAKkW,GAAajW,QAAQ,SAAUye,GACzCD,EAAOznB,KAAK0nB,KAEdD,EAAOE,KAAK,SAAUvd,EAAGwd,GAAK,OAAO5W,SAAS5G,EAAG,IAAM4G,SAAS4W,EAAG,MACnE,IAAK,IAAIxoB,EAAI,EAAGA,EAAIqoB,EAAOpoB,OAAQD,GAAK,EAAG,CACzC,IAAIsoB,EAAQD,EAAOroB,GAVRrC,KAWAiO,OAAOmV,mBACZuH,GAASrpB,EAAIwpB,aACfd,EAAaW,GAENA,GAASrpB,EAAIwpB,aAAed,IACrCA,EAAaW,GAGjB,OAAOX,GAAc,SAsRrBhR,cAjJoB,CAAEA,cAjBxB,WACE,IAAIrF,EAAS3T,KACT+qB,EAAYpX,EAAOoM,SAEvBpM,EAAOoM,SAAsC,IAA3BpM,EAAOoB,SAASzS,OAClCqR,EAAOgK,gBAAkBhK,EAAOoM,SAChCpM,EAAOiK,gBAAkBjK,EAAOoM,SAG5BgL,IAAcpX,EAAOoM,UAAYpM,EAAO/B,KAAK+B,EAAOoM,SAAW,OAAS,UAExEgL,GAAaA,IAAcpX,EAAOoM,WACpCpM,EAAOiH,OAAQ,EACfjH,EAAOqX,WAAWzX,YAsJpB1P,QA9NY,CAAEonB,WApDhB,WACE,IACIC,EADSlrB,KACWkrB,WACpBjd,EAFSjO,KAEOiO,OAChBqG,EAHStU,KAGIsU,IACbV,EAJS5T,KAII4T,IACbuX,EAAW,GAEfA,EAASloB,KAAK,eACdkoB,EAASloB,KAAKgL,EAAOgP,WAEjBhP,EAAOoU,UACT8I,EAASloB,KAAK,aAEXoM,GAAQY,SACXkb,EAASloB,KAAK,cAEZgL,EAAOiP,YACTiO,EAASloB,KAAK,cAEZqR,GACF6W,EAASloB,KAAK,OAEa,EAAzBgL,EAAOiI,iBACTiV,EAASloB,KAAK,YAEZ6d,EAAOG,SACTkK,EAASloB,KAAK,WAEZ6d,EAAOE,KACTmK,EAASloB,KAAK,QAGXwN,EAAQC,MAAQD,EAAQE,UAAYtB,GAAQK,eAAiBL,GAAQQ,wBACxEsb,EAASloB,KAAM,OAAUgL,EAAgB,WAG3Ckd,EAASjf,QAAQ,SAAUkf,GACzBF,EAAWjoB,KAAKgL,EAAO6W,uBAAyBsG,KAGlDxX,EAAIjQ,SAASunB,EAAW3d,KAAK,OAWS8d,cARxC,WACE,IACIzX,EADS5T,KACI4T,IACbsX,EAFSlrB,KAEWkrB,WAExBtX,EAAI3P,YAAYinB,EAAW3d,KAAK,QAkOhC+d,OAzKW,CACXC,UArDF,SAAoBC,EAASC,EAAKC,EAAQC,EAAOC,EAAkBxjB,GACjE,IAAIyjB,EACJ,SAASC,IACH1jB,GAAYA,IAEbojB,EAAQO,UAAaH,EAmBxBE,IAlBIL,IACFI,EAAQ,IAAIvqB,EAAIQ,OACVkqB,OAASF,EACfD,EAAMI,QAAUH,EACZH,IACFE,EAAMF,MAAQA,GAEZD,IACFG,EAAMH,OAASA,GAEbD,IACFI,EAAMJ,IAAMA,IAGdK,KAkCJvH,cA1BF,WACE,IAAI5Q,EAAS3T,KAEb,SAAS8rB,IACH,MAAOnY,GAA8CA,IAAUA,EAAOmK,iBAC9C/W,IAAxB4M,EAAOuY,eAA8BvY,EAAOuY,cAAgB,GAC5DvY,EAAOuY,eAAiBvY,EAAOwY,aAAa7pB,SAC1CqR,EAAO1F,OAAOuW,qBAAuB7Q,EAAOJ,SAChDI,EAAO/B,KAAK,iBANhB+B,EAAOwY,aAAexY,EAAOC,IAAIjI,KAAK,OAStC,IAAK,IAAItJ,EAAI,EAAGA,EAAIsR,EAAOwY,aAAa7pB,OAAQD,GAAK,EAAG,CACtD,IAAImpB,EAAU7X,EAAOwY,aAAa9pB,GAClCsR,EAAO4X,UACLC,EACAA,EAAQY,YAAcZ,EAAQ5mB,aAAa,OAC3C4mB,EAAQE,QAAUF,EAAQ5mB,aAAa,UACvC4mB,EAAQG,OAASH,EAAQ5mB,aAAa,UACtC,EACAknB,OAiLFO,EAAmB,GAEnBtsB,EAAuB,SAAUiR,GACnC,SAASjR,IAIP,IAHA,IAAI8F,EAIAX,EACA+I,EAHAnI,EAAO,GAAIC,EAAMpB,UAAUrC,OACvByD,KAAQD,EAAMC,GAAQpB,UAAWoB,GAGrB,IAAhBD,EAAKxD,QAAgBwD,EAAK,GAAG4I,aAAe5I,EAAK,GAAG4I,cAAgB1C,OACtEiC,EAASnI,EAAK,IAEEZ,GAAfW,EAASC,GAAkB,GAAImI,EAASpI,EAAO,IAE7CoI,IAAUA,EAAS,IAExBA,EAAS3B,GAAMqC,OAAO,GAAIV,GACtB/I,IAAO+I,EAAO/I,KAAM+I,EAAO/I,GAAKA,GAEpC8L,EAAYzI,KAAKvI,KAAMiO,GAEvBjC,OAAOC,KAAKkZ,GAAYjZ,QAAQ,SAAUogB,GACxCtgB,OAAOC,KAAKkZ,EAAWmH,IAAiBpgB,QAAQ,SAAUqgB,GACnDxsB,EAAOyD,UAAU+oB,KACpBxsB,EAAOyD,UAAU+oB,GAAepH,EAAWmH,GAAgBC,QAMjE,IAAI5Y,EAAS3T,UACiB,IAAnB2T,EAAOxB,UAChBwB,EAAOxB,QAAU,IAEnBnG,OAAOC,KAAK0H,EAAOxB,SAASjG,QAAQ,SAAUkG,GAC5C,IAAIzS,EAASgU,EAAOxB,QAAQC,GAC5B,GAAIzS,EAAOsO,OAAQ,CACjB,IAAIue,EAAkBxgB,OAAOC,KAAKtM,EAAOsO,QAAQ,GAC7CsE,EAAe5S,EAAOsO,OAAOue,GACjC,GAA4B,iBAAjBja,GAA8C,OAAjBA,EAAyB,OACjE,KAAMia,KAAmBve,GAAU,YAAasE,GAAiB,QACjC,IAA5BtE,EAAOue,KACTve,EAAOue,GAAmB,CAAE7X,SAAS,IAGF,iBAA5B1G,EAAOue,IACT,YAAave,EAAOue,KAEzBve,EAAOue,GAAiB7X,SAAU,GAE/B1G,EAAOue,KAAoBve,EAAOue,GAAmB,CAAE7X,SAAS,OAKzE,IAAI8X,EAAengB,GAAMqC,OAAO,GAAI6T,GACpC7O,EAAO3B,iBAAiBya,GAGxB9Y,EAAO1F,OAAS3B,GAAMqC,OAAO,GAAI8d,EAAcJ,EAAkBpe,GACjE0F,EAAO2W,eAAiBhe,GAAMqC,OAAO,GAAIgF,EAAO1F,QAChD0F,EAAO+Y,aAAepgB,GAAMqC,OAAO,GAAIV,GAMvC,IAAI2F,GAHJD,EAAOpR,EAAIA,GAGCoR,EAAO1F,OAAO/I,IAG1B,GAFAA,EAAK0O,EAAI,GAET,CAIA,GAAiB,EAAbA,EAAItR,OAAY,CAClB,IAAIqqB,EAAU,GAKd,OAJA/Y,EAAI/J,KAAK,SAAUO,EAAOwiB,GACxB,IAAIC,EAAYvgB,GAAMqC,OAAO,GAAIV,EAAQ,CAAE/I,GAAI0nB,IAC/CD,EAAQ1pB,KAAK,IAAIlD,EAAO8sB,MAEnBF,EAGTznB,EAAGyO,OAASA,EACZC,EAAI5O,KAAK,SAAU2O,GAGnB,IAmDQrE,EACA6R,EApDJ/M,EAAaR,EAAI7S,SAAU,IAAO4S,EAAO1F,OAAmB,cAwHhE,OArHA3B,GAAMqC,OAAOgF,EAAQ,CACnBC,IAAKA,EACL1O,GAAIA,EACJkP,WAAYA,EACZkR,UAAWlR,EAAW,GAGtB8W,WAAY,GAGZrW,OAAQtS,IACRyS,WAAY,GACZD,SAAU,GACVE,gBAAiB,GAGjBlB,aAAc,WACZ,MAAmC,eAA5BJ,EAAO1F,OAAOgP,WAEvBjJ,WAAY,WACV,MAAmC,aAA5BL,EAAO1F,OAAOgP,WAGvB3I,IAA+B,QAAzBpP,EAAGiY,IAAItM,eAAoD,QAAzB+C,EAAIlK,IAAI,aAChD6K,aAA0C,eAA5BZ,EAAO1F,OAAOgP,YAAwD,QAAzB/X,EAAGiY,IAAItM,eAAoD,QAAzB+C,EAAIlK,IAAI,cACrG8K,SAAwC,gBAA9BJ,EAAW1K,IAAI,WAGzB+P,YAAa,EACbwB,UAAW,EAGXN,aAAa,EACbC,OAAO,EAGPd,UAAW,EACX+C,kBAAmB,EACnBtC,SAAU,EACVuO,SAAU,EACV1L,WAAW,EAGXO,eAAgBhK,EAAO1F,OAAO0P,eAC9BC,eAAgBjK,EAAO1F,OAAO2P,eAG9ByH,aACM/V,EAAQ,CAAC,aAAc,YAAa,YACpC6R,EAAU,CAAC,YAAa,YAAa,WACrC9R,GAAQK,cACVyR,EAAU,CAAC,cAAe,cAAe,aAChC9R,GAAQQ,wBACjBsR,EAAU,CAAC,gBAAiB,gBAAiB,gBAE/CxN,EAAOmZ,iBAAmB,CACxBnD,MAAOra,EAAM,GACbua,KAAMva,EAAM,GACZwa,IAAKxa,EAAM,IAEbqE,EAAOoZ,mBAAqB,CAC1BpD,MAAOxI,EAAQ,GACf0I,KAAM1I,EAAQ,GACd2I,IAAK3I,EAAQ,IAER9R,GAAQC,QAAUqE,EAAO1F,OAAO6R,cAAgBnM,EAAOmZ,iBAAmBnZ,EAAOoZ,oBAE1FvH,gBAAiB,CACfO,eAAWhf,EACXif,aAASjf,EACT4f,yBAAqB5f,EACrB+f,oBAAgB/f,EAChB6f,iBAAa7f,EACbyV,sBAAkBzV,EAClB+gB,oBAAgB/gB,EAChBigB,wBAAoBjgB,EAEpBmgB,aAAc,iDAEdsB,cAAelc,GAAMM,MACrB6b,kBAAc1hB,EAEdkhB,WAAY,GACZF,yBAAqBhhB,EACrB4e,kBAAc5e,EACd8f,iBAAa9f,GAIfkf,YAAY,EAGZrC,eAAgBjQ,EAAO1F,OAAO2V,eAE9B6B,QAAS,CACPc,OAAQ,EACRC,OAAQ,EACRN,SAAU,EACVG,SAAU,EACV5G,KAAM,GAIR0M,aAAc,GACdD,aAAc,IAKhBvY,EAAOtB,aAGHsB,EAAO1F,OAAOwU,MAChB9O,EAAO8O,OAIF9O,GAGJ3C,IAAcjR,EAAOitB,UAAYhc,GAItC,IAAIG,EAAkB,CAAEkb,iBAAkB,CAAEhb,cAAc,GAAOmR,SAAU,CAAEnR,cAAc,GAAO5N,MAAO,CAAE4N,cAAc,GAAO9O,EAAG,CAAE8O,cAAc,IA4QnJ,QA/QAtR,EAAOyD,UAAYwI,OAAO4G,OAAQ5B,GAAeA,EAAYxN,YAC5CkL,YAAc3O,GAIxByD,UAAUub,qBAAuB,WACtC,IAAIpL,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAChB4G,EAASlB,EAAOkB,OAChBG,EAAarB,EAAOqB,WACpBX,EAAaV,EAAOO,KACpBuF,EAAc9F,EAAO8F,YACrBwT,EAAM,EACV,GAAIhf,EAAO+J,eAAgB,CAGzB,IAFA,IACIkV,EADAtX,EAAYf,EAAO4E,GAAa1B,gBAE3B1V,EAAIoX,EAAc,EAAGpX,EAAIwS,EAAOvS,OAAQD,GAAK,EAChDwS,EAAOxS,KAAO6qB,IAEhBD,GAAO,EACS5Y,GAFhBuB,GAAaf,EAAOxS,GAAG0V,mBAEOmV,GAAY,IAG9C,IAAK,IAAI7U,EAAMoB,EAAc,EAAU,GAAPpB,EAAUA,GAAO,EAC3CxD,EAAOwD,KAAS6U,IAElBD,GAAO,EACS5Y,GAFhBuB,GAAaf,EAAOwD,GAAKN,mBAEKmV,GAAY,SAI9C,IAAK,IAAI3U,EAAMkB,EAAc,EAAGlB,EAAM1D,EAAOvS,OAAQiW,GAAO,EACtDvD,EAAWuD,GAAOvD,EAAWyE,GAAepF,IAC9C4Y,GAAO,GAIb,OAAOA,GAGTltB,EAAOyD,UAAU+P,OAAS,WACxB,IAAII,EAAS3T,KACb,GAAK2T,IAAUA,EAAOmK,UAAtB,CACA,IAAI/I,EAAWpB,EAAOoB,SAClB9G,EAAS0F,EAAO1F,OAEhBA,EAAOkU,aACTxO,EAAOyO,gBAETzO,EAAOH,aACPG,EAAOQ,eACPR,EAAO6G,iBACP7G,EAAOoH,sBAUHpH,EAAO1F,OAAOoU,UAChB5F,IACI9I,EAAO1F,OAAOiP,YAChBvJ,EAAOyF,sBAG4B,SAAhCzF,EAAO1F,OAAOqI,eAA0D,EAA9B3C,EAAO1F,OAAOqI,gBAAsB3C,EAAOiH,QAAUjH,EAAO1F,OAAO+J,eACnGrE,EAAO0J,QAAQ1J,EAAOkB,OAAOvS,OAAS,EAAG,GAAG,GAAO,GAEnDqR,EAAO0J,QAAQ1J,EAAO8F,YAAa,GAAG,GAAO,KAG1DgD,IAGAxO,EAAO8K,eAAiBhE,IAAapB,EAAOoB,UAC9CpB,EAAOqF,gBAETrF,EAAO/B,KAAK,UA1BZ,SAAS6K,IACP,IAAI0Q,EAAiBxZ,EAAOY,cAAmC,EAApBZ,EAAOmG,UAAiBnG,EAAOmG,UACtEwI,EAAenM,KAAKoM,IAAIpM,KAAKK,IAAI2W,EAAgBxZ,EAAO+G,gBAAiB/G,EAAOyG,gBACpFzG,EAAO8I,aAAa6F,GACpB3O,EAAOiI,oBACPjI,EAAOoH,wBAwBXhb,EAAOyD,UAAUinB,gBAAkB,SAA0B2C,EAAcC,QACrD,IAAfA,IAAwBA,GAAa,GAE1C,IAAI1Z,EAAS3T,KACTstB,EAAmB3Z,EAAO1F,OAAOgP,UAKrC,OAJKmQ,IAEHA,EAAoC,eAArBE,EAAoC,WAAa,cAE7DF,IAAiBE,GAAuC,eAAjBF,GAAkD,aAAjBA,IAIpD,aAArBE,IACF3Z,EAAOC,IACJ3P,YAAc0P,EAAO1F,OAA6B,uBAAI,yBACtDtK,SAAU,GAAMgQ,EAAO1F,OAA6B,uBAAImf,IAEtD3c,EAAQC,MAAQD,EAAQE,UAAYtB,GAAQK,eAAiBL,GAAQQ,wBACxE8D,EAAOC,IAAIjQ,SAAWgQ,EAAO1F,OAA6B,uBAAI,OAASmf,IAGlD,eAArBE,IACF3Z,EAAOC,IACJ3P,YAAc0P,EAAO1F,OAA6B,uBAAI,6BACtDtK,SAAU,GAAMgQ,EAAO1F,OAA6B,uBAAImf,IAEtD3c,EAAQC,MAAQD,EAAQE,UAAYtB,GAAQK,eAAiBL,GAAQQ,wBACxE8D,EAAOC,IAAIjQ,SAAWgQ,EAAO1F,OAA6B,uBAAI,OAASmf,IAI3EzZ,EAAO1F,OAAOgP,UAAYmQ,EAE1BzZ,EAAOkB,OAAOhL,KAAK,SAAU0T,EAAYgQ,GAClB,aAAjBH,EACFG,EAAQtsB,MAAMwS,MAAQ,GAEtB8Z,EAAQtsB,MAAMyS,OAAS,KAI3BC,EAAO/B,KAAK,mBACRyb,GAAc1Z,EAAOJ,UAjChBI,GAsCX5T,EAAOyD,UAAUif,KAAO,WACtB,IAAI9O,EAAS3T,KACT2T,EAAO+J,cAEX/J,EAAO/B,KAAK,cAGR+B,EAAO1F,OAAOkU,aAChBxO,EAAOyO,gBAITzO,EAAOsX,aAGHtX,EAAO1F,OAAOkN,MAChBxH,EAAOsL,aAITtL,EAAOH,aAGPG,EAAOQ,eAEHR,EAAO1F,OAAO8K,eAChBpF,EAAOqF,gBAILrF,EAAO1F,OAAO0R,YAChBhM,EAAOiM,gBAGLjM,EAAO1F,OAAOsW,eAChB5Q,EAAO4Q,gBAIL5Q,EAAO1F,OAAOkN,KAChBxH,EAAO0J,QAAQ1J,EAAO1F,OAAOwP,aAAe9J,EAAOsK,aAAc,EAAGtK,EAAO1F,OAAOiX,oBAElFvR,EAAO0J,QAAQ1J,EAAO1F,OAAOwP,aAAc,EAAG9J,EAAO1F,OAAOiX,oBAI9DvR,EAAOyR,eAGPzR,EAAO+J,aAAc,EAGrB/J,EAAO/B,KAAK,UAGd7R,EAAOyD,UAAUgqB,QAAU,SAAkBC,EAAgBC,QACnC,IAAnBD,IAA4BA,GAAiB,QAC7B,IAAhBC,IAAyBA,GAAc,GAE5C,IAAI/Z,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAChB2F,EAAMD,EAAOC,IACbQ,EAAaT,EAAOS,WACpBS,EAASlB,EAAOkB,OAEpB,YAA6B,IAAlBlB,EAAO1F,QAA0B0F,EAAOmK,YAInDnK,EAAO/B,KAAK,iBAGZ+B,EAAO+J,aAAc,EAGrB/J,EAAOoW,eAGH9b,EAAOkN,MACTxH,EAAO+L,cAILgO,IACF/Z,EAAO0X,gBACPzX,EAAI9O,WAAW,SACfsP,EAAWtP,WAAW,SAClB+P,GAAUA,EAAOvS,QACnBuS,EACG5Q,YAAY,CACXgK,EAAO+L,kBACP/L,EAAOiN,iBACPjN,EAAOsN,eACPtN,EAAOwN,gBAAiBlO,KAAK,MAC9BzI,WAAW,SACXA,WAAW,2BACXA,WAAW,sBACXA,WAAW,oBAIlB6O,EAAO/B,KAAK,WAGZ5F,OAAOC,KAAK0H,EAAO1C,iBAAiB/E,QAAQ,SAAUgF,GACpDyC,EAAOrM,IAAI4J,MAGU,IAAnBuc,IACF9Z,EAAOC,IAAI,GAAGD,OAAS,KACvBA,EAAOC,IAAI5O,KAAK,SAAU,MAC1BsH,GAAMC,YAAYoH,IAEpBA,EAAOmK,WAAY,GA/CV,MAoDX/d,EAAO4tB,eAAiB,SAAyBC,GAC/CthB,GAAMqC,OAAO0d,EAAkBuB,IAGjCzc,EAAgBkb,iBAAiB9b,IAAM,WACrC,OAAO8b,GAGTlb,EAAgBqR,SAASjS,IAAM,WAC7B,OAAOiS,GAGTrR,EAAgB1N,MAAM8M,IAAM,WAC1B,OAAOS,GAGTG,EAAgB5O,EAAEgO,IAAM,WACtB,OAAOhO,GAGTyJ,OAAOsH,iBAAkBvT,EAAQoR,GAE1BpR,EAjeiB,CAkexBiR,GAEE6c,EAAW,CACb7a,KAAM,SACNC,MAAO,CACL8N,OAAQD,GAEV5N,OAAQ,CACN6N,OAAQD,IAIRgN,EAAY,CACd9a,KAAM,UACNC,MAAO,CACL8a,QAAS1e,IAEX6D,OAAQ,CACN6a,QAAS1e,KAIT2e,EAAY,CACdhb,KAAM,UACNC,MAAO,CACLgb,QAASxd,GAEXyC,OAAQ,CACN+a,QAASxd,IAITyd,EAAS,CACXlb,KAAM,SACNJ,OAAQ,WACN,IAAIe,EAAS3T,KACbsM,GAAMqC,OAAOgF,EAAQ,CACnBwa,OAAQ,CACNC,cAAe,WACRza,IAAUA,EAAOmK,WAAcnK,EAAO+J,cAC3C/J,EAAO/B,KAAK,gBACZ+B,EAAO/B,KAAK,YAEdyc,yBAA0B,WACnB1a,IAAUA,EAAOmK,WAAcnK,EAAO+J,aAC3C/J,EAAO/B,KAAK,0BAKpBhM,GAAI,CACF6c,KAAM,WAGJnhB,EAAIlB,iBAAiB,SAFRJ,KAEyBmuB,OAAOC,eAG7C9sB,EAAIlB,iBAAiB,oBALRJ,KAKoCmuB,OAAOE,2BAE1Db,QAAS,WAEPlsB,EAAIjB,oBAAoB,SADXL,KAC4BmuB,OAAOC,eAChD9sB,EAAIjB,oBAAoB,oBAFXL,KAEuCmuB,OAAOE,6BAK7DC,EAAW,CACbC,KAAMjtB,EAAIktB,kBAAoBltB,EAAImtB,uBAClCC,OAAQ,SAAgBpoB,EAAQqoB,QACb,IAAZA,IAAqBA,EAAU,IAEpC,IAAIhb,EAAS3T,KAGTkQ,EAAW,IADIoe,EAASC,KACI,SAAUK,GAIxC,GAAyB,IAArBA,EAAUtsB,OAAd,CAIA,IAAIusB,EAAiB,WACnBlb,EAAO/B,KAAK,iBAAkBgd,EAAU,KAGtCttB,EAAIwtB,sBACNxtB,EAAIwtB,sBAAsBD,GAE1BvtB,EAAIW,WAAW4sB,EAAgB,QAV/Blb,EAAO/B,KAAK,iBAAkBgd,EAAU,MAc5C1e,EAAS6e,QAAQzoB,EAAQ,CACvB0oB,gBAA0C,IAAvBL,EAAQK,YAAoCL,EAAQK,WACvEC,eAAwC,IAAtBN,EAAQM,WAAmCN,EAAQM,UACrEC,mBAAgD,IAA1BP,EAAQO,eAAuCP,EAAQO,gBAG/Evb,EAAOzD,SAASif,UAAUlsB,KAAKiN,IAEjCuS,KAAM,WACJ,IAAI9O,EAAS3T,KACb,GAAKqP,GAAQa,UAAayD,EAAO1F,OAAOiC,SAAxC,CACA,GAAIyD,EAAO1F,OAAOmhB,eAEhB,IADA,IAAIC,EAAmB1b,EAAOC,IAAIhN,UACzBvE,EAAI,EAAGA,EAAIgtB,EAAiB/sB,OAAQD,GAAK,EAChDsR,EAAOzD,SAASwe,OAAOW,EAAiBhtB,IAI5CsR,EAAOzD,SAASwe,OAAO/a,EAAOC,IAAI,GAAI,CAAEqb,UAAWtb,EAAO1F,OAAOqhB,uBAGjE3b,EAAOzD,SAASwe,OAAO/a,EAAOS,WAAW,GAAI,CAAE4a,YAAY,MAE7DxB,QAAS,WACMxtB,KACNkQ,SAASif,UAAUjjB,QAAQ,SAAUgE,GAC1CA,EAASqf,eAFEvvB,KAINkQ,SAASif,UAAY,KAI5BK,EAAa,CACfxc,KAAM,WACN/E,OAAQ,CACNiC,UAAU,EACVkf,gBAAgB,EAChBE,sBAAsB,GAExB1c,OAAQ,WAENtG,GAAMqC,OADO3O,KACQ,CACnBkQ,SAAU,CACRuS,KAAM6L,EAAS7L,KAAK/P,KAHX1S,MAIT0uB,OAAQJ,EAASI,OAAOhc,KAJf1S,MAKTwtB,QAASc,EAASd,QAAQ9a,KALjB1S,MAMTmvB,UAAW,OAIjBvpB,GAAI,CACF6c,KAAM,WACSziB,KACNkQ,SAASuS,QAElB+K,QAAS,WACMxtB,KACNkQ,SAASsd,aAKlBiC,EAAU,CACZlc,OAAQ,SAAgBmc,GACtB,IAAI/b,EAAS3T,KACT2vB,EAAMhc,EAAO1F,OACbqI,EAAgBqZ,EAAIrZ,cACpB4B,EAAiByX,EAAIzX,eACrBF,EAAiB2X,EAAI3X,eACrB4X,EAAQjc,EAAO1F,OAAOyG,QACtBmb,EAAkBD,EAAMC,gBACxBC,EAAiBF,EAAME,eACvBC,EAAQpc,EAAOe,QACfsb,EAAeD,EAAME,KACrBC,EAAaH,EAAMlhB,GACnBgG,EAASkb,EAAMlb,OACfsb,EAAqBJ,EAAM/a,WAC3Bob,EAAcL,EAAMK,YACpBC,EAAiBN,EAAMhnB,OAC3B4K,EAAOiI,oBACP,IAEI0U,EAIAC,EACAC,EAPA/W,EAAc9F,EAAO8F,aAAe,EAGb6W,EAAvB3c,EAAOY,aAA6B,QACpBZ,EAAOI,eAAiB,OAAS,MAIjDiE,GACFuY,EAAcpa,KAAKC,MAAME,EAAgB,GAAK4B,EAAiB2X,EAC/DW,EAAera,KAAKC,MAAME,EAAgB,GAAK4B,EAAiB4X,IAEhES,EAAcja,GAAiB4B,EAAiB,GAAK2X,EACrDW,EAAetY,EAAiB4X,GAElC,IAAIG,EAAO9Z,KAAKK,KAAKiD,GAAe,GAAK+W,EAAc,GACnD3hB,EAAKsH,KAAKoM,KAAK9I,GAAe,GAAK8W,EAAa1b,EAAOvS,OAAS,GAChEyG,GAAU4K,EAAOqB,WAAWib,IAAS,IAAMtc,EAAOqB,WAAW,IAAM,GASvE,SAASyb,IACP9c,EAAOQ,eACPR,EAAO6G,iBACP7G,EAAOoH,sBACHpH,EAAO+c,MAAQ/c,EAAO1F,OAAOyiB,KAAK/b,SACpChB,EAAO+c,KAAKC,OAIhB,GAhBArkB,GAAMqC,OAAOgF,EAAOe,QAAS,CAC3Bub,KAAMA,EACNphB,GAAIA,EACJ9F,OAAQA,EACRiM,WAAYrB,EAAOqB,aAYjBgb,IAAiBC,GAAQC,IAAerhB,IAAO6gB,EAKjD,OAJI/b,EAAOqB,aAAemb,GAAsBpnB,IAAWsnB,GACzD1c,EAAOkB,OAAOnL,IAAI4mB,EAAavnB,EAAS,WAE1C4K,EAAO6G,iBAGT,GAAI7G,EAAO1F,OAAOyG,QAAQkc,eAcxB,OAbAjd,EAAO1F,OAAOyG,QAAQkc,eAAeroB,KAAKoL,EAAQ,CAChD5K,OAAQA,EACRknB,KAAMA,EACNphB,GAAIA,EACJgG,OAAS,WAEP,IADA,IAAIgc,EAAiB,GACZxuB,EAAI4tB,EAAM5tB,GAAKwM,EAAIxM,GAAK,EAC/BwuB,EAAe5tB,KAAK4R,EAAOxS,IAE7B,OAAOwuB,EALD,UAQVJ,IAGF,IAAIK,EAAiB,GACjBC,EAAgB,GACpB,GAAIrB,EACF/b,EAAOS,WAAWzI,KAAM,IAAOgI,EAAO1F,OAAiB,YAAI/J,cAE3D,IAAK,IAAI7B,EAAI2tB,EAAc3tB,GAAK6tB,EAAY7tB,GAAK,GAC3CA,EAAI4tB,GAAYphB,EAAJxM,IACdsR,EAAOS,WAAWzI,KAAM,IAAOgI,EAAO1F,OAAiB,WAAI,6BAAgC5L,EAAI,MAAQ6B,SAI7G,IAAK,IAAImU,EAAM,EAAGA,EAAMxD,EAAOvS,OAAQ+V,GAAO,EACjC4X,GAAP5X,GAAeA,GAAOxJ,SACE,IAAfqhB,GAA8BR,EACvCqB,EAAc9tB,KAAKoV,IAET6X,EAAN7X,GAAoB0Y,EAAc9tB,KAAKoV,GACvCA,EAAM2X,GAAgBc,EAAe7tB,KAAKoV,KAIpD0Y,EAAc7kB,QAAQ,SAAU9B,GAC9BuJ,EAAOS,WAAW3J,OAAO2lB,EAAYvb,EAAOzK,GAAQA,MAEtD0mB,EAAelG,KAAK,SAAUvd,EAAGwd,GAAK,OAAOA,EAAIxd,IAAMnB,QAAQ,SAAU9B,GACvEuJ,EAAOS,WAAWtJ,QAAQslB,EAAYvb,EAAOzK,GAAQA,MAEvDuJ,EAAOS,WAAWrT,SAAS,iBAAiB2I,IAAI4mB,EAAavnB,EAAS,MACtE0nB,KAEFL,YAAa,SAAqBxZ,EAAOxM,GACvC,IAAIuJ,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAAOyG,QAC3B,GAAIzG,EAAO+iB,OAASrd,EAAOe,QAAQsc,MAAM5mB,GACvC,OAAOuJ,EAAOe,QAAQsc,MAAM5mB,GAE9B,IAAI6mB,EAAWhjB,EAAOmiB,YAClB7tB,EAAE0L,EAAOmiB,YAAY7nB,KAAKoL,EAAQiD,EAAOxM,IACzC7H,EAAG,eAAmBoR,EAAO1F,OAAiB,WAAI,8BAAkC7D,EAAQ,KAAQwM,EAAQ,UAGhH,OAFKqa,EAAS1sB,KAAK,4BAA8B0sB,EAAS1sB,KAAK,0BAA2B6F,GACtF6D,EAAO+iB,QAASrd,EAAOe,QAAQsc,MAAM5mB,GAAS6mB,GAC3CA,GAET9Q,YAAa,SAAqBtL,GAEhC,GAAsB,iBAAXA,GAAuB,WAAYA,EAC5C,IAAK,IAAIxS,EAAI,EAAGA,EAAIwS,EAAOvS,OAAQD,GAAK,EAClCwS,EAAOxS,IAHFrC,KAGe0U,QAAQG,OAAO5R,KAAK4R,EAAOxS,SAH1CrC,KAMJ0U,QAAQG,OAAO5R,KAAK4R,GANhB7U,KAQN0U,QAAQnB,QAAO,IAExB6M,aAAc,SAAsBvL,GAClC,IAAIlB,EAAS3T,KACTyZ,EAAc9F,EAAO8F,YACrBoC,EAAiBpC,EAAc,EAC/ByX,EAAoB,EAExB,GAAIrf,MAAMC,QAAQ+C,GAAS,CACzB,IAAK,IAAIxS,EAAI,EAAGA,EAAIwS,EAAOvS,OAAQD,GAAK,EAClCwS,EAAOxS,IAAMsR,EAAOe,QAAQG,OAAOpO,QAAQoO,EAAOxS,IAExDwZ,EAAiBpC,EAAc5E,EAAOvS,OACtC4uB,EAAoBrc,EAAOvS,YAE3BqR,EAAOe,QAAQG,OAAOpO,QAAQoO,GAEhC,GAAIlB,EAAO1F,OAAOyG,QAAQsc,MAAO,CAC/B,IAAIA,EAAQrd,EAAOe,QAAQsc,MACvBG,EAAW,GACfnlB,OAAOC,KAAK+kB,GAAO9kB,QAAQ,SAAUklB,GACnCD,EAASld,SAASmd,EAAa,IAAMF,GAAqBF,EAAMI,KAElEzd,EAAOe,QAAQsc,MAAQG,EAEzBxd,EAAOe,QAAQnB,QAAO,GACtBI,EAAO0J,QAAQxB,EAAgB,IAEjC6E,YAAa,SAAqBC,GAChC,IAAIhN,EAAS3T,KACb,GAAI,MAAO2gB,EAAX,CACA,IAAIlH,EAAc9F,EAAO8F,YACzB,GAAI5H,MAAMC,QAAQ6O,GAChB,IAAK,IAAIte,EAAIse,EAAcre,OAAS,EAAQ,GAALD,EAAQA,GAAK,EAClDsR,EAAOe,QAAQG,OAAOpN,OAAOkZ,EAActe,GAAI,GAC3CsR,EAAO1F,OAAOyG,QAAQsc,cACjBrd,EAAOe,QAAQsc,MAAMrQ,EAActe,IAExCse,EAActe,GAAKoX,IAAeA,GAAe,GACrDA,EAActD,KAAKK,IAAIiD,EAAa,QAGtC9F,EAAOe,QAAQG,OAAOpN,OAAOkZ,EAAe,GACxChN,EAAO1F,OAAOyG,QAAQsc,cACjBrd,EAAOe,QAAQsc,MAAMrQ,GAE1BA,EAAgBlH,IAAeA,GAAe,GAClDA,EAActD,KAAKK,IAAIiD,EAAa,GAEtC9F,EAAOe,QAAQnB,QAAO,GACtBI,EAAO0J,QAAQ5D,EAAa,KAE9BoH,gBAAiB,WACf,IAAIlN,EAAS3T,KACb2T,EAAOe,QAAQG,OAAS,GACpBlB,EAAO1F,OAAOyG,QAAQsc,QACxBrd,EAAOe,QAAQsc,MAAQ,IAEzBrd,EAAOe,QAAQnB,QAAO,GACtBI,EAAO0J,QAAQ,EAAG,KAIlBgU,EAAY,CACdre,KAAM,UACN/E,OAAQ,CACNyG,QAAS,CACPC,SAAS,EACTE,OAAQ,GACRmc,OAAO,EACPZ,YAAa,KACbQ,eAAgB,KAChBf,gBAAiB,EACjBC,eAAgB,IAGpBld,OAAQ,WACN,IAAIe,EAAS3T,KACbsM,GAAMqC,OAAOgF,EAAQ,CACnBe,QAAS,CACPnB,OAAQkc,EAAQlc,OAAOb,KAAKiB,GAC5BwM,YAAasP,EAAQtP,YAAYzN,KAAKiB,GACtCyM,aAAcqP,EAAQrP,aAAa1N,KAAKiB,GACxC+M,YAAa+O,EAAQ/O,YAAYhO,KAAKiB,GACtCkN,gBAAiB4O,EAAQ5O,gBAAgBnO,KAAKiB,GAC9Cyc,YAAaX,EAAQW,YAAY1d,KAAKiB,GACtCkB,OAAQlB,EAAO1F,OAAOyG,QAAQG,OAC9Bmc,MAAO,OAIbprB,GAAI,CACF0rB,WAAY,WACV,IAAI3d,EAAS3T,KACb,GAAK2T,EAAO1F,OAAOyG,QAAQC,QAA3B,CACAhB,EAAOuX,WAAWjoB,KAAO0Q,EAAO1F,OAA6B,uBAAI,WACjE,IAAIsjB,EAAkB,CACpBtY,qBAAqB,GAEvB3M,GAAMqC,OAAOgF,EAAO1F,OAAQsjB,GAC5BjlB,GAAMqC,OAAOgF,EAAO2W,eAAgBiH,GAE/B5d,EAAO1F,OAAOwP,cACjB9J,EAAOe,QAAQnB,WAGnBkJ,aAAc,WACCzc,KACDiO,OAAOyG,QAAQC,SADd3U,KAEN0U,QAAQnB,YAKjBie,EAAW,CACbC,OAAQ,SAAgBrqB,GACtB,IAAIuM,EAAS3T,KACTsU,EAAMX,EAAOY,aACblO,EAAIe,EACJf,EAAEqf,gBAAiBrf,EAAIA,EAAEqf,eAC7B,IAAIgM,EAAKrrB,EAAEsrB,SAAWtrB,EAAEurB,SAExB,IAAKje,EAAOgK,iBAAoBhK,EAAOI,gBAAyB,KAAP2d,GAAe/d,EAAOK,cAAuB,KAAP0d,GAC7F,OAAO,EAET,IAAK/d,EAAOiK,iBAAoBjK,EAAOI,gBAAyB,KAAP2d,GAAe/d,EAAOK,cAAuB,KAAP0d,GAC7F,OAAO,EAET,KAAIrrB,EAAEwrB,UAAYxrB,EAAEyrB,QAAUzrB,EAAE0rB,SAAW1rB,EAAE2rB,SAGzC/xB,EAAIK,eAAiBL,EAAIK,cAAcE,WAA0D,UAA7CP,EAAIK,cAAcE,SAASqQ,eAA0E,aAA7C5Q,EAAIK,cAAcE,SAASqQ,gBAA3I,CAGA,GAAI8C,EAAO1F,OAAOgkB,SAASC,iBAA0B,KAAPR,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,GAAY,CAC/F,IAAIS,GAAS,EAEb,GAAoE,EAAhExe,EAAOC,IAAIhN,QAAS,IAAO+M,EAAO1F,OAAiB,YAAI3L,QAAsF,IAAxEqR,EAAOC,IAAIhN,QAAS,IAAO+M,EAAO1F,OAAuB,kBAAI3L,OACpI,OAEF,IAAI8vB,EAAc9wB,EAAIwpB,WAClBuH,EAAe/wB,EAAIgxB,YACnBC,EAAe5e,EAAOC,IAAI7K,SAC1BuL,IAAOie,EAAa9oB,MAAQkK,EAAOC,IAAI,GAAGtK,YAM9C,IALA,IAAIkpB,EAAc,CAChB,CAACD,EAAa9oB,KAAM8oB,EAAa/oB,KACjC,CAAC+oB,EAAa9oB,KAAOkK,EAAOF,MAAO8e,EAAa/oB,KAChD,CAAC+oB,EAAa9oB,KAAM8oB,EAAa/oB,IAAMmK,EAAOD,QAC9C,CAAC6e,EAAa9oB,KAAOkK,EAAOF,MAAO8e,EAAa/oB,IAAMmK,EAAOD,SACtDrR,EAAI,EAAGA,EAAImwB,EAAYlwB,OAAQD,GAAK,EAAG,CAC9C,IAAIsoB,EAAQ6H,EAAYnwB,GAEV,GAAZsoB,EAAM,IAAWA,EAAM,IAAMyH,GACd,GAAZzH,EAAM,IAAWA,EAAM,IAAM0H,IAEhCF,GAAS,GAGb,IAAKA,EAAU,OAEbxe,EAAOI,gBACE,KAAP2d,GAAoB,KAAPA,IACXrrB,EAAE4gB,eAAkB5gB,EAAE4gB,iBACnB5gB,EAAEosB,aAAc,IAEb,KAAPf,IAAcpd,GAAgB,KAAPod,GAAapd,IAAQX,EAAOuK,aAC5C,KAAPwT,IAAcpd,GAAgB,KAAPod,GAAapd,IAAQX,EAAO0K,cAE7C,KAAPqT,GAAoB,KAAPA,IACXrrB,EAAE4gB,eAAkB5gB,EAAE4gB,iBACnB5gB,EAAEosB,aAAc,GAEd,KAAPf,GAAa/d,EAAOuK,YACb,KAAPwT,GAAa/d,EAAO0K,aAE1B1K,EAAO/B,KAAK,WAAY8f,KAG1BgB,OAAQ,WACO1yB,KACFiyB,SAAStd,UACpBpS,EAAEtC,GAAK2F,GAAG,UAFG5F,KAEeiyB,SAASR,QAFxBzxB,KAGNiyB,SAAStd,SAAU,IAE5Bge,QAAS,WACM3yB,KACDiyB,SAAStd,UACrBpS,EAAEtC,GAAKqH,IAAI,UAFEtH,KAEgBiyB,SAASR,QAFzBzxB,KAGNiyB,SAAStd,SAAU,KAI1Bie,EAAa,CACf5f,KAAM,WACN/E,OAAQ,CACNgkB,SAAU,CACRtd,SAAS,EACTud,gBAAgB,IAGpBtf,OAAQ,WAENtG,GAAMqC,OADO3O,KACQ,CACnBiyB,SAAU,CACRtd,SAAS,EACT+d,OAAQlB,EAASkB,OAAOhgB,KAJf1S,MAKT2yB,QAASnB,EAASmB,QAAQjgB,KALjB1S,MAMTyxB,OAAQD,EAASC,OAAO/e,KANf1S,UAUf4F,GAAI,CACF6c,KAAM,WACSziB,KACFiO,OAAOgkB,SAAStd,SADd3U,KAEJiyB,SAASS,UAGpBlF,QAAS,WACMxtB,KACFiyB,SAAStd,SADP3U,KAEJiyB,SAASU,aA6BxB,IAAIE,EAAa,CACfC,eAAgBxmB,GAAMM,MACtBxF,OACoD,EAA9C9F,EAAIE,UAAUC,UAAUqB,QAAQ,WAA0B,iBA1BlE,WACE,IAAIoO,EAAY,UACZ6hB,EAAc7hB,KAAajR,EAE/B,IAAK8yB,EAAa,CAChB,IAAIC,EAAU/yB,EAAIa,cAAc,OAChCkyB,EAAQ9xB,aAAagQ,EAAW,WAChC6hB,EAA4C,mBAAvBC,EAAQ9hB,GAc/B,OAXK6hB,GACA9yB,EAAIgzB,gBACJhzB,EAAIgzB,eAAeC,aAGuB,IAA1CjzB,EAAIgzB,eAAeC,WAAW,GAAI,MAGrCH,EAAc9yB,EAAIgzB,eAAeC,WAAW,eAAgB,QAGvDH,EAMEI,GAAqB,QAAU,aAExC7U,UAAW,SAAmBjY,GAE5B,IAII+sB,EAAK,EACLC,EAAK,EACLC,EAAK,EACLC,EAAK,EAkDT,MA/CI,WAAYltB,IACdgtB,EAAKhtB,EAAEwB,QAEL,eAAgBxB,IAClBgtB,GAAMhtB,EAAEmtB,WAAa,KAEnB,gBAAiBntB,IACnBgtB,GAAMhtB,EAAEotB,YAAc,KAEpB,gBAAiBptB,IACnB+sB,GAAM/sB,EAAEqtB,YAAc,KAIpB,SAAUrtB,GAAKA,EAAEyG,OAASzG,EAAEstB,kBAC9BP,EAAKC,EACLA,EAAK,GAGPC,EA7BiB,GA6BZF,EACLG,EA9BiB,GA8BZF,EAED,WAAYhtB,IACdktB,EAAKltB,EAAEutB,QAEL,WAAYvtB,IACditB,EAAKjtB,EAAEwtB,SAGJP,GAAMC,IAAOltB,EAAEytB,YACE,IAAhBztB,EAAEytB,WACJR,GAxCc,GAyCdC,GAzCc,KA2CdD,GA1Cc,IA2CdC,GA3Cc,MAgDdD,IAAOF,IACTA,EAAME,EAAK,GAAM,EAAI,GAEnBC,IAAOF,IACTA,EAAME,EAAK,GAAM,EAAI,GAGhB,CACLQ,MAAOX,EACPY,MAAOX,EACPY,OAAQX,EACRY,OAAQX,IAGZY,iBAAkB,WACHn0B,KACNo0B,cAAe,GAExBC,iBAAkB,WACHr0B,KACNo0B,cAAe,GAExB3C,OAAQ,SAAgBrqB,GACtB,IAAIf,EAAIe,EACJuM,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAAOqmB,WAE3B,IAAK3gB,EAAOygB,eAAiBnmB,EAAOsmB,eAAkB,OAAO,EAEzDluB,EAAEqf,gBAAiBrf,EAAIA,EAAEqf,eAC7B,IAAI8O,EAAQ,EACRC,EAAY9gB,EAAOY,cAAgB,EAAI,EAEvCvP,EAAO6tB,EAAWvU,UAAUjY,GAEhC,GAAI4H,EAAOymB,YACT,GAAI/gB,EAAOI,eAAgB,CACzB,KAAIoC,KAAK8B,IAAIjT,EAAKivB,QAAU9d,KAAK8B,IAAIjT,EAAKkvB,SACnC,OAAO,EADuCM,EAAQxvB,EAAKivB,OAASQ,MAEtE,CAAA,KAAIte,KAAK8B,IAAIjT,EAAKkvB,QAAU/d,KAAK8B,IAAIjT,EAAKivB,SAC1C,OAAO,EAD8CO,EAAQxvB,EAAKkvB,YAGzEM,EAAQre,KAAK8B,IAAIjT,EAAKivB,QAAU9d,KAAK8B,IAAIjT,EAAKkvB,SAAWlvB,EAAKivB,OAASQ,GAAazvB,EAAKkvB,OAG3F,GAAc,IAAVM,EAAe,OAAO,EAI1B,GAFIvmB,EAAO0mB,SAAUH,GAASA,GAEzB7gB,EAAO1F,OAAOoU,SAaZ,CAED1O,EAAO1F,OAAOkN,MAChBxH,EAAOwK,UAET,IAAI+J,EAAWvU,EAAO9G,eAAkB2nB,EAAQvmB,EAAO2mB,YACnD/Z,EAAelH,EAAOgH,YACtBG,EAASnH,EAAOiH,MA2BpB,GAzBIsN,GAAYvU,EAAOyG,iBAAkB8N,EAAWvU,EAAOyG,gBACvD8N,GAAYvU,EAAO+G,iBAAkBwN,EAAWvU,EAAO+G,gBAE3D/G,EAAO6F,cAAc,GACrB7F,EAAO8I,aAAayL,GACpBvU,EAAO6G,iBACP7G,EAAOiI,oBACPjI,EAAOoH,wBAEDF,GAAgBlH,EAAOgH,cAAkBG,GAAUnH,EAAOiH,QAC9DjH,EAAOoH,sBAGLpH,EAAO1F,OAAOiV,iBAChBhhB,aAAayR,EAAO2gB,WAAWO,SAC/BlhB,EAAO2gB,WAAWO,QAAUvoB,GAAMI,SAAS,WACzCiH,EAAOkL,kBACN,MAGLlL,EAAO/B,KAAK,SAAUvL,GAGlBsN,EAAO1F,OAAO6mB,UAAYnhB,EAAO1F,OAAO8mB,8BAAgCphB,EAAOmhB,SAASE,OAExF9M,IAAavU,EAAOyG,gBAAkB8N,IAAavU,EAAO+G,eAAkB,OAAO,MA/C5D,CAC3B,GAAqD,GAAjDpO,GAAMM,MAAQ+G,EAAO2gB,WAAWxB,eAClC,GAAI0B,EAAQ,EACV,GAAM7gB,EAAOiH,QAASjH,EAAO1F,OAAOkN,MAAUxH,EAAOyJ,WAG9C,GAAInP,EAAOsmB,eAAkB,OAAO,OAFzC5gB,EAAOuK,YACPvK,EAAO/B,KAAK,SAAUvL,QAEnB,GAAMsN,EAAOgH,cAAehH,EAAO1F,OAAOkN,MAAUxH,EAAOyJ,WAG3D,GAAInP,EAAOsmB,eAAkB,OAAO,OAFzC5gB,EAAO0K,YACP1K,EAAO/B,KAAK,SAAUvL,GAG1BsN,EAAO2gB,WAAWxB,gBAAiB,IAAKxxB,EAAIS,MAAQkzB,UAwCtD,OAFI5uB,EAAE4gB,eAAkB5gB,EAAE4gB,iBACnB5gB,EAAEosB,aAAc,GAChB,GAETC,OAAQ,WACN,IAAI/e,EAAS3T,KACb,IAAK6yB,EAAWzrB,MAAS,OAAO,EAChC,GAAIuM,EAAO2gB,WAAW3f,QAAW,OAAO,EACxC,IAAIrO,EAASqN,EAAOC,IAQpB,MAP8C,cAA1CD,EAAO1F,OAAOqmB,WAAWY,eAC3B5uB,EAAS/D,EAAEoR,EAAO1F,OAAOqmB,WAAWY,eAEtC5uB,EAAOV,GAAG,aAAc+N,EAAO2gB,WAAWH,kBAC1C7tB,EAAOV,GAAG,aAAc+N,EAAO2gB,WAAWD,kBAC1C/tB,EAAOV,GAAGitB,EAAWzrB,MAAOuM,EAAO2gB,WAAW7C,QAC9C9d,EAAO2gB,WAAW3f,SAAU,GAG9Bge,QAAS,WACP,IAAIhf,EAAS3T,KACb,IAAK6yB,EAAWzrB,MAAS,OAAO,EAChC,IAAKuM,EAAO2gB,WAAW3f,QAAW,OAAO,EACzC,IAAIrO,EAASqN,EAAOC,IAMpB,MAL8C,cAA1CD,EAAO1F,OAAOqmB,WAAWY,eAC3B5uB,EAAS/D,EAAEoR,EAAO1F,OAAOqmB,WAAWY,eAEtC5uB,EAAOgB,IAAIurB,EAAWzrB,MAAOuM,EAAO2gB,WAAW7C,UAC/C9d,EAAO2gB,WAAW3f,SAAU,KA2C5BwgB,EAAa,CACf5hB,OAAQ,WAEN,IAAII,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAAO+c,WAE3B,IAAIrX,EAAO1F,OAAOkN,KAAlB,CACA,IAAIwU,EAAMhc,EAAOqX,WACboK,EAAUzF,EAAIyF,QACdC,EAAU1F,EAAI0F,QAEdA,GAA4B,EAAjBA,EAAQ/yB,SACjBqR,EAAOgH,YACT0a,EAAQ1xB,SAASsK,EAAOqnB,eAExBD,EAAQpxB,YAAYgK,EAAOqnB,eAE7BD,EAAQ1hB,EAAO1F,OAAO8K,eAAiBpF,EAAOoM,SAAW,WAAa,eAAe9R,EAAOsnB,YAE1FH,GAA4B,EAAjBA,EAAQ9yB,SACjBqR,EAAOiH,MACTwa,EAAQzxB,SAASsK,EAAOqnB,eAExBF,EAAQnxB,YAAYgK,EAAOqnB,eAE7BF,EAAQzhB,EAAO1F,OAAO8K,eAAiBpF,EAAOoM,SAAW,WAAa,eAAe9R,EAAOsnB,cAGhGC,YAAa,SAAqBnvB,GAEhCA,EAAE4gB,iBADWjnB,KAEF2a,cAFE3a,KAEqBiO,OAAOkN,MAF5Bnb,KAGNqe,aAEToX,YAAa,SAAqBpvB,GAEhCA,EAAE4gB,iBADWjnB,KAEF4a,QAFE5a,KAEeiO,OAAOkN,MAFtBnb,KAGNke,aAETuE,KAAM,WACJ,IAII2S,EACAC,EALA1hB,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAAO+c,YACrB/c,EAAOynB,QAAUznB,EAAO0nB,UAI1B1nB,EAAOynB,SACTN,EAAU7yB,EAAE0L,EAAOynB,QAEjB/hB,EAAO1F,OAAOiW,mBACc,iBAAlBjW,EAAOynB,QACG,EAAjBN,EAAQ9yB,QACkC,IAA1CqR,EAAOC,IAAIjI,KAAKsC,EAAOynB,QAAQpzB,SAElC8yB,EAAUzhB,EAAOC,IAAIjI,KAAKsC,EAAOynB,UAGjCznB,EAAO0nB,SACTN,EAAU9yB,EAAE0L,EAAO0nB,QAEjBhiB,EAAO1F,OAAOiW,mBACc,iBAAlBjW,EAAO0nB,QACG,EAAjBN,EAAQ/yB,QACkC,IAA1CqR,EAAOC,IAAIjI,KAAKsC,EAAO0nB,QAAQrzB,SAElC+yB,EAAU1hB,EAAOC,IAAIjI,KAAKsC,EAAO0nB,UAIjCP,GAA4B,EAAjBA,EAAQ9yB,QACrB8yB,EAAQxvB,GAAG,QAAS+N,EAAOqX,WAAWyK,aAEpCJ,GAA4B,EAAjBA,EAAQ/yB,QACrB+yB,EAAQzvB,GAAG,QAAS+N,EAAOqX,WAAWwK,aAGxClpB,GAAMqC,OAAOgF,EAAOqX,WAAY,CAC9BoK,QAASA,EACTM,OAAQN,GAAWA,EAAQ,GAC3BC,QAASA,EACTM,OAAQN,GAAWA,EAAQ,OAG/B7H,QAAS,WACP,IAAI7Z,EAAS3T,KACT2vB,EAAMhc,EAAOqX,WACboK,EAAUzF,EAAIyF,QACdC,EAAU1F,EAAI0F,QACdD,GAAWA,EAAQ9yB,SACrB8yB,EAAQ9tB,IAAI,QAASqM,EAAOqX,WAAWyK,aACvCL,EAAQnxB,YAAY0P,EAAO1F,OAAO+c,WAAWsK,gBAE3CD,GAAWA,EAAQ/yB,SACrB+yB,EAAQ/tB,IAAI,QAASqM,EAAOqX,WAAWwK,aACvCH,EAAQpxB,YAAY0P,EAAO1F,OAAO+c,WAAWsK,kBAgF/CM,EAAa,CACfriB,OAAQ,WAEN,IAAII,EAAS3T,KACTsU,EAAMX,EAAOW,IACbrG,EAAS0F,EAAO1F,OAAO4nB,WAC3B,GAAK5nB,EAAO/I,IAAOyO,EAAOkiB,WAAW3wB,IAAOyO,EAAOkiB,WAAWjiB,KAAwC,IAAjCD,EAAOkiB,WAAWjiB,IAAItR,OAA3F,CACA,IAGIwzB,EAHAhhB,EAAenB,EAAOe,SAAWf,EAAO1F,OAAOyG,QAAQC,QAAUhB,EAAOe,QAAQG,OAAOvS,OAASqR,EAAOkB,OAAOvS,OAC9GsR,EAAMD,EAAOkiB,WAAWjiB,IAGxBmiB,EAAQpiB,EAAO1F,OAAOkN,KAAOhF,KAAKE,MAAMvB,EAAsC,EAAtBnB,EAAOsK,cAAqBtK,EAAO1F,OAAOiK,gBAAkBvE,EAAOoB,SAASzS,OAcxI,GAbIqR,EAAO1F,OAAOkN,OAChB2a,EAAU3f,KAAKE,MAAM1C,EAAO8F,YAAc9F,EAAOsK,cAAgBtK,EAAO1F,OAAOiK,iBACjEpD,EAAe,EAA2B,EAAtBnB,EAAOsK,eACvC6X,GAAYhhB,EAAsC,EAAtBnB,EAAOsK,cAEvB8X,EAAQ,EAAlBD,IAAuBA,GAAWC,GAClCD,EAAU,GAAsC,YAAjCniB,EAAO1F,OAAO+nB,iBAAgCF,EAAUC,EAAQD,IAEnFA,OADqC,IAArBniB,EAAOmF,UACbnF,EAAOmF,UAEPnF,EAAO8F,aAAe,EAGd,YAAhBxL,EAAO2X,MAAsBjS,EAAOkiB,WAAWI,SAA8C,EAAnCtiB,EAAOkiB,WAAWI,QAAQ3zB,OAAY,CAClG,IACI4zB,EACAC,EACAC,EAHAH,EAAUtiB,EAAOkiB,WAAWI,QAoBhC,GAhBIhoB,EAAOooB,iBACT1iB,EAAOkiB,WAAWS,WAAaL,EAAQ1rB,GAAG,GAAGoJ,EAAOI,eAAiB,aAAe,gBAAe,GACnGH,EAAIlK,IAAIiK,EAAOI,eAAiB,QAAU,SAAYJ,EAAOkiB,WAAWS,YAAcroB,EAAOsoB,mBAAqB,GAAM,MACxF,EAA5BtoB,EAAOsoB,yBAAmDxvB,IAAzB4M,EAAOmI,gBAC1CnI,EAAOkiB,WAAWW,oBAAuBV,EAAUniB,EAAOmI,cACtDnI,EAAOkiB,WAAWW,mBAAsBvoB,EAAOsoB,mBAAqB,EACtE5iB,EAAOkiB,WAAWW,mBAAqBvoB,EAAOsoB,mBAAqB,EAC1D5iB,EAAOkiB,WAAWW,mBAAqB,IAChD7iB,EAAOkiB,WAAWW,mBAAqB,IAG3CN,EAAaJ,EAAUniB,EAAOkiB,WAAWW,mBAEzCJ,IADAD,EAAYD,GAAc/f,KAAKoM,IAAI0T,EAAQ3zB,OAAQ2L,EAAOsoB,oBAAsB,IACxDL,GAAc,GAExCD,EAAQhyB,YAAcgK,EAAwB,kBAAI,IAAOA,EAAwB,kBAAI,SAAYA,EAAwB,kBAAI,cAAiBA,EAAwB,kBAAI,SAAYA,EAAwB,kBAAI,cAAiBA,EAAwB,kBAAI,SAC9O,EAAb2F,EAAItR,OACN2zB,EAAQpsB,KAAK,SAAUO,EAAOqsB,GAC5B,IAAIC,EAAUn0B,EAAEk0B,GACZE,EAAcD,EAAQtsB,QACtBusB,IAAgBb,GAClBY,EAAQ/yB,SAASsK,EAAO2oB,mBAEtB3oB,EAAOooB,iBACUH,GAAfS,GAA6BA,GAAeR,GAC9CO,EAAQ/yB,SAAWsK,EAAwB,kBAAI,SAE7C0oB,IAAgBT,GAClBQ,EACGtrB,OACAzH,SAAWsK,EAAwB,kBAAI,SACvC7C,OACAzH,SAAWsK,EAAwB,kBAAI,cAExC0oB,IAAgBR,GAClBO,EACG1rB,OACArH,SAAWsK,EAAwB,kBAAI,SACvCjD,OACArH,SAAWsK,EAAwB,kBAAI,sBAOhD,GAFcgoB,EAAQ1rB,GAAGurB,GACjBnyB,SAASsK,EAAO2oB,mBACpB3oB,EAAOooB,eAAgB,CAGzB,IAFA,IAAIQ,EAAwBZ,EAAQ1rB,GAAG2rB,GACnCY,EAAuBb,EAAQ1rB,GAAG4rB,GAC7B9zB,EAAI6zB,EAAY7zB,GAAK8zB,EAAW9zB,GAAK,EAC5C4zB,EAAQ1rB,GAAGlI,GAAGsB,SAAWsK,EAAwB,kBAAI,SAEvD4oB,EACGzrB,OACAzH,SAAWsK,EAAwB,kBAAI,SACvC7C,OACAzH,SAAWsK,EAAwB,kBAAI,cAC1C6oB,EACG9rB,OACArH,SAAWsK,EAAwB,kBAAI,SACvCjD,OACArH,SAAWsK,EAAwB,kBAAI,cAG9C,GAAIA,EAAOooB,eAAgB,CACzB,IAAIU,EAAuB5gB,KAAKoM,IAAI0T,EAAQ3zB,OAAQ2L,EAAOsoB,mBAAqB,GAC5ES,GAAmBrjB,EAAOkiB,WAAWS,WAAaS,EAAyBpjB,EAAOkiB,WAAqB,YAAK,EAAMO,EAAWziB,EAAOkiB,WAAWS,WAC/IhG,EAAahc,EAAM,QAAU,OACjC2hB,EAAQvsB,IAAIiK,EAAOI,eAAiBuc,EAAa,MAAQ0G,EAAgB,OAO7E,GAJoB,aAAhB/oB,EAAO2X,OACThS,EAAIjI,KAAM,IAAOsC,EAAmB,cAAInE,KAAKmE,EAAOgpB,sBAAsBnB,EAAU,IACpFliB,EAAIjI,KAAM,IAAOsC,EAAiB,YAAInE,KAAKmE,EAAOipB,oBAAoBnB,KAEpD,gBAAhB9nB,EAAO2X,KAAwB,CACjC,IAAIuR,EAEFA,EADElpB,EAAOmpB,oBACczjB,EAAOI,eAAiB,WAAa,aAErCJ,EAAOI,eAAiB,aAAe,WAEhE,IAAIsjB,GAASvB,EAAU,GAAKC,EACxBuB,EAAS,EACTC,EAAS,EACgB,eAAzBJ,EACFG,EAASD,EAETE,EAASF,EAEXzjB,EAAIjI,KAAM,IAAOsC,EAA2B,sBAAI5I,UAAW,6BAA+BiyB,EAAS,YAAcC,EAAS,KAAM/xB,WAAWmO,EAAO1F,OAAOoL,OAEvI,WAAhBpL,EAAO2X,MAAqB3X,EAAOupB,cACrC5jB,EAAIhR,KAAKqL,EAAOupB,aAAa7jB,EAAQmiB,EAAU,EAAGC,IAClDpiB,EAAO/B,KAAK,mBAAoB+B,EAAQC,EAAI,KAE5CD,EAAO/B,KAAK,mBAAoB+B,EAAQC,EAAI,IAE9CA,EAAID,EAAO1F,OAAO8K,eAAiBpF,EAAOoM,SAAW,WAAa,eAAe9R,EAAOsnB,aAE1FkC,OAAQ,WAEN,IAAI9jB,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAAO4nB,WAC3B,GAAK5nB,EAAO/I,IAAOyO,EAAOkiB,WAAW3wB,IAAOyO,EAAOkiB,WAAWjiB,KAAwC,IAAjCD,EAAOkiB,WAAWjiB,IAAItR,OAA3F,CACA,IAAIwS,EAAenB,EAAOe,SAAWf,EAAO1F,OAAOyG,QAAQC,QAAUhB,EAAOe,QAAQG,OAAOvS,OAASqR,EAAOkB,OAAOvS,OAE9GsR,EAAMD,EAAOkiB,WAAWjiB,IACxB8jB,EAAiB,GACrB,GAAoB,YAAhBzpB,EAAO2X,KAAoB,CAE7B,IADA,IAAI+R,EAAkBhkB,EAAO1F,OAAOkN,KAAOhF,KAAKE,MAAMvB,EAAsC,EAAtBnB,EAAOsK,cAAqBtK,EAAO1F,OAAOiK,gBAAkBvE,EAAOoB,SAASzS,OACzID,EAAI,EAAGA,EAAIs1B,EAAiBt1B,GAAK,EACpC4L,EAAO2pB,aACTF,GAAkBzpB,EAAO2pB,aAAarvB,KAAKoL,EAAQtR,EAAG4L,EAAO4pB,aAE7DH,GAAkB,IAAOzpB,EAAoB,cAAI,WAAeA,EAAkB,YAAI,OAAWA,EAAoB,cAAI,IAG7H2F,EAAIhR,KAAK80B,GACT/jB,EAAOkiB,WAAWI,QAAUriB,EAAIjI,KAAM,IAAOsC,EAAkB,aAE7C,aAAhBA,EAAO2X,OAEP8R,EADEzpB,EAAO6pB,eACQ7pB,EAAO6pB,eAAevvB,KAAKoL,EAAQ1F,EAAO8pB,aAAc9pB,EAAO+pB,YAE/D,gBAAoB/pB,EAAmB,aAAI,4BAEtCA,EAAiB,WAAI,YAE7C2F,EAAIhR,KAAK80B,IAES,gBAAhBzpB,EAAO2X,OAEP8R,EADEzpB,EAAOgqB,kBACQhqB,EAAOgqB,kBAAkB1vB,KAAKoL,EAAQ1F,EAAOiqB,sBAE7C,gBAAoBjqB,EAA2B,qBAAI,YAEtE2F,EAAIhR,KAAK80B,IAES,WAAhBzpB,EAAO2X,MACTjS,EAAO/B,KAAK,mBAAoB+B,EAAOkiB,WAAWjiB,IAAI,MAG1D6O,KAAM,WACJ,IAAI9O,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAAO4nB,WAC3B,GAAK5nB,EAAO/I,GAAZ,CAEA,IAAI0O,EAAMrR,EAAE0L,EAAO/I,IACA,IAAf0O,EAAItR,SAGNqR,EAAO1F,OAAOiW,mBACU,iBAAdjW,EAAO/I,IACD,EAAb0O,EAAItR,QACkC,IAAtCqR,EAAOC,IAAIjI,KAAKsC,EAAO/I,IAAI5C,SAE9BsR,EAAMD,EAAOC,IAAIjI,KAAKsC,EAAO/I,KAGX,YAAhB+I,EAAO2X,MAAsB3X,EAAOkqB,WACtCvkB,EAAIjQ,SAASsK,EAAOmqB,gBAGtBxkB,EAAIjQ,SAASsK,EAAOoqB,cAAgBpqB,EAAO2X,MAEvB,YAAhB3X,EAAO2X,MAAsB3X,EAAOooB,iBACtCziB,EAAIjQ,SAAU,GAAMsK,EAAoB,cAAKA,EAAW,KAAI,YAC5D0F,EAAOkiB,WAAWW,mBAAqB,EACnCvoB,EAAOsoB,mBAAqB,IAC9BtoB,EAAOsoB,mBAAqB,IAGZ,gBAAhBtoB,EAAO2X,MAA0B3X,EAAOmpB,qBAC1CxjB,EAAIjQ,SAASsK,EAAOqqB,0BAGlBrqB,EAAOkqB,WACTvkB,EAAIhO,GAAG,QAAU,IAAOqI,EAAkB,YAAI,SAAiB5H,GAC7DA,EAAE4gB,iBACF,IAAI7c,EAAQ7H,EAAEvC,MAAMoK,QAAUuJ,EAAO1F,OAAOiK,eACxCvE,EAAO1F,OAAOkN,OAAQ/Q,GAASuJ,EAAOsK,cAC1CtK,EAAO0J,QAAQjT,KAInBkC,GAAMqC,OAAOgF,EAAOkiB,WAAY,CAC9BjiB,IAAKA,EACL1O,GAAI0O,EAAI,QAGZ4Z,QAAS,WACP,IAAI7Z,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAAO4nB,WAC3B,GAAK5nB,EAAO/I,IAAOyO,EAAOkiB,WAAW3wB,IAAOyO,EAAOkiB,WAAWjiB,KAAwC,IAAjCD,EAAOkiB,WAAWjiB,IAAItR,OAA3F,CACA,IAAIsR,EAAMD,EAAOkiB,WAAWjiB,IAE5BA,EAAI3P,YAAYgK,EAAOsqB,aACvB3kB,EAAI3P,YAAYgK,EAAOoqB,cAAgBpqB,EAAO2X,MAC1CjS,EAAOkiB,WAAWI,SAAWtiB,EAAOkiB,WAAWI,QAAQhyB,YAAYgK,EAAO2oB,mBAC1E3oB,EAAOkqB,WACTvkB,EAAItM,IAAI,QAAU,IAAO2G,EAAkB,gBA0G7CuqB,EAAY,CACd/b,aAAc,WACZ,IAAI9I,EAAS3T,KACb,GAAK2T,EAAO1F,OAAOwqB,UAAUvzB,IAAOyO,EAAO8kB,UAAUvzB,GAArD,CACA,IAAIuzB,EAAY9kB,EAAO8kB,UACnBnkB,EAAMX,EAAOY,aACbgG,EAAW5G,EAAO4G,SAClBme,EAAWD,EAAUC,SACrBC,EAAYF,EAAUE,UACtBC,EAAUH,EAAUG,QACpBhlB,EAAM6kB,EAAU7kB,IAChB3F,EAAS0F,EAAO1F,OAAOwqB,UAEvBI,EAAUH,EACVI,GAAUH,EAAYD,GAAYne,EAClCjG,EAEW,GADbwkB,GAAUA,IAERD,EAAUH,EAAWI,EACrBA,EAAS,GACqBH,GAApBG,EAASJ,IACnBG,EAAUF,EAAYG,GAEfA,EAAS,GAClBD,EAAUH,EAAWI,EACrBA,EAAS,GACoBH,EAApBG,EAASJ,IAClBG,EAAUF,EAAYG,GAEpBnlB,EAAOI,gBACL1E,GAAQU,aACV6oB,EAAQvzB,UAAW,eAAiByzB,EAAS,aAE7CF,EAAQvzB,UAAW,cAAgByzB,EAAS,OAE9CF,EAAQ,GAAG33B,MAAMwS,MAAQolB,EAAU,OAE/BxpB,GAAQU,aACV6oB,EAAQvzB,UAAW,oBAAsByzB,EAAS,UAElDF,EAAQvzB,UAAW,cAAgByzB,EAAS,OAE9CF,EAAQ,GAAG33B,MAAMyS,OAASmlB,EAAU,MAElC5qB,EAAO8qB,OACT72B,aAAayR,EAAO8kB,UAAU5D,SAC9BjhB,EAAI,GAAG3S,MAAM+3B,QAAU,EACvBrlB,EAAO8kB,UAAU5D,QAAU5yB,WAAW,WACpC2R,EAAI,GAAG3S,MAAM+3B,QAAU,EACvBplB,EAAIpO,WAAW,MACd,QAGPgU,cAAe,SAAuB/T,GACvBzF,KACDiO,OAAOwqB,UAAUvzB,IADhBlF,KAC8By4B,UAAUvzB,IADxClF,KAENy4B,UAAUG,QAAQpzB,WAAWC,IAEtC+N,WAAY,WACV,IAAIG,EAAS3T,KACb,GAAK2T,EAAO1F,OAAOwqB,UAAUvzB,IAAOyO,EAAO8kB,UAAUvzB,GAArD,CAEA,IAAIuzB,EAAY9kB,EAAO8kB,UACnBG,EAAUH,EAAUG,QACpBhlB,EAAM6kB,EAAU7kB,IAEpBglB,EAAQ,GAAG33B,MAAMwS,MAAQ,GACzBmlB,EAAQ,GAAG33B,MAAMyS,OAAS,GAC1B,IAIIglB,EAJAC,EAAYhlB,EAAOI,eAAiBH,EAAI,GAAGjL,YAAciL,EAAI,GAAG9K,aAEhEmwB,EAAUtlB,EAAOO,KAAOP,EAAOkC,YAC/BqjB,EAAcD,GAAWN,EAAYhlB,EAAOO,MAG9CwkB,EADuC,SAArC/kB,EAAO1F,OAAOwqB,UAAUC,SACfC,EAAYM,EAEZhlB,SAASN,EAAO1F,OAAOwqB,UAAUC,SAAU,IAGpD/kB,EAAOI,eACT6kB,EAAQ,GAAG33B,MAAMwS,MAAQilB,EAAW,KAEpCE,EAAQ,GAAG33B,MAAMyS,OAASglB,EAAW,KAIrC9kB,EAAI,GAAG3S,MAAMk4B,QADA,GAAXF,EACqB,OAEA,GAErBtlB,EAAO1F,OAAOwqB,UAAUM,OAC1BnlB,EAAI,GAAG3S,MAAM+3B,QAAU,GAEzB1sB,GAAMqC,OAAO8pB,EAAW,CACtBE,UAAWA,EACXM,QAASA,EACTC,YAAaA,EACbR,SAAUA,IAEZD,EAAU7kB,IAAID,EAAO1F,OAAO8K,eAAiBpF,EAAOoM,SAAW,WAAa,eAAepM,EAAO1F,OAAOwqB,UAAUlD,aAErH6D,gBAAiB,SAAyB/yB,GACxC,IAaIgzB,EAbA1lB,EAAS3T,KACTy4B,EAAY9kB,EAAO8kB,UACnBnkB,EAAMX,EAAOY,aACbX,EAAM6kB,EAAU7kB,IAChB8kB,EAAWD,EAAUC,SACrBC,EAAYF,EAAUE,UAS1BU,IANI1lB,EAAOI,eACsB,eAAX1N,EAAEuf,MAAoC,cAAXvf,EAAEuf,KAAwBvf,EAAE8f,cAAc,GAAGC,MAAQ/f,EAAE+f,OAAS/f,EAAEizB,QAElF,eAAXjzB,EAAEuf,MAAoC,cAAXvf,EAAEuf,KAAwBvf,EAAE8f,cAAc,GAAGG,MAAQjgB,EAAEigB,OAASjgB,EAAEkzB,SAG9E3lB,EAAI7K,SAAS4K,EAAOI,eAAiB,OAAS,OAAU2kB,EAAW,IAAOC,EAAYD,GAC3HW,EAAgBljB,KAAKK,IAAIL,KAAKoM,IAAI8W,EAAe,GAAI,GACjD/kB,IACF+kB,EAAgB,EAAIA,GAGtB,IAAInR,EAAWvU,EAAOyG,gBAAmBzG,EAAO+G,eAAiB/G,EAAOyG,gBAAkBif,EAE1F1lB,EAAO6G,eAAe0N,GACtBvU,EAAO8I,aAAayL,GACpBvU,EAAOiI,oBACPjI,EAAOoH,uBAETye,YAAa,SAAqBnzB,GAChC,IAAIsN,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAAOwqB,UACvBA,EAAY9kB,EAAO8kB,UACnBrkB,EAAaT,EAAOS,WACpBR,EAAM6kB,EAAU7kB,IAChBglB,EAAUH,EAAUG,QACxBjlB,EAAO8kB,UAAU1S,WAAY,EAC7B1f,EAAE4gB,iBACF5gB,EAAEwhB,kBAEFzT,EAAW5O,WAAW,KACtBozB,EAAQpzB,WAAW,KACnBizB,EAAUW,gBAAgB/yB,GAE1BnE,aAAayR,EAAO8kB,UAAUgB,aAE9B7lB,EAAIpO,WAAW,GACXyI,EAAO8qB,MACTnlB,EAAIlK,IAAI,UAAW,GAErBiK,EAAO/B,KAAK,qBAAsBvL,IAEpCqzB,WAAY,SAAoBrzB,GAC9B,IACIoyB,EADSz4B,KACUy4B,UACnBrkB,EAFSpU,KAEWoU,WACpBR,EAAM6kB,EAAU7kB,IAChBglB,EAAUH,EAAUG,QAJX54B,KAMDy4B,UAAU1S,YAClB1f,EAAE4gB,eAAkB5gB,EAAE4gB,iBACnB5gB,EAAEosB,aAAc,EACvBgG,EAAUW,gBAAgB/yB,GAC1B+N,EAAW5O,WAAW,GACtBoO,EAAIpO,WAAW,GACfozB,EAAQpzB,WAAW,GAZNxF,KAaN4R,KAAK,oBAAqBvL,KAEnCszB,UAAW,SAAmBtzB,GAC5B,IAAIsN,EAAS3T,KAETiO,EAAS0F,EAAO1F,OAAOwqB,UAEvB7kB,EADYD,EAAO8kB,UACH7kB,IAEfD,EAAO8kB,UAAU1S,YACtBpS,EAAO8kB,UAAU1S,WAAY,EACzB9X,EAAO8qB,OACT72B,aAAayR,EAAO8kB,UAAUgB,aAC9B9lB,EAAO8kB,UAAUgB,YAAcntB,GAAMI,SAAS,WAC5CkH,EAAIlK,IAAI,UAAW,GACnBkK,EAAIpO,WAAW,MACd,MAELmO,EAAO/B,KAAK,mBAAoBvL,GAC5B4H,EAAO2rB,eACTjmB,EAAOkL,mBAGXgb,gBAAiB,WACf,IAAIlmB,EAAS3T,KACb,GAAK2T,EAAO1F,OAAOwqB,UAAUvzB,GAA7B,CACA,IAAIuzB,EAAY9kB,EAAO8kB,UACnB3L,EAAmBnZ,EAAOmZ,iBAC1BC,EAAqBpZ,EAAOoZ,mBAC5B9e,EAAS0F,EAAO1F,OAEhB3H,EADMmyB,EAAU7kB,IACH,GACbkmB,KAAiBzqB,GAAQc,kBAAmBlC,EAAO4W,mBAAmB,CAAE+E,SAAS,EAAOzjB,SAAS,GACjGgK,KAAkBd,GAAQc,kBAAmBlC,EAAO4W,mBAAmB,CAAE+E,SAAS,EAAMzjB,SAAS,GAChGkJ,GAAQC,OAKXhJ,EAAOlG,iBAAiB0sB,EAAiBnD,MAAOhW,EAAO8kB,UAAUe,YAAaM,GAC9ExzB,EAAOlG,iBAAiB0sB,EAAiBjD,KAAMlW,EAAO8kB,UAAUiB,WAAYI,GAC5ExzB,EAAOlG,iBAAiB0sB,EAAiBhD,IAAKnW,EAAO8kB,UAAUkB,UAAWxpB,KAN1E7J,EAAOlG,iBAAiB2sB,EAAmBpD,MAAOhW,EAAO8kB,UAAUe,YAAaM,GAChF75B,EAAIG,iBAAiB2sB,EAAmBlD,KAAMlW,EAAO8kB,UAAUiB,WAAYI,GAC3E75B,EAAIG,iBAAiB2sB,EAAmBjD,IAAKnW,EAAO8kB,UAAUkB,UAAWxpB,MAO7E4pB,iBAAkB,WAChB,IAAIpmB,EAAS3T,KACb,GAAK2T,EAAO1F,OAAOwqB,UAAUvzB,GAA7B,CACA,IAAIuzB,EAAY9kB,EAAO8kB,UACnB3L,EAAmBnZ,EAAOmZ,iBAC1BC,EAAqBpZ,EAAOoZ,mBAC5B9e,EAAS0F,EAAO1F,OAEhB3H,EADMmyB,EAAU7kB,IACH,GACbkmB,KAAiBzqB,GAAQc,kBAAmBlC,EAAO4W,mBAAmB,CAAE+E,SAAS,EAAOzjB,SAAS,GACjGgK,KAAkBd,GAAQc,kBAAmBlC,EAAO4W,mBAAmB,CAAE+E,SAAS,EAAMzjB,SAAS,GAChGkJ,GAAQC,OAKXhJ,EAAOjG,oBAAoBysB,EAAiBnD,MAAOhW,EAAO8kB,UAAUe,YAAaM,GACjFxzB,EAAOjG,oBAAoBysB,EAAiBjD,KAAMlW,EAAO8kB,UAAUiB,WAAYI,GAC/ExzB,EAAOjG,oBAAoBysB,EAAiBhD,IAAKnW,EAAO8kB,UAAUkB,UAAWxpB,KAN7E7J,EAAOjG,oBAAoB0sB,EAAmBpD,MAAOhW,EAAO8kB,UAAUe,YAAaM,GACnF75B,EAAII,oBAAoB0sB,EAAmBlD,KAAMlW,EAAO8kB,UAAUiB,WAAYI,GAC9E75B,EAAII,oBAAoB0sB,EAAmBjD,IAAKnW,EAAO8kB,UAAUkB,UAAWxpB,MAOhFsS,KAAM,WACJ,IAAI9O,EAAS3T,KACb,GAAK2T,EAAO1F,OAAOwqB,UAAUvzB,GAA7B,CACA,IAAIuzB,EAAY9kB,EAAO8kB,UACnBuB,EAAYrmB,EAAOC,IACnB3F,EAAS0F,EAAO1F,OAAOwqB,UAEvB7kB,EAAMrR,EAAE0L,EAAO/I,IACfyO,EAAO1F,OAAOiW,mBAA0C,iBAAdjW,EAAO/I,IAAgC,EAAb0O,EAAItR,QAAmD,IAArC03B,EAAUruB,KAAKsC,EAAO/I,IAAI5C,SAClHsR,EAAMomB,EAAUruB,KAAKsC,EAAO/I,KAG9B,IAAI0zB,EAAUhlB,EAAIjI,KAAM,IAAOgI,EAAO1F,OAAOwqB,UAAmB,WACzC,IAAnBG,EAAQt2B,SACVs2B,EAAUr2B,EAAG,eAAmBoR,EAAO1F,OAAOwqB,UAAmB,UAAI,YACrE7kB,EAAInJ,OAAOmuB,IAGbtsB,GAAMqC,OAAO8pB,EAAW,CACtB7kB,IAAKA,EACL1O,GAAI0O,EAAI,GACRglB,QAASA,EACTqB,OAAQrB,EAAQ,KAGd3qB,EAAOisB,WACTzB,EAAUoB,oBAGdrM,QAAS,WACMxtB,KACNy4B,UAAUsB,qBAwEjBI,EAAW,CACbC,aAAc,SAAsBl1B,EAAIqV,GACtC,IACIjG,EADStU,KACIsU,IAEbV,EAAMrR,EAAE2C,GACRuvB,EAAYngB,GAAO,EAAI,EAEvB+lB,EAAIzmB,EAAIrP,KAAK,yBAA2B,IACxCoY,EAAI/I,EAAIrP,KAAK,0BACbqY,EAAIhJ,EAAIrP,KAAK,0BACb8yB,EAAQzjB,EAAIrP,KAAK,8BACjBy0B,EAAUplB,EAAIrP,KAAK,gCAwBvB,GAtBIoY,GAAKC,GACPD,EAAIA,GAAK,IACTC,EAAIA,GAAK,KAdE5c,KAeK+T,gBAChB4I,EAAI0d,EACJzd,EAAI,MAEJA,EAAIyd,EACJ1d,EAAI,KAIJA,EADsB,GAApB,EAAI7Z,QAAQ,KACTmR,SAAS0I,EAAG,IAAMpC,EAAWka,EAAa,IAE1C9X,EAAIpC,EAAWka,EAAa,KAGjC7X,EADsB,GAApB,EAAI9Z,QAAQ,KACTmR,SAAS2I,EAAG,IAAMrC,EAAY,IAE9BqC,EAAIrC,EAAY,KAGnB,MAAOye,EAA6C,CACtD,IAAIsB,EAAiBtB,GAAYA,EAAU,IAAM,EAAI7iB,KAAK8B,IAAIsC,IAC9D3G,EAAI,GAAG3S,MAAM+3B,QAAUsB,EAEzB,GAAI,MAAOjD,EACTzjB,EAAIvO,UAAW,eAAiBsX,EAAI,KAAOC,EAAI,cAC1C,CACL,IAAI2d,EAAelD,GAAUA,EAAQ,IAAM,EAAIlhB,KAAK8B,IAAIsC,IACxD3G,EAAIvO,UAAW,eAAiBsX,EAAI,KAAOC,EAAI,gBAAkB2d,EAAe,OAGpF9d,aAAc,WACZ,IAAI9I,EAAS3T,KACT4T,EAAMD,EAAOC,IACbiB,EAASlB,EAAOkB,OAChB0F,EAAW5G,EAAO4G,SAClBxF,EAAWpB,EAAOoB,SACtBnB,EAAI7S,SAAS,8EACV8I,KAAK,SAAUO,EAAOlF,GACrByO,EAAO6mB,SAASJ,aAAal1B,EAAIqV,KAErC1F,EAAOhL,KAAK,SAAU0T,EAAYgQ,GAChC,IAAIpT,EAAgBoT,EAAQhT,SACO,EAA/B5G,EAAO1F,OAAOiK,gBAAsD,SAAhCvE,EAAO1F,OAAOqI,gBACpD6D,GAAiBhE,KAAKE,KAAKkH,EAAa,GAAMhD,GAAYxF,EAASzS,OAAS,IAE9E6X,EAAgBhE,KAAKoM,IAAIpM,KAAKK,IAAI2D,GAAgB,GAAI,GACtD5X,EAAEgrB,GAAS5hB,KAAK,8EACb9B,KAAK,SAAUO,EAAOlF,GACrByO,EAAO6mB,SAASJ,aAAal1B,EAAIiV,QAIzCX,cAAe,SAAuB/T,QAClB,IAAbA,IAAsBA,EAAWzF,KAAKiO,OAAOoL,OAErCrZ,KACI4T,IACbjI,KAAK,8EACN9B,KAAK,SAAUO,EAAOqwB,GACrB,IAAIC,EAAcn4B,EAAEk4B,GAChBE,EAAmB1mB,SAASymB,EAAYn2B,KAAK,iCAAkC,KAAOkB,EACzE,IAAbA,IAAkBk1B,EAAmB,GACzCD,EAAYl1B,WAAWm1B,OA+C3BC,EAAO,CAETC,0BAA2B,SAAmCx0B,GAC5D,GAAIA,EAAE8f,cAAc7jB,OAAS,EAAK,OAAO,EACzC,IAAIw4B,EAAKz0B,EAAE8f,cAAc,GAAGC,MACxB2U,EAAK10B,EAAE8f,cAAc,GAAGG,MACxB0U,EAAK30B,EAAE8f,cAAc,GAAGC,MACxB6U,EAAK50B,EAAE8f,cAAc,GAAGG,MAE5B,OADenQ,KAAKqR,KAAMrR,KAAKsR,IAAMuT,EAAKF,EAAK,GAAQ3kB,KAAKsR,IAAMwT,EAAKF,EAAK,KAI9EG,eAAgB,SAAwB70B,GACtC,IAAIsN,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAAOktB,KACvBA,EAAOxnB,EAAOwnB,KACdC,EAAUD,EAAKC,QAGnB,GAFAD,EAAKE,oBAAqB,EAC1BF,EAAKG,kBAAmB,GACnBjsB,GAAQmB,SAAU,CACrB,GAAe,eAAXnK,EAAEuf,MAAqC,eAAXvf,EAAEuf,MAAyBvf,EAAE8f,cAAc7jB,OAAS,EAClF,OAEF64B,EAAKE,oBAAqB,EAC1BD,EAAQG,WAAaX,EAAKC,0BAA0Bx0B,GAEjD+0B,EAAQnK,UAAamK,EAAQnK,SAAS3uB,SACzC84B,EAAQnK,SAAW1uB,EAAE8D,EAAEC,QAAQoF,QAAQ,iBACP,IAA5B0vB,EAAQnK,SAAS3uB,SAAgB84B,EAAQnK,SAAWtd,EAAOkB,OAAOtK,GAAGoJ,EAAO8F,cAChF2hB,EAAQI,SAAWJ,EAAQnK,SAAStlB,KAAK,oBACzCyvB,EAAQK,aAAeL,EAAQI,SAAShwB,OAAQ,IAAOyC,EAAqB,gBAC5EmtB,EAAQM,SAAWN,EAAQK,aAAal3B,KAAK,qBAAuB0J,EAAOytB,SACvC,IAAhCN,EAAQK,aAAan5B,SAK3B84B,EAAQI,SAASh2B,WAAW,GAC5BmO,EAAOwnB,KAAKQ,WAAY,GALpBP,EAAQI,cAAWz0B,GAOzB60B,gBAAiB,SAAyBv1B,GACxC,IACI4H,EADSjO,KACOiO,OAAOktB,KACvBA,EAFSn7B,KAEKm7B,KACdC,EAAUD,EAAKC,QACnB,IAAK/rB,GAAQmB,SAAU,CACrB,GAAe,cAAXnK,EAAEuf,MAAoC,cAAXvf,EAAEuf,MAAwBvf,EAAE8f,cAAc7jB,OAAS,EAChF,OAEF64B,EAAKG,kBAAmB,EACxBF,EAAQS,UAAYjB,EAAKC,0BAA0Bx0B,GAEhD+0B,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASl5B,SAExC64B,EAAK9D,MADHhoB,GAAQmB,SACGnK,EAAEgxB,MAAQ8D,EAAKZ,aAEda,EAAQS,UAAYT,EAAQG,WAAcJ,EAAKZ,aAE3DY,EAAK9D,MAAQ+D,EAAQM,WACvBP,EAAK9D,MAAS+D,EAAQM,SAAW,EAAMvlB,KAAKsR,IAAO0T,EAAK9D,MAAQ+D,EAAQM,SAAY,EAAI,KAEtFP,EAAK9D,MAAQppB,EAAO6tB,WACtBX,EAAK9D,MAASppB,EAAO6tB,SAAW,EAAM3lB,KAAKsR,IAAOxZ,EAAO6tB,SAAWX,EAAK9D,MAAS,EAAI,KAExF+D,EAAQI,SAASn2B,UAAW,4BAA+B81B,EAAU,MAAI,OAE3EY,aAAc,SAAsB11B,GAClC,IACI4H,EADSjO,KACOiO,OAAOktB,KACvBA,EAFSn7B,KAEKm7B,KACdC,EAAUD,EAAKC,QACnB,IAAK/rB,GAAQmB,SAAU,CACrB,IAAK2qB,EAAKE,qBAAuBF,EAAKG,iBACpC,OAEF,GAAe,aAAXj1B,EAAEuf,MAAmC,aAAXvf,EAAEuf,MAAuBvf,EAAE21B,eAAe15B,OAAS,IAAMwe,EAAOG,QAC5F,OAEFka,EAAKE,oBAAqB,EAC1BF,EAAKG,kBAAmB,EAErBF,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASl5B,SAC1C64B,EAAK9D,MAAQlhB,KAAKK,IAAIL,KAAKoM,IAAI4Y,EAAK9D,MAAO+D,EAAQM,UAAWztB,EAAO6tB,UACrEV,EAAQI,SAASh2B,WAhBJxF,KAgBsBiO,OAAOoL,OAAOhU,UAAW,4BAA+B81B,EAAU,MAAI,KACzGA,EAAKZ,aAAeY,EAAK9D,MACzB8D,EAAKQ,WAAY,EACE,IAAfR,EAAK9D,QAAe+D,EAAQnK,cAAWlqB,KAE7Cwe,aAAc,SAAsBlf,GAClC,IACI80B,EADSn7B,KACKm7B,KACdC,EAAUD,EAAKC,QACfvP,EAAQsP,EAAKtP,MACZuP,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASl5B,SACtCupB,EAAM9F,YACNjF,EAAOG,SAAW5a,EAAE4gB,iBACxB4E,EAAM9F,WAAY,EAClB8F,EAAMoQ,aAAatf,EAAe,eAAXtW,EAAEuf,KAAwBvf,EAAE8f,cAAc,GAAGC,MAAQ/f,EAAE+f,MAC9EyF,EAAMoQ,aAAarf,EAAe,eAAXvW,EAAEuf,KAAwBvf,EAAE8f,cAAc,GAAGG,MAAQjgB,EAAEigB,SAEhFc,YAAa,SAAqB/gB,GAChC,IAAIsN,EAAS3T,KACTm7B,EAAOxnB,EAAOwnB,KACdC,EAAUD,EAAKC,QACfvP,EAAQsP,EAAKtP,MACb/C,EAAWqS,EAAKrS,SACpB,GAAKsS,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASl5B,SAC1CqR,EAAOsS,YAAa,EACf4F,EAAM9F,WAAcqV,EAAQnK,UAAjC,CAEKpF,EAAM7F,UACT6F,EAAMpY,MAAQ2nB,EAAQI,SAAS,GAAG7yB,YAClCkjB,EAAMnY,OAAS0nB,EAAQI,SAAS,GAAG1yB,aACnC+iB,EAAMtF,OAASja,GAAMO,aAAauuB,EAAQK,aAAa,GAAI,MAAQ,EACnE5P,EAAMrF,OAASla,GAAMO,aAAauuB,EAAQK,aAAa,GAAI,MAAQ,EACnEL,EAAQc,WAAad,EAAQnK,SAAS,GAAGtoB,YACzCyyB,EAAQe,YAAcf,EAAQnK,SAAS,GAAGnoB,aAC1CsyB,EAAQK,aAAaj2B,WAAW,GAC5BmO,EAAOW,MACTuX,EAAMtF,QAAUsF,EAAMtF,OACtBsF,EAAMrF,QAAUqF,EAAMrF,SAI1B,IAAI4V,EAAcvQ,EAAMpY,MAAQ0nB,EAAK9D,MACjCgF,EAAexQ,EAAMnY,OAASynB,EAAK9D,MAEvC,KAAI+E,EAAchB,EAAQc,YAAcG,EAAejB,EAAQe,aAA/D,CAUA,GARAtQ,EAAMyQ,KAAOnmB,KAAKoM,IAAM6Y,EAAQc,WAAa,EAAME,EAAc,EAAK,GACtEvQ,EAAM0Q,MAAQ1Q,EAAMyQ,KACpBzQ,EAAM2Q,KAAOrmB,KAAKoM,IAAM6Y,EAAQe,YAAc,EAAME,EAAe,EAAK,GACxExQ,EAAM4Q,MAAQ5Q,EAAM2Q,KAEpB3Q,EAAM6Q,eAAe/f,EAAe,cAAXtW,EAAEuf,KAAuBvf,EAAE8f,cAAc,GAAGC,MAAQ/f,EAAE+f,MAC/EyF,EAAM6Q,eAAe9f,EAAe,cAAXvW,EAAEuf,KAAuBvf,EAAE8f,cAAc,GAAGG,MAAQjgB,EAAEigB,OAE1EuF,EAAM7F,UAAYmV,EAAKQ,UAAW,CACrC,GACEhoB,EAAOI,iBAEJoC,KAAKC,MAAMyV,EAAMyQ,QAAUnmB,KAAKC,MAAMyV,EAAMtF,SAAWsF,EAAM6Q,eAAe/f,EAAIkP,EAAMoQ,aAAatf,GAChGxG,KAAKC,MAAMyV,EAAM0Q,QAAUpmB,KAAKC,MAAMyV,EAAMtF,SAAWsF,EAAM6Q,eAAe/f,EAAIkP,EAAMoQ,aAAatf,GAIzG,YADAkP,EAAM9F,WAAY,GAElB,IACCpS,EAAOI,iBAELoC,KAAKC,MAAMyV,EAAM2Q,QAAUrmB,KAAKC,MAAMyV,EAAMrF,SAAWqF,EAAM6Q,eAAe9f,EAAIiP,EAAMoQ,aAAarf,GAChGzG,KAAKC,MAAMyV,EAAM4Q,QAAUtmB,KAAKC,MAAMyV,EAAMrF,SAAWqF,EAAM6Q,eAAe9f,EAAIiP,EAAMoQ,aAAarf,GAIzG,YADAiP,EAAM9F,WAAY,GAItB1f,EAAE4gB,iBACF5gB,EAAEwhB,kBAEFgE,EAAM7F,SAAU,EAChB6F,EAAM3F,SAAY2F,EAAM6Q,eAAe/f,EAAIkP,EAAMoQ,aAAatf,EAAKkP,EAAMtF,OACzEsF,EAAMxF,SAAYwF,EAAM6Q,eAAe9f,EAAIiP,EAAMoQ,aAAarf,EAAKiP,EAAMrF,OAErEqF,EAAM3F,SAAW2F,EAAMyQ,OACzBzQ,EAAM3F,SAAY2F,EAAMyQ,KAAO,EAAMnmB,KAAKsR,IAAOoE,EAAMyQ,KAAOzQ,EAAM3F,SAAY,EAAI,KAElF2F,EAAM3F,SAAW2F,EAAM0Q,OACzB1Q,EAAM3F,SAAY2F,EAAM0Q,KAAO,EAAMpmB,KAAKsR,IAAOoE,EAAM3F,SAAW2F,EAAM0Q,KAAQ,EAAI,KAGlF1Q,EAAMxF,SAAWwF,EAAM2Q,OACzB3Q,EAAMxF,SAAYwF,EAAM2Q,KAAO,EAAMrmB,KAAKsR,IAAOoE,EAAM2Q,KAAO3Q,EAAMxF,SAAY,EAAI,KAElFwF,EAAMxF,SAAWwF,EAAM4Q,OACzB5Q,EAAMxF,SAAYwF,EAAM4Q,KAAO,EAAMtmB,KAAKsR,IAAOoE,EAAMxF,SAAWwF,EAAM4Q,KAAQ,EAAI,KAIjF3T,EAAS6T,gBAAiB7T,EAAS6T,cAAgB9Q,EAAM6Q,eAAe/f,GACxEmM,EAAS8T,gBAAiB9T,EAAS8T,cAAgB/Q,EAAM6Q,eAAe9f,GACxEkM,EAAS+T,WAAY/T,EAAS+T,SAAW96B,KAAK6K,OACnDkc,EAASnM,GAAKkP,EAAM6Q,eAAe/f,EAAImM,EAAS6T,gBAAkB56B,KAAK6K,MAAQkc,EAAS+T,UAAY,EACpG/T,EAASlM,GAAKiP,EAAM6Q,eAAe9f,EAAIkM,EAAS8T,gBAAkB76B,KAAK6K,MAAQkc,EAAS+T,UAAY,EAChG1mB,KAAK8B,IAAI4T,EAAM6Q,eAAe/f,EAAImM,EAAS6T,eAAiB,IAAK7T,EAASnM,EAAI,GAC9ExG,KAAK8B,IAAI4T,EAAM6Q,eAAe9f,EAAIkM,EAAS8T,eAAiB,IAAK9T,EAASlM,EAAI,GAClFkM,EAAS6T,cAAgB9Q,EAAM6Q,eAAe/f,EAC9CmM,EAAS8T,cAAgB/Q,EAAM6Q,eAAe9f,EAC9CkM,EAAS+T,SAAW96B,KAAK6K,MAEzBwuB,EAAQK,aAAap2B,UAAW,eAAkBwmB,EAAc,SAAI,OAAUA,EAAc,SAAI,YAElGzD,WAAY,WACV,IACI+S,EADSn7B,KACKm7B,KACdC,EAAUD,EAAKC,QACfvP,EAAQsP,EAAKtP,MACb/C,EAAWqS,EAAKrS,SACpB,GAAKsS,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASl5B,OAA1C,CACA,IAAKupB,EAAM9F,YAAc8F,EAAM7F,QAG7B,OAFA6F,EAAM9F,WAAY,OAClB8F,EAAM7F,SAAU,GAGlB6F,EAAM9F,WAAY,EAClB8F,EAAM7F,SAAU,EAChB,IAAI8W,EAAoB,IACpBC,EAAoB,IACpBC,EAAoBlU,EAASnM,EAAImgB,EACjCG,EAAepR,EAAM3F,SAAW8W,EAChCE,EAAoBpU,EAASlM,EAAImgB,EACjCI,EAAetR,EAAMxF,SAAW6W,EAGjB,IAAfpU,EAASnM,IAAWmgB,EAAoB3mB,KAAK8B,KAAKglB,EAAepR,EAAM3F,UAAY4C,EAASnM,IAC7E,IAAfmM,EAASlM,IAAWmgB,EAAoB5mB,KAAK8B,KAAKklB,EAAetR,EAAMxF,UAAYyC,EAASlM,IAChG,IAAImM,EAAmB5S,KAAKK,IAAIsmB,EAAmBC,GAEnDlR,EAAM3F,SAAW+W,EACjBpR,EAAMxF,SAAW8W,EAGjB,IAAIf,EAAcvQ,EAAMpY,MAAQ0nB,EAAK9D,MACjCgF,EAAexQ,EAAMnY,OAASynB,EAAK9D,MACvCxL,EAAMyQ,KAAOnmB,KAAKoM,IAAM6Y,EAAQc,WAAa,EAAME,EAAc,EAAK,GACtEvQ,EAAM0Q,MAAQ1Q,EAAMyQ,KACpBzQ,EAAM2Q,KAAOrmB,KAAKoM,IAAM6Y,EAAQe,YAAc,EAAME,EAAe,EAAK,GACxExQ,EAAM4Q,MAAQ5Q,EAAM2Q,KACpB3Q,EAAM3F,SAAW/P,KAAKK,IAAIL,KAAKoM,IAAIsJ,EAAM3F,SAAU2F,EAAM0Q,MAAO1Q,EAAMyQ,MACtEzQ,EAAMxF,SAAWlQ,KAAKK,IAAIL,KAAKoM,IAAIsJ,EAAMxF,SAAUwF,EAAM4Q,MAAO5Q,EAAM2Q,MAEtEpB,EAAQK,aAAaj2B,WAAWujB,GAAkB1jB,UAAW,eAAkBwmB,EAAc,SAAI,OAAUA,EAAc,SAAI,WAE/HuR,gBAAiB,WACf,IACIjC,EADSn7B,KACKm7B,KACdC,EAAUD,EAAKC,QACfA,EAAQnK,UAHCjxB,KAGkB8b,gBAHlB9b,KAG2CyZ,cACtD2hB,EAAQI,SAASn2B,UAAU,+BAC3B+1B,EAAQK,aAAap2B,UAAU,sBAE/B81B,EAAK9D,MAAQ,EACb8D,EAAKZ,aAAe,EAEpBa,EAAQnK,cAAWlqB,EACnBq0B,EAAQI,cAAWz0B,EACnBq0B,EAAQK,kBAAe10B,IAI3BzC,OAAQ,SAAgB+B,GACtB,IACI80B,EADSn7B,KACKm7B,KAEdA,EAAK9D,OAAwB,IAAf8D,EAAK9D,MAErB8D,EAAKkC,MAGLlC,EAAKmC,GAAGj3B,IAGZi3B,GAAI,SAAcj3B,GAChB,IAgBIk3B,EACAC,EAGAlW,EACAC,EACAkW,EACAC,EACAC,EACAC,EACAxB,EACAC,EACAwB,EACAC,EACAC,EACAC,EACA9B,EACAC,EAjCAxoB,EAAS3T,KAETm7B,EAAOxnB,EAAOwnB,KACdltB,EAAS0F,EAAO1F,OAAOktB,KACvBC,EAAUD,EAAKC,QACfvP,EAAQsP,EAAKtP,OAEZuP,EAAQnK,WACXmK,EAAQnK,SAAWtd,EAAOyI,aAAe7Z,EAAEoR,EAAOyI,cAAgBzI,EAAOkB,OAAOtK,GAAGoJ,EAAO8F,aAC1F2hB,EAAQI,SAAWJ,EAAQnK,SAAStlB,KAAK,oBACzCyvB,EAAQK,aAAeL,EAAQI,SAAShwB,OAAQ,IAAOyC,EAAqB,iBAEzEmtB,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASl5B,UAE1C84B,EAAQnK,SAASttB,SAAU,GAAMsK,EAAuB,uBAqBpB,IAAzB4d,EAAMoQ,aAAatf,GAAqBtW,GACjDk3B,EAAoB,aAAXl3B,EAAEuf,KAAsBvf,EAAE21B,eAAe,GAAG5V,MAAQ/f,EAAE+f,MAC/DoX,EAAoB,aAAXn3B,EAAEuf,KAAsBvf,EAAE21B,eAAe,GAAG1V,MAAQjgB,EAAEigB,QAE/DiX,EAAS1R,EAAMoQ,aAAatf,EAC5B6gB,EAAS3R,EAAMoQ,aAAarf,GAG9Bue,EAAK9D,MAAQ+D,EAAQK,aAAal3B,KAAK,qBAAuB0J,EAAOytB,SACrEP,EAAKZ,aAAea,EAAQK,aAAal3B,KAAK,qBAAuB0J,EAAOytB,SACxEr1B,GACF61B,EAAad,EAAQnK,SAAS,GAAGtoB,YACjCwzB,EAAcf,EAAQnK,SAAS,GAAGnoB,aAGlCwe,EAFU8T,EAAQnK,SAASloB,SAASU,KAEhByyB,EAAa,EAAMqB,EACvChW,EAFU6T,EAAQnK,SAASloB,SAASS,IAEhB2yB,EAAc,EAAMqB,EAExCG,EAAavC,EAAQI,SAAS,GAAG7yB,YACjCi1B,EAAcxC,EAAQI,SAAS,GAAG1yB,aAClCszB,EAAcuB,EAAaxC,EAAK9D,MAChCgF,EAAeuB,EAAczC,EAAK9D,MAIlC0G,IAFAF,EAAgB1nB,KAAKoM,IAAM2Z,EAAa,EAAME,EAAc,EAAK,IAGjE4B,IAFAF,EAAgB3nB,KAAKoM,IAAM4Z,EAAc,EAAME,EAAe,EAAK,KAInEoB,EAAanW,EAAQ6T,EAAK9D,OAGTwG,IACfJ,EAAaI,GAEEE,EAAbN,IACFA,EAAaM,IANfL,EAAanW,EAAQ4T,EAAK9D,OASTyG,IACfJ,EAAaI,GAEEE,EAAbN,IACFA,EAAaM,IAIfN,EADAD,EAAa,EAGfrC,EAAQK,aAAaj2B,WAAW,KAAKH,UAAW,eAAiBo4B,EAAa,OAASC,EAAa,SACpGtC,EAAQI,SAASh2B,WAAW,KAAKH,UAAW,4BAA+B81B,EAAU,MAAI,OAE3FkC,IAAK,WACH,IAAI1pB,EAAS3T,KAETm7B,EAAOxnB,EAAOwnB,KACdltB,EAAS0F,EAAO1F,OAAOktB,KACvBC,EAAUD,EAAKC,QAEdA,EAAQnK,WACXmK,EAAQnK,SAAWtd,EAAOyI,aAAe7Z,EAAEoR,EAAOyI,cAAgBzI,EAAOkB,OAAOtK,GAAGoJ,EAAO8F,aAC1F2hB,EAAQI,SAAWJ,EAAQnK,SAAStlB,KAAK,oBACzCyvB,EAAQK,aAAeL,EAAQI,SAAShwB,OAAQ,IAAOyC,EAAqB,iBAEzEmtB,EAAQI,UAAwC,IAA5BJ,EAAQI,SAASl5B,SAE1C64B,EAAK9D,MAAQ,EACb8D,EAAKZ,aAAe,EACpBa,EAAQK,aAAaj2B,WAAW,KAAKH,UAAU,sBAC/C+1B,EAAQI,SAASh2B,WAAW,KAAKH,UAAU,+BAC3C+1B,EAAQnK,SAAShtB,YAAa,GAAMgK,EAAuB,kBAC3DmtB,EAAQnK,cAAWlqB,IAGrB2rB,OAAQ,WACN,IAAI/e,EAAS3T,KACTm7B,EAAOxnB,EAAOwnB,KAClB,IAAIA,EAAKxmB,QAAT,CACAwmB,EAAKxmB,SAAU,EAEf,IAAIxE,IAA+C,eAA7BwD,EAAO0R,YAAYsE,QAA0Bta,GAAQc,kBAAmBwD,EAAO1F,OAAO4W,mBAAmB,CAAE+E,SAAS,EAAMzjB,SAAS,GAGrJkJ,GAAQmB,UACVmD,EAAOS,WAAWxO,GAAG,eAAgB,gBAAiBu1B,EAAKD,eAAgB/qB,GAC3EwD,EAAOS,WAAWxO,GAAG,gBAAiB,gBAAiBu1B,EAAKS,gBAAiBzrB,GAC7EwD,EAAOS,WAAWxO,GAAG,aAAc,gBAAiBu1B,EAAKY,aAAc5rB,IACjC,eAA7BwD,EAAO0R,YAAYsE,QAC5BhW,EAAOS,WAAWxO,GAAG+N,EAAO0R,YAAYsE,MAAO,gBAAiBwR,EAAKD,eAAgB/qB,GACrFwD,EAAOS,WAAWxO,GAAG+N,EAAO0R,YAAYwE,KAAM,gBAAiBsR,EAAKS,gBAAiBzrB,GACrFwD,EAAOS,WAAWxO,GAAG+N,EAAO0R,YAAYyE,IAAK,gBAAiBqR,EAAKY,aAAc5rB,IAInFwD,EAAOS,WAAWxO,GAAG+N,EAAO0R,YAAYwE,KAAO,IAAOlW,EAAO1F,OAAOktB,KAAmB,eAAIA,EAAK/T,eAElGuL,QAAS,WACP,IAAIhf,EAAS3T,KACTm7B,EAAOxnB,EAAOwnB,KAClB,GAAKA,EAAKxmB,QAAV,CAEAhB,EAAOwnB,KAAKxmB,SAAU,EAEtB,IAAIxE,IAA+C,eAA7BwD,EAAO0R,YAAYsE,QAA0Bta,GAAQc,kBAAmBwD,EAAO1F,OAAO4W,mBAAmB,CAAE+E,SAAS,EAAMzjB,SAAS,GAGrJkJ,GAAQmB,UACVmD,EAAOS,WAAW9M,IAAI,eAAgB,gBAAiB6zB,EAAKD,eAAgB/qB,GAC5EwD,EAAOS,WAAW9M,IAAI,gBAAiB,gBAAiB6zB,EAAKS,gBAAiBzrB,GAC9EwD,EAAOS,WAAW9M,IAAI,aAAc,gBAAiB6zB,EAAKY,aAAc5rB,IAClC,eAA7BwD,EAAO0R,YAAYsE,QAC5BhW,EAAOS,WAAW9M,IAAIqM,EAAO0R,YAAYsE,MAAO,gBAAiBwR,EAAKD,eAAgB/qB,GACtFwD,EAAOS,WAAW9M,IAAIqM,EAAO0R,YAAYwE,KAAM,gBAAiBsR,EAAKS,gBAAiBzrB,GACtFwD,EAAOS,WAAW9M,IAAIqM,EAAO0R,YAAYyE,IAAK,gBAAiBqR,EAAKY,aAAc5rB,IAIpFwD,EAAOS,WAAW9M,IAAIqM,EAAO0R,YAAYwE,KAAO,IAAOlW,EAAO1F,OAAOktB,KAAmB,eAAIA,EAAK/T,gBAkHjG6W,EAAO,CACTC,YAAa,SAAqB9zB,EAAO+zB,QACd,IAApBA,IAA6BA,GAAkB,GAEpD,IAAIxqB,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAAOyiB,KAC3B,QAAqB,IAAVtmB,GACkB,IAAzBuJ,EAAOkB,OAAOvS,OAAlB,CACA,IAEI2uB,EAFYtd,EAAOe,SAAWf,EAAO1F,OAAOyG,QAAQC,QAGpDhB,EAAOS,WAAWrT,SAAU,IAAO4S,EAAO1F,OAAiB,WAAI,6BAAgC7D,EAAQ,MACvGuJ,EAAOkB,OAAOtK,GAAGH,GAEjBg0B,EAAUnN,EAAStlB,KAAM,IAAOsC,EAAmB,aAAI,SAAYA,EAAkB,YAAI,UAAaA,EAAmB,aAAI,MAC7HgjB,EAAS9sB,SAAS8J,EAAOowB,eAAkBpN,EAAS9sB,SAAS8J,EAAOqwB,cAAiBrN,EAAS9sB,SAAS8J,EAAOswB,gBAChHH,EAAUA,EAAQp6B,IAAIitB,EAAS,KAEV,IAAnBmN,EAAQ97B,QAEZ87B,EAAQv0B,KAAK,SAAU20B,EAAYhT,GACjC,IAAIgQ,EAAWj5B,EAAEipB,GACjBgQ,EAAS73B,SAASsK,EAAOswB,cAEzB,IAAIE,EAAajD,EAASj3B,KAAK,mBAC3BknB,EAAM+P,EAASj3B,KAAK,YACpBmnB,EAAS8P,EAASj3B,KAAK,eACvBonB,EAAQ6P,EAASj3B,KAAK,cAE1BoP,EAAO4X,UAAUiQ,EAAS,GAAK/P,GAAOgT,EAAa/S,EAAQC,GAAO,EAAO,WACvE,GAAI,MAAOhY,GAA8CA,KAAWA,GAAWA,EAAO1F,UAAW0F,EAAOmK,UAAxG,CAqBA,GApBI2gB,GACFjD,EAAS9xB,IAAI,mBAAqB,QAAW+0B,EAAa,MAC1DjD,EAAS12B,WAAW,qBAEhB4mB,IACF8P,EAASj3B,KAAK,SAAUmnB,GACxB8P,EAAS12B,WAAW,gBAElB6mB,IACF6P,EAASj3B,KAAK,QAASonB,GACvB6P,EAAS12B,WAAW,eAElB2mB,IACF+P,EAASj3B,KAAK,MAAOknB,GACrB+P,EAAS12B,WAAW,cAIxB02B,EAAS73B,SAASsK,EAAOqwB,aAAar6B,YAAYgK,EAAOswB,cACzDtN,EAAStlB,KAAM,IAAOsC,EAAqB,gBAAI/J,SAC3CyP,EAAO1F,OAAOkN,MAAQgjB,EAAiB,CACzC,IAAIO,EAAqBzN,EAAS1sB,KAAK,2BACvC,GAAI0sB,EAAS9sB,SAASwP,EAAO1F,OAAOmN,qBAAsB,CACxD,IAAIujB,EAAgBhrB,EAAOS,WAAWrT,SAAU,6BAAgC29B,EAAqB,WAAe/qB,EAAO1F,OAA0B,oBAAI,KACzJ0F,EAAO+c,KAAKwN,YAAYS,EAAcv0B,SAAS,OAC1C,CACL,IAAIw0B,EAAkBjrB,EAAOS,WAAWrT,SAAU,IAAO4S,EAAO1F,OAA0B,oBAAI,6BAAgCywB,EAAqB,MACnJ/qB,EAAO+c,KAAKwN,YAAYU,EAAgBx0B,SAAS,IAGrDuJ,EAAO/B,KAAK,iBAAkBqf,EAAS,GAAIuK,EAAS,OAGtD7nB,EAAO/B,KAAK,gBAAiBqf,EAAS,GAAIuK,EAAS,QAGvD7K,KAAM,WACJ,IAAIhd,EAAS3T,KACToU,EAAaT,EAAOS,WACpBqY,EAAe9Y,EAAO1F,OACtB4G,EAASlB,EAAOkB,OAChB4E,EAAc9F,EAAO8F,YACrBhF,EAAYd,EAAOe,SAAW+X,EAAa/X,QAAQC,QACnD1G,EAASwe,EAAaiE,KAEtBpa,EAAgBmW,EAAanW,cAKjC,SAASuoB,EAAWz0B,GAClB,GAAIqK,GACF,GAAIL,EAAWrT,SAAU,IAAO0rB,EAAuB,WAAI,6BAAgCriB,EAAQ,MAAQ9H,OACzG,OAAO,OAEJ,GAAIuS,EAAOzK,GAAU,OAAO,EACnC,OAAO,EAET,SAASmT,EAAWgQ,GAClB,OAAI9Y,EACKlS,EAAEgrB,GAAShpB,KAAK,2BAElBhC,EAAEgrB,GAASnjB,QAIpB,GApBsB,SAAlBkM,IACFA,EAAgB,GAkBb3C,EAAO+c,KAAKoO,qBAAsBnrB,EAAO+c,KAAKoO,oBAAqB,GACpEnrB,EAAO1F,OAAOiL,sBAChB9E,EAAWrT,SAAU,IAAO0rB,EAA8B,mBAAI5iB,KAAK,SAAUk1B,EAASxR,GACpF,IAAInjB,EAAQqK,EAAYlS,EAAEgrB,GAAShpB,KAAK,2BAA6BhC,EAAEgrB,GAASnjB,QAChFuJ,EAAO+c,KAAKwN,YAAY9zB,UAErB,GAAoB,EAAhBkM,EACT,IAAK,IAAIjU,EAAIoX,EAAapX,EAAIoX,EAAcnD,EAAejU,GAAK,EAC1Dw8B,EAAWx8B,IAAMsR,EAAO+c,KAAKwN,YAAY77B,QAG/CsR,EAAO+c,KAAKwN,YAAYzkB,GAE1B,GAAIxL,EAAO+wB,aACT,GAAoB,EAAhB1oB,GAAsBrI,EAAOgxB,oBAAkD,EAA5BhxB,EAAOgxB,mBAAyB,CAMrF,IALA,IAAIC,EAASjxB,EAAOgxB,mBAChBhS,EAAM3W,EACN6oB,EAAWhpB,KAAKoM,IAAI9I,EAAcwT,EAAM9W,KAAKK,IAAI0oB,EAAQjS,GAAMpY,EAAOvS,QACtE88B,EAAWjpB,KAAKK,IAAIiD,EAActD,KAAKK,IAAIyW,EAAKiS,GAAS,GAEpD7mB,EAAMoB,EAAcnD,EAAe+B,EAAM8mB,EAAU9mB,GAAO,EAC7DwmB,EAAWxmB,IAAQ1E,EAAO+c,KAAKwN,YAAY7lB,GAGjD,IAAK,IAAIE,EAAM6mB,EAAU7mB,EAAMkB,EAAalB,GAAO,EAC7CsmB,EAAWtmB,IAAQ5E,EAAO+c,KAAKwN,YAAY3lB,OAE5C,CACL,IAAI+C,EAAYlH,EAAWrT,SAAU,IAAO0rB,EAA2B,gBAChD,EAAnBnR,EAAUhZ,QAAcqR,EAAO+c,KAAKwN,YAAY3gB,EAAWjC,IAE/D,IAAIE,EAAYpH,EAAWrT,SAAU,IAAO0rB,EAA2B,gBAChD,EAAnBjR,EAAUlZ,QAAcqR,EAAO+c,KAAKwN,YAAY3gB,EAAW/B,OAiFnE6jB,EAAa,CACfC,aAAc,SAAsB3iB,EAAGC,GACrC,IACMuiB,EACAC,EACAG,EAqBFC,EACAC,EAzBAC,EAIK,SAAUC,EAAOphB,GAGtB,IAFA6gB,GAAY,EACZD,EAAWQ,EAAMr9B,OACY,EAAtB68B,EAAWC,GAEZO,EADJJ,EAAQJ,EAAWC,GAAY,IACX7gB,EAClB6gB,EAAWG,EAEXJ,EAAWI,EAGf,OAAOJ,GAuBX,OApBAn/B,KAAK2c,EAAIA,EACT3c,KAAK4c,EAAIA,EACT5c,KAAKm2B,UAAYxZ,EAAEra,OAAS,EAO5BtC,KAAK4/B,YAAc,SAAqB5E,GACtC,OAAKA,GAGLyE,EAAKC,EAAa1/B,KAAK2c,EAAGqe,GAC1BwE,EAAKC,EAAK,GAIAzE,EAAKh7B,KAAK2c,EAAE6iB,KAAQx/B,KAAK4c,EAAE6iB,GAAMz/B,KAAK4c,EAAE4iB,KAASx/B,KAAK2c,EAAE8iB,GAAMz/B,KAAK2c,EAAE6iB,IAAQx/B,KAAK4c,EAAE4iB,IAR5E,GAUbx/B,MAGT6/B,uBAAwB,SAAgCC,GACtD,IAAInsB,EAAS3T,KACR2T,EAAOosB,WAAWC,SACrBrsB,EAAOosB,WAAWC,OAASrsB,EAAO1F,OAAOkN,KACrC,IAAIkkB,EAAWC,aAAa3rB,EAAOqB,WAAY8qB,EAAE9qB,YACjD,IAAIqqB,EAAWC,aAAa3rB,EAAOoB,SAAU+qB,EAAE/qB,YAGvD0H,aAAc,SAAsBwjB,EAAgBvjB,GAClD,IAEIwjB,EACAC,EAHAxsB,EAAS3T,KACTogC,EAAazsB,EAAOosB,WAAWM,QAGnC,SAASC,EAAuBR,GAK9B,IAAIhmB,EAAYnG,EAAOY,cAAgBZ,EAAOmG,UAAYnG,EAAOmG,UAC7B,UAAhCnG,EAAO1F,OAAO8xB,WAAWQ,KAC3B5sB,EAAOosB,WAAWF,uBAAuBC,GAGzCK,GAAuBxsB,EAAOosB,WAAWC,OAAOJ,aAAa9lB,IAG1DqmB,GAAuD,cAAhCxsB,EAAO1F,OAAO8xB,WAAWQ,KACnDL,GAAcJ,EAAEplB,eAAiBolB,EAAE1lB,iBAAmBzG,EAAO+G,eAAiB/G,EAAOyG,gBACrF+lB,GAAwBrmB,EAAYnG,EAAOyG,gBAAkB8lB,EAAcJ,EAAE1lB,gBAG3EzG,EAAO1F,OAAO8xB,WAAWS,UAC3BL,EAAsBL,EAAEplB,eAAiBylB,GAE3CL,EAAEtlB,eAAe2lB,GACjBL,EAAErjB,aAAa0jB,EAAqBxsB,GACpCmsB,EAAElkB,oBACFkkB,EAAE/kB,sBAEJ,GAAIlJ,MAAMC,QAAQsuB,GAChB,IAAK,IAAI/9B,EAAI,EAAGA,EAAI+9B,EAAW99B,OAAQD,GAAK,EACtC+9B,EAAW/9B,KAAOqa,GAAgB0jB,EAAW/9B,aAActC,GAC7DugC,EAAuBF,EAAW/9B,SAG7B+9B,aAAsBrgC,GAAU2c,IAAiB0jB,GAC1DE,EAAuBF,IAG3B5mB,cAAe,SAAuB/T,EAAUiX,GAC9C,IAEIra,EAFAsR,EAAS3T,KACTogC,EAAazsB,EAAOosB,WAAWM,QAEnC,SAASI,EAAwBX,GAC/BA,EAAEtmB,cAAc/T,EAAUkO,GACT,IAAblO,IACFq6B,EAAE/iB,kBACE+iB,EAAE7xB,OAAOiP,YACX5Q,GAAMI,SAAS,WACbozB,EAAE1mB,qBAGN0mB,EAAE1rB,WAAWjM,cAAc,WACpBi4B,IACDN,EAAE7xB,OAAOkN,MAAwC,UAAhCxH,EAAO1F,OAAO8xB,WAAWQ,IAC5CT,EAAE3hB,UAEJ2hB,EAAE33B,oBAIR,GAAI0J,MAAMC,QAAQsuB,GAChB,IAAK/9B,EAAI,EAAGA,EAAI+9B,EAAW99B,OAAQD,GAAK,EAClC+9B,EAAW/9B,KAAOqa,GAAgB0jB,EAAW/9B,aAActC,GAC7D0gC,EAAwBL,EAAW/9B,SAG9B+9B,aAAsBrgC,GAAU2c,IAAiB0jB,GAC1DK,EAAwBL,KA8D1BM,EAAO,CACTC,gBAAiB,SAAyB/sB,GAExC,OADAA,EAAIrP,KAAK,WAAY,KACdqP,GAETgtB,UAAW,SAAmBhtB,EAAKitB,GAEjC,OADAjtB,EAAIrP,KAAK,OAAQs8B,GACVjtB,GAETktB,WAAY,SAAoBltB,EAAKmtB,GAEnC,OADAntB,EAAIrP,KAAK,aAAcw8B,GAChBntB,GAETotB,UAAW,SAAmBptB,GAE5B,OADAA,EAAIrP,KAAK,iBAAiB,GACnBqP,GAETqtB,SAAU,SAAkBrtB,GAE1B,OADAA,EAAIrP,KAAK,iBAAiB,GACnBqP,GAETstB,WAAY,SAAoB76B,GAC9B,IAAIsN,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAAOyyB,KAC3B,GAAkB,KAAdr6B,EAAEsrB,QAAN,CACA,IAAIwP,EAAY5+B,EAAE8D,EAAEC,QAChBqN,EAAOqX,YAAcrX,EAAOqX,WAAWoK,SAAW+L,EAAUz6B,GAAGiN,EAAOqX,WAAWoK,WAC7EzhB,EAAOiH,QAAUjH,EAAO1F,OAAOkN,MACnCxH,EAAOuK,YAELvK,EAAOiH,MACTjH,EAAO+sB,KAAKU,OAAOnzB,EAAOozB,kBAE1B1tB,EAAO+sB,KAAKU,OAAOnzB,EAAOqzB,mBAG1B3tB,EAAOqX,YAAcrX,EAAOqX,WAAWqK,SAAW8L,EAAUz6B,GAAGiN,EAAOqX,WAAWqK,WAC7E1hB,EAAOgH,cAAgBhH,EAAO1F,OAAOkN,MACzCxH,EAAO0K,YAEL1K,EAAOgH,YACThH,EAAO+sB,KAAKU,OAAOnzB,EAAOszB,mBAE1B5tB,EAAO+sB,KAAKU,OAAOnzB,EAAOuzB,mBAG1B7tB,EAAOkiB,YAAcsL,EAAUz6B,GAAI,IAAOiN,EAAO1F,OAAO4nB,WAAsB,cAChFsL,EAAU,GAAGM,UAGjBL,OAAQ,SAAgBM,GACtB,IACIC,EADS3hC,KACa0gC,KAAKkB,WACH,IAAxBD,EAAar/B,SACjBq/B,EAAa/+B,KAAK,IAClB++B,EAAa/+B,KAAK8+B,KAEpBG,iBAAkB,WAChB,IAAIluB,EAAS3T,KAEb,IAAI2T,EAAO1F,OAAOkN,KAAlB,CACA,IAAIwU,EAAMhc,EAAOqX,WACboK,EAAUzF,EAAIyF,QACdC,EAAU1F,EAAI0F,QAEdA,GAA4B,EAAjBA,EAAQ/yB,SACjBqR,EAAOgH,YACThH,EAAO+sB,KAAKM,UAAU3L,GAEtB1hB,EAAO+sB,KAAKO,SAAS5L,IAGrBD,GAA4B,EAAjBA,EAAQ9yB,SACjBqR,EAAOiH,MACTjH,EAAO+sB,KAAKM,UAAU5L,GAEtBzhB,EAAO+sB,KAAKO,SAAS7L,MAI3B0M,iBAAkB,WAChB,IAAInuB,EAAS3T,KACTiO,EAAS0F,EAAO1F,OAAOyyB,KACvB/sB,EAAOkiB,YAAcliB,EAAO1F,OAAO4nB,WAAWsC,WAAaxkB,EAAOkiB,WAAWI,SAAWtiB,EAAOkiB,WAAWI,QAAQ3zB,QACpHqR,EAAOkiB,WAAWI,QAAQpsB,KAAK,SAAU8sB,EAAaoL,GACpD,IAAIC,EAAYz/B,EAAEw/B,GAClBpuB,EAAO+sB,KAAKC,gBAAgBqB,GAC5BruB,EAAO+sB,KAAKE,UAAUoB,EAAW,UACjCruB,EAAO+sB,KAAKI,WAAWkB,EAAW/zB,EAAOg0B,wBAAwB30B,QAAQ,YAAa00B,EAAU53B,QAAU,OAIhHqY,KAAM,WACJ,IAAI9O,EAAS3T,KAEb2T,EAAOC,IAAInJ,OAAOkJ,EAAO+sB,KAAKkB,YAG9B,IACIxM,EACAC,EAFApnB,EAAS0F,EAAO1F,OAAOyyB,KAGvB/sB,EAAOqX,YAAcrX,EAAOqX,WAAWoK,UACzCA,EAAUzhB,EAAOqX,WAAWoK,SAE1BzhB,EAAOqX,YAAcrX,EAAOqX,WAAWqK,UACzCA,EAAU1hB,EAAOqX,WAAWqK,SAE1BD,IACFzhB,EAAO+sB,KAAKC,gBAAgBvL,GAC5BzhB,EAAO+sB,KAAKE,UAAUxL,EAAS,UAC/BzhB,EAAO+sB,KAAKI,WAAW1L,EAASnnB,EAAOqzB,kBACvClM,EAAQxvB,GAAG,UAAW+N,EAAO+sB,KAAKQ,aAEhC7L,IACF1hB,EAAO+sB,KAAKC,gBAAgBtL,GAC5B1hB,EAAO+sB,KAAKE,UAAUvL,EAAS,UAC/B1hB,EAAO+sB,KAAKI,WAAWzL,EAASpnB,EAAOuzB,kBACvCnM,EAAQzvB,GAAG,UAAW+N,EAAO+sB,KAAKQ,aAIhCvtB,EAAOkiB,YAAcliB,EAAO1F,OAAO4nB,WAAWsC,WAAaxkB,EAAOkiB,WAAWI,SAAWtiB,EAAOkiB,WAAWI,QAAQ3zB,QACpHqR,EAAOkiB,WAAWjiB,IAAIhO,GAAG,UAAY,IAAO+N,EAAO1F,OAAO4nB,WAAsB,YAAIliB,EAAO+sB,KAAKQ,aAGpG1T,QAAS,WACP,IAGI4H,EACAC,EAJA1hB,EAAS3T,KACT2T,EAAO+sB,KAAKkB,YAA8C,EAAhCjuB,EAAO+sB,KAAKkB,WAAWt/B,QAAcqR,EAAO+sB,KAAKkB,WAAW19B,SAItFyP,EAAOqX,YAAcrX,EAAOqX,WAAWoK,UACzCA,EAAUzhB,EAAOqX,WAAWoK,SAE1BzhB,EAAOqX,YAAcrX,EAAOqX,WAAWqK,UACzCA,EAAU1hB,EAAOqX,WAAWqK,SAE1BD,GACFA,EAAQ9tB,IAAI,UAAWqM,EAAO+sB,KAAKQ,YAEjC7L,GACFA,EAAQ/tB,IAAI,UAAWqM,EAAO+sB,KAAKQ,YAIjCvtB,EAAOkiB,YAAcliB,EAAO1F,OAAO4nB,WAAWsC,WAAaxkB,EAAOkiB,WAAWI,SAAWtiB,EAAOkiB,WAAWI,QAAQ3zB,QACpHqR,EAAOkiB,WAAWjiB,IAAItM,IAAI,UAAY,IAAOqM,EAAO1F,OAAO4nB,WAAsB,YAAIliB,EAAO+sB,KAAKQ,cA0DnGgB,EAAU,CACZzf,KAAM,WACJ,IAAI9O,EAAS3T,KACb,GAAK2T,EAAO1F,OAAOvM,QAAnB,CACA,IAAKJ,EAAII,UAAYJ,EAAII,QAAQygC,UAG/B,OAFAxuB,EAAO1F,OAAOvM,QAAQiT,SAAU,OAChChB,EAAO1F,OAAOm0B,eAAeztB,SAAU,GAGzC,IAAIjT,EAAUiS,EAAOjS,QACrBA,EAAQgc,aAAc,EACtBhc,EAAQ2gC,MAAQH,EAAQI,iBACnB5gC,EAAQ2gC,MAAMp9B,KAAQvD,EAAQ2gC,MAAM59B,SACzC/C,EAAQ6gC,cAAc,EAAG7gC,EAAQ2gC,MAAM59B,MAAOkP,EAAO1F,OAAOiX,oBACvDvR,EAAO1F,OAAOvM,QAAQ8gC,cACzBlhC,EAAIlB,iBAAiB,WAAYuT,EAAOjS,QAAQ+gC,uBAGpDjV,QAAS,WACMxtB,KACDiO,OAAOvM,QAAQ8gC,cACzBlhC,EAAIjB,oBAAoB,WAFbL,KAEgC0B,QAAQ+gC,qBAGvDA,mBAAoB,WACLziC,KACN0B,QAAQ2gC,MAAQH,EAAQI,gBADlBtiC,KAEN0B,QAAQ6gC,cAFFviC,KAEuBiO,OAAOoL,MAF9BrZ,KAE4C0B,QAAQ2gC,MAAM59B,OAAO,IAEhF69B,cAAe,WACb,IAAII,EAAYphC,EAAIF,SAASuhC,SAAS5wB,MAAM,GAAG5O,MAAM,KAAK6E,OAAO,SAAU46B,GAAQ,MAAgB,KAATA,IACtF7M,EAAQ2M,EAAUpgC,OAGtB,MAAO,CAAE2C,IAFCy9B,EAAU3M,EAAQ,GAETtxB,MADPi+B,EAAU3M,EAAQ,KAGhC8M,WAAY,SAAoB59B,EAAKmF,GAEnC,GADapK,KACD0B,QAAQgc,aADP1d,KAC8BiO,OAAOvM,QAAQiT,QAA1D,CACA,IAAIiC,EAFS5W,KAEM6U,OAAOtK,GAAGH,GACzB3F,EAAQy9B,EAAQY,QAAQlsB,EAAMrS,KAAK,iBAClCjD,EAAIF,SAASuhC,SAASI,SAAS99B,KAClCR,EAAQQ,EAAM,IAAMR,GAEtB,IAAIu+B,EAAe1hC,EAAII,QAAQuhC,MAC3BD,GAAgBA,EAAav+B,QAAUA,IAR9BzE,KAWFiO,OAAOvM,QAAQ8gC,aACxBlhC,EAAII,QAAQ8gC,aAAa,CAAE/9B,MAAOA,GAAS,KAAMA,GAEjDnD,EAAII,QAAQygC,UAAU,CAAE19B,MAAOA,GAAS,KAAMA,MAGlDq+B,QAAS,SAAiBh5B,GACxB,OAAOA,EAAK8D,WACTN,QAAQ,OAAQ,KAChBA,QAAQ,WAAY,IACpBA,QAAQ,OAAQ,KAChBA,QAAQ,MAAO,IACfA,QAAQ,MAAO,KAEpBi1B,cAAe,SAAuBlpB,EAAO5U,EAAOuY,GAClD,IAAIrJ,EAAS3T,KACb,GAAIyE,EACF,IAAK,IAAIpC,EAAI,EAAGC,EAASqR,EAAOkB,OAAOvS,OAAQD,EAAIC,EAAQD,GAAK,EAAG,CACjE,IAAIuU,EAAQjD,EAAOkB,OAAOtK,GAAGlI,GAE7B,GADmB6/B,EAAQY,QAAQlsB,EAAMrS,KAAK,mBACzBE,IAAUmS,EAAMzS,SAASwP,EAAO1F,OAAOmN,qBAAsB,CAChF,IAAIhR,EAAQwM,EAAMxM,QAClBuJ,EAAO0J,QAAQjT,EAAOiP,EAAO2D,SAIjCrJ,EAAO0J,QAAQ,EAAGhE,EAAO2D,KAgD3BkmB,EAAiB,CACnBC,YAAa,WACX,IAAIxvB,EAAS3T,KACTojC,EAAUnjC,EAAImB,SAASC,KAAKiM,QAAQ,IAAK,IAE7C,GAAI81B,IADkBzvB,EAAOkB,OAAOtK,GAAGoJ,EAAO8F,aAAalV,KAAK,aAC/B,CAC/B,IAAIyZ,EAAWrK,EAAOS,WAAWrT,SAAU,IAAO4S,EAAO1F,OAAiB,WAAI,eAAkBm1B,EAAU,MAAQh5B,QAClH,QAAwB,IAAb4T,EAA4B,OACvCrK,EAAO0J,QAAQW,KAGnBqlB,QAAS,WACP,IAAI1vB,EAAS3T,KACb,GAAK2T,EAAOyuB,eAAe1kB,aAAgB/J,EAAO1F,OAAOm0B,eAAeztB,QACxE,GAAIhB,EAAO1F,OAAOm0B,eAAeI,cAAgBlhC,EAAII,SAAWJ,EAAII,QAAQ8gC,aAC1ElhC,EAAII,QAAQ8gC,aAAa,KAAM,KAAQ,IAAO7uB,EAAOkB,OAAOtK,GAAGoJ,EAAO8F,aAAalV,KAAK,cAAkB,QACrG,CACL,IAAIqS,EAAQjD,EAAOkB,OAAOtK,GAAGoJ,EAAO8F,aAChCpY,EAAOuV,EAAMrS,KAAK,cAAgBqS,EAAMrS,KAAK,gBACjDtE,EAAImB,SAASC,KAAOA,GAAQ,KAGhCohB,KAAM,WACJ,IAAI9O,EAAS3T,KACb,MAAK2T,EAAO1F,OAAOm0B,eAAeztB,SAAYhB,EAAO1F,OAAOvM,SAAWiS,EAAO1F,OAAOvM,QAAQiT,SAA7F,CACAhB,EAAOyuB,eAAe1kB,aAAc,EACpC,IAAIrc,EAAOpB,EAAImB,SAASC,KAAKiM,QAAQ,IAAK,IAC1C,GAAIjM,EAEF,IADA,IACSgB,EAAI,EAAGC,EAASqR,EAAOkB,OAAOvS,OAAQD,EAAIC,EAAQD,GAAK,EAAG,CACjE,IAAIuU,EAAQjD,EAAOkB,OAAOtK,GAAGlI,GAE7B,IADgBuU,EAAMrS,KAAK,cAAgBqS,EAAMrS,KAAK,mBACpClD,IAASuV,EAAMzS,SAASwP,EAAO1F,OAAOmN,qBAAsB,CAC5E,IAAIhR,EAAQwM,EAAMxM,QAClBuJ,EAAO0J,QAAQjT,EANP,EAMqBuJ,EAAO1F,OAAOiX,oBAAoB,IAIjEvR,EAAO1F,OAAOm0B,eAAekB,YAC/B/gC,EAAEjB,GAAKsE,GAAG,aAAc+N,EAAOyuB,eAAee,eAGlD3V,QAAS,WACMxtB,KACFiO,OAAOm0B,eAAekB,YAC/B/gC,EAAEjB,GAAKgG,IAAI,aAFAtH,KAEqBoiC,eAAee,eAiDjDI,EAAW,CACbC,IAAK,WACH,IAAI7vB,EAAS3T,KACTyjC,EAAiB9vB,EAAOkB,OAAOtK,GAAGoJ,EAAO8F,aACzC9M,EAAQgH,EAAO1F,OAAO6mB,SAASnoB,MAC/B82B,EAAel/B,KAAK,0BACtBoI,EAAQ82B,EAAel/B,KAAK,yBAA2BoP,EAAO1F,OAAO6mB,SAASnoB,OAEhFgH,EAAOmhB,SAASD,QAAUvoB,GAAMI,SAAS,WACnCiH,EAAO1F,OAAO6mB,SAAS4O,iBACrB/vB,EAAO1F,OAAOkN,MAChBxH,EAAOwK,UACPxK,EAAO0K,UAAU1K,EAAO1F,OAAOoL,OAAO,GAAM,GAC5C1F,EAAO/B,KAAK,aACF+B,EAAOgH,YAGPhH,EAAO1F,OAAO6mB,SAAS6O,gBAIjChwB,EAAOmhB,SAASE,QAHhBrhB,EAAO0J,QAAQ1J,EAAOkB,OAAOvS,OAAS,EAAGqR,EAAO1F,OAAOoL,OAAO,GAAM,GACpE1F,EAAO/B,KAAK,cAJZ+B,EAAO0K,UAAU1K,EAAO1F,OAAOoL,OAAO,GAAM,GAC5C1F,EAAO/B,KAAK,aAOL+B,EAAO1F,OAAOkN,MACvBxH,EAAOwK,UACPxK,EAAOuK,UAAUvK,EAAO1F,OAAOoL,OAAO,GAAM,GAC5C1F,EAAO/B,KAAK,aACF+B,EAAOiH,MAGPjH,EAAO1F,OAAO6mB,SAAS6O,gBAIjChwB,EAAOmhB,SAASE,QAHhBrhB,EAAO0J,QAAQ,EAAG1J,EAAO1F,OAAOoL,OAAO,GAAM,GAC7C1F,EAAO/B,KAAK,cAJZ+B,EAAOuK,UAAUvK,EAAO1F,OAAOoL,OAAO,GAAM,GAC5C1F,EAAO/B,KAAK,cAObjF,IAELgd,MAAO,WACL,IAAIhW,EAAS3T,KACb,YAAuC,IAA5B2T,EAAOmhB,SAASD,WACvBlhB,EAAOmhB,SAAS8O,UACpBjwB,EAAOmhB,SAAS8O,SAAU,EAC1BjwB,EAAO/B,KAAK,iBACZ+B,EAAOmhB,SAAS0O,OACT,KAETxO,KAAM,WACJ,IAAIrhB,EAAS3T,KACb,QAAK2T,EAAOmhB,SAAS8O,eACkB,IAA5BjwB,EAAOmhB,SAASD,UAEvBlhB,EAAOmhB,SAASD,UAClB3yB,aAAayR,EAAOmhB,SAASD,SAC7BlhB,EAAOmhB,SAASD,aAAU9tB,GAE5B4M,EAAOmhB,SAAS8O,SAAU,EAC1BjwB,EAAO/B,KAAK,iBACL,KAETiyB,MAAO,SAAexqB,GACpB,IAAI1F,EAAS3T,KACR2T,EAAOmhB,SAAS8O,UACjBjwB,EAAOmhB,SAASgP,SAChBnwB,EAAOmhB,SAASD,SAAW3yB,aAAayR,EAAOmhB,SAASD,SAC5DlhB,EAAOmhB,SAASgP,QAAS,EACX,IAAVzqB,GAAgB1F,EAAO1F,OAAO6mB,SAASiP,mBAIzCpwB,EAAOS,WAAW,GAAGhU,iBAAiB,gBAAiBuT,EAAOmhB,SAASsI,iBACvEzpB,EAAOS,WAAW,GAAGhU,iBAAiB,sBAAuBuT,EAAOmhB,SAASsI,mBAJ7EzpB,EAAOmhB,SAASgP,QAAS,EACzBnwB,EAAOmhB,SAAS0O,WAiFlBQ,EAAO,CACTvnB,aAAc,WAGZ,IAFA,IAAI9I,EAAS3T,KACT6U,EAASlB,EAAOkB,OACXxS,EAAI,EAAGA,EAAIwS,EAAOvS,OAAQD,GAAK,EAAG,CACzC,IAAI4uB,EAAWtd,EAAOkB,OAAOtK,GAAGlI,GAE5B4hC,GADShT,EAAS,GAAGvX,kBAEpB/F,EAAO1F,OAAOsO,mBAAoB0nB,GAAMtwB,EAAOmG,WACpD,IAAIoqB,EAAK,EACJvwB,EAAOI,iBACVmwB,EAAKD,EACLA,EAAK,GAEP,IAAIE,EAAexwB,EAAO1F,OAAOm2B,WAAWC,UACxCluB,KAAKK,IAAI,EAAIL,KAAK8B,IAAIgZ,EAAS,GAAG1W,UAAW,GAC7C,EAAIpE,KAAKoM,IAAIpM,KAAKK,IAAIya,EAAS,GAAG1W,UAAW,GAAI,GACrD0W,EACGvnB,IAAI,CACHsvB,QAASmL,IAEV9+B,UAAW,eAAiB4+B,EAAK,OAASC,EAAK,cAGtD1qB,cAAe,SAAuB/T,GACpC,IAAIkO,EAAS3T,KACT6U,EAASlB,EAAOkB,OAChBT,EAAaT,EAAOS,WAExB,GADAS,EAAOrP,WAAWC,GACdkO,EAAO1F,OAAOsO,kBAAiC,IAAb9W,EAAgB,CACpD,IAAI6+B,GAAiB,EACrBzvB,EAAO1M,cAAc,WACnB,IAAIm8B,GACC3wB,IAAUA,EAAOmK,UAAtB,CACAwmB,GAAiB,EACjB3wB,EAAOyJ,WAAY,EAEnB,IADA,IAAImnB,EAAgB,CAAC,sBAAuB,iBACnCliC,EAAI,EAAGA,EAAIkiC,EAAcjiC,OAAQD,GAAK,EAC7C+R,EAAWzM,QAAQ48B,EAAcliC,UAoDvCmiC,EAAO,CACT/nB,aAAc,WACZ,IAYIgoB,EAZA9wB,EAAS3T,KACT4T,EAAMD,EAAOC,IACbQ,EAAaT,EAAOS,WACpBS,EAASlB,EAAOkB,OAChB6vB,EAAc/wB,EAAOF,MACrBkxB,EAAehxB,EAAOD,OACtBY,EAAMX,EAAOY,aACbF,EAAaV,EAAOO,KACpBjG,EAAS0F,EAAO1F,OAAO22B,WACvB7wB,EAAeJ,EAAOI,eACtBU,EAAYd,EAAOe,SAAWf,EAAO1F,OAAOyG,QAAQC,QACpDkwB,EAAgB,EAEhB52B,EAAO62B,SACL/wB,GAE2B,KAD7B0wB,EAAgBrwB,EAAWzI,KAAK,wBACdrJ,SAChBmiC,EAAgBliC,EAAE,0CAClB6R,EAAW3J,OAAOg6B,IAEpBA,EAAc/6B,IAAI,CAAEgK,OAASgxB,EAAc,QAGd,KAD7BD,EAAgB7wB,EAAIjI,KAAK,wBACPrJ,SAChBmiC,EAAgBliC,EAAE,0CAClBqR,EAAInJ,OAAOg6B,KAIjB,IAAK,IAAIpiC,EAAI,EAAGA,EAAIwS,EAAOvS,OAAQD,GAAK,EAAG,CACzC,IAAI4uB,EAAWpc,EAAOtK,GAAGlI,GACrBkb,EAAalb,EACboS,IACF8I,EAAatJ,SAASgd,EAAS1sB,KAAK,2BAA4B,KAElE,IAAIwgC,EAA0B,GAAbxnB,EACbynB,EAAQ7uB,KAAKC,MAAM2uB,EAAa,KAChCzwB,IACFywB,GAAcA,EACdC,EAAQ7uB,KAAKC,OAAO2uB,EAAa,MAEnC,IAAIxqB,EAAWpE,KAAKK,IAAIL,KAAKoM,IAAI0O,EAAS,GAAG1W,SAAU,IAAK,GACxD0pB,EAAK,EACLC,EAAK,EACLe,EAAK,EACL1nB,EAAa,GAAM,GACrB0mB,EAAc,GAARe,EAAY3wB,EAClB4wB,EAAK,IACK1nB,EAAa,GAAK,GAAM,GAClC0mB,EAAK,EACLgB,EAAc,GAARD,EAAY3wB,IACRkJ,EAAa,GAAK,GAAM,GAClC0mB,EAAK5vB,EAAsB,EAAR2wB,EAAY3wB,EAC/B4wB,EAAK5wB,IACKkJ,EAAa,GAAK,GAAM,IAClC0mB,GAAM5vB,EACN4wB,EAAM,EAAI5wB,EAA4B,EAAbA,EAAiB2wB,GAExC1wB,IACF2vB,GAAMA,GAGHlwB,IACHmwB,EAAKD,EACLA,EAAK,GAGP,IAAI5+B,EAAY,YAAc0O,EAAe,GAAKgxB,GAAc,iBAAmBhxB,EAAegxB,EAAa,GAAK,oBAAsBd,EAAK,OAASC,EAAK,OAASe,EAAK,MAM3K,GALI1qB,GAAY,IAAiB,EAAZA,IACnBsqB,EAA8B,GAAbtnB,EAA+B,GAAXhD,EACjCjG,IAAOuwB,EAA+B,IAAbtnB,EAA+B,GAAXhD,IAEnD0W,EAAS5rB,UAAUA,GACf4I,EAAOi3B,aAAc,CAEvB,IAAIC,EAAepxB,EAAekd,EAAStlB,KAAK,6BAA+BslB,EAAStlB,KAAK,4BACzFy5B,EAAcrxB,EAAekd,EAAStlB,KAAK,8BAAgCslB,EAAStlB,KAAK,+BACjE,IAAxBw5B,EAAa7iC,SACf6iC,EAAe5iC,EAAG,oCAAuCwR,EAAe,OAAS,OAAS,YAC1Fkd,EAASxmB,OAAO06B,IAES,IAAvBC,EAAY9iC,SACd8iC,EAAc7iC,EAAG,oCAAuCwR,EAAe,QAAU,UAAY,YAC7Fkd,EAASxmB,OAAO26B,IAEdD,EAAa7iC,SAAU6iC,EAAa,GAAGlkC,MAAM+3B,QAAU7iB,KAAKK,KAAK+D,EAAU,IAC3E6qB,EAAY9iC,SAAU8iC,EAAY,GAAGnkC,MAAM+3B,QAAU7iB,KAAKK,IAAI+D,EAAU,KAUhF,GAPAnG,EAAW1K,IAAI,CACb27B,2BAA6B,YAAehxB,EAAa,EAAK,KAC9DixB,wBAA0B,YAAejxB,EAAa,EAAK,KAC3DkxB,uBAAyB,YAAelxB,EAAa,EAAK,KAC1DmxB,mBAAqB,YAAenxB,EAAa,EAAK,OAGpDpG,EAAO62B,OACT,GAAI/wB,EACF0wB,EAAcp/B,UAAW,qBAAwBq/B,EAAc,EAAKz2B,EAAOw3B,cAAgB,QAAWf,EAAc,EAAK,0CAA6Cz2B,EAAkB,YAAI,SACvL,CACL,IAAIy3B,EAAcvvB,KAAK8B,IAAI4sB,GAA6D,GAA3C1uB,KAAKC,MAAMD,KAAK8B,IAAI4sB,GAAiB,IAC9E3E,EAAa,KACd/pB,KAAKwvB,IAAmB,EAAdD,EAAkBvvB,KAAKwR,GAAM,KAAO,EAC5CxR,KAAKyvB,IAAmB,EAAdF,EAAkBvvB,KAAKwR,GAAM,KAAO,GAE/Cke,EAAS53B,EAAO63B,YAChBC,EAAS93B,EAAO63B,YAAc5F,EAC9Bn3B,EAASkF,EAAOw3B,aACpBhB,EAAcp/B,UAAW,WAAawgC,EAAS,QAAUE,EAAS,uBAA0BpB,EAAe,EAAK57B,GAAU,QAAW47B,EAAe,EAAIoB,EAAU,uBAGtK,IAAIC,EAAWv1B,EAAQG,UAAYH,EAAQK,aAAiBuD,EAAa,EAAK,EAC9ED,EACG/O,UAAW,qBAAuB2gC,EAAU,gBAAkBryB,EAAOI,eAAiB,EAAI8wB,GAAiB,iBAAmBlxB,EAAOI,gBAAkB8wB,EAAgB,GAAK,SAEjLrrB,cAAe,SAAuB/T,GACpC,IACImO,EADS5T,KACI4T,IADJ5T,KAEO6U,OAEjBrP,WAAWC,GACXkG,KAAK,gHACLnG,WAAWC,GANDzF,KAOFiO,OAAO22B,WAAWE,SAPhB9kC,KAOkC+T,gBAC7CH,EAAIjI,KAAK,uBAAuBnG,WAAWC,KAwD7CwgC,EAAO,CACTxpB,aAAc,WAIZ,IAHA,IAAI9I,EAAS3T,KACT6U,EAASlB,EAAOkB,OAChBP,EAAMX,EAAOY,aACRlS,EAAI,EAAGA,EAAIwS,EAAOvS,OAAQD,GAAK,EAAG,CACzC,IAAI4uB,EAAWpc,EAAOtK,GAAGlI,GACrBkY,EAAW0W,EAAS,GAAG1W,SACvB5G,EAAO1F,OAAOi4B,WAAWC,gBAC3B5rB,EAAWpE,KAAKK,IAAIL,KAAKoM,IAAI0O,EAAS,GAAG1W,SAAU,IAAK,IAE1D,IAEI6rB,GADU,IAAM7rB,EAEhB8rB,EAAU,EACVpC,GAJShT,EAAS,GAAGvX,kBAKrBwqB,EAAK,EAYT,GAXKvwB,EAAOI,eAKDO,IACT8xB,GAAWA,IALXlC,EAAKD,EAELoC,GAAWD,EACXA,EAFAnC,EAAK,GAOPhT,EAAS,GAAGhwB,MAAMqlC,QAAUnwB,KAAK8B,IAAI9B,KAAK6uB,MAAMzqB,IAAa1F,EAAOvS,OAEhEqR,EAAO1F,OAAOi4B,WAAWhB,aAAc,CAEzC,IAAIC,EAAexxB,EAAOI,eAAiBkd,EAAStlB,KAAK,6BAA+BslB,EAAStlB,KAAK,4BAClGy5B,EAAczxB,EAAOI,eAAiBkd,EAAStlB,KAAK,8BAAgCslB,EAAStlB,KAAK,+BAC1E,IAAxBw5B,EAAa7iC,SACf6iC,EAAe5iC,EAAG,oCAAuCoR,EAAOI,eAAiB,OAAS,OAAS,YACnGkd,EAASxmB,OAAO06B,IAES,IAAvBC,EAAY9iC,SACd8iC,EAAc7iC,EAAG,oCAAuCoR,EAAOI,eAAiB,QAAU,UAAY,YACtGkd,EAASxmB,OAAO26B,IAEdD,EAAa7iC,SAAU6iC,EAAa,GAAGlkC,MAAM+3B,QAAU7iB,KAAKK,KAAK+D,EAAU,IAC3E6qB,EAAY9iC,SAAU8iC,EAAY,GAAGnkC,MAAM+3B,QAAU7iB,KAAKK,IAAI+D,EAAU,IAE9E0W,EACG5rB,UAAW,eAAiB4+B,EAAK,OAASC,EAAK,oBAAsBmC,EAAU,gBAAkBD,EAAU,UAGlH5sB,cAAe,SAAuB/T,GACpC,IAAIkO,EAAS3T,KACT6U,EAASlB,EAAOkB,OAChB4E,EAAc9F,EAAO8F,YACrBrF,EAAaT,EAAOS,WAKxB,GAJAS,EACGrP,WAAWC,GACXkG,KAAK,gHACLnG,WAAWC,GACVkO,EAAO1F,OAAOsO,kBAAiC,IAAb9W,EAAgB,CACpD,IAAI6+B,GAAiB,EAErBzvB,EAAOtK,GAAGkP,GAAatR,cAAc,WACnC,IAAIm8B,GACC3wB,IAAUA,EAAOmK,UAAtB,CAEAwmB,GAAiB,EACjB3wB,EAAOyJ,WAAY,EAEnB,IADA,IAAImnB,EAAgB,CAAC,sBAAuB,iBACnCliC,EAAI,EAAGA,EAAIkiC,EAAcjiC,OAAQD,GAAK,EAC7C+R,EAAWzM,QAAQ48B,EAAcliC,UAsDvCkkC,EAAY,CACd9pB,aAAc,WAcZ,IAbA,IAAI9I,EAAS3T,KACT0kC,EAAc/wB,EAAOF,MACrBkxB,EAAehxB,EAAOD,OACtBmB,EAASlB,EAAOkB,OAChBT,EAAaT,EAAOS,WACpBa,EAAkBtB,EAAOsB,gBACzBhH,EAAS0F,EAAO1F,OAAOu4B,gBACvBzyB,EAAeJ,EAAOI,eACtB1O,EAAYsO,EAAOmG,UACnB2sB,EAAS1yB,EAA6B2wB,EAAc,EAA3Br/B,EAA8Cs/B,EAAe,EAA5Bt/B,EAC1DqhC,EAAS3yB,EAAe9F,EAAOy4B,QAAUz4B,EAAOy4B,OAChD5sB,EAAY7L,EAAO04B,MAEdtkC,EAAI,EAAGC,EAASuS,EAAOvS,OAAQD,EAAIC,EAAQD,GAAK,EAAG,CAC1D,IAAI4uB,EAAWpc,EAAOtK,GAAGlI,GACrBuT,EAAYX,EAAgB5S,GAE5BukC,GAAqBH,EADPxV,EAAS,GAAGvX,kBACmB9D,EAAY,GAAMA,EAAa3H,EAAO44B,SAEnFT,EAAUryB,EAAe2yB,EAASE,EAAmB,EACrDP,EAAUtyB,EAAe,EAAI2yB,EAASE,EAEtCE,GAAchtB,EAAY3D,KAAK8B,IAAI2uB,GAEnClJ,EAAa3pB,EAAe,EAAI9F,EAAO84B,QAAU,EACjDtJ,EAAa1pB,EAAe9F,EAAO84B,QAAU,EAAqB,EAGlE5wB,KAAK8B,IAAIwlB,GAAc,OAASA,EAAa,GAC7CtnB,KAAK8B,IAAIylB,GAAc,OAASA,EAAa,GAC7CvnB,KAAK8B,IAAI6uB,GAAc,OAASA,EAAa,GAC7C3wB,KAAK8B,IAAImuB,GAAW,OAASA,EAAU,GACvCjwB,KAAK8B,IAAIouB,GAAW,OAASA,EAAU,GAE3C,IAAIW,EAAiB,eAAiBvJ,EAAa,MAAQC,EAAa,MAAQoJ,EAAa,gBAAkBT,EAAU,gBAAkBD,EAAU,OAIrJ,GAFAnV,EAAS5rB,UAAU2hC,GACnB/V,EAAS,GAAGhwB,MAAMqlC,OAAmD,EAAzCnwB,KAAK8B,IAAI9B,KAAK6uB,MAAM4B,IAC5C34B,EAAOi3B,aAAc,CAEvB,IAAI+B,EAAkBlzB,EAAekd,EAAStlB,KAAK,6BAA+BslB,EAAStlB,KAAK,4BAC5Fu7B,EAAiBnzB,EAAekd,EAAStlB,KAAK,8BAAgCslB,EAAStlB,KAAK,+BACjE,IAA3Bs7B,EAAgB3kC,SAClB2kC,EAAkB1kC,EAAG,oCAAuCwR,EAAe,OAAS,OAAS,YAC7Fkd,EAASxmB,OAAOw8B,IAEY,IAA1BC,EAAe5kC,SACjB4kC,EAAiB3kC,EAAG,oCAAuCwR,EAAe,QAAU,UAAY,YAChGkd,EAASxmB,OAAOy8B,IAEdD,EAAgB3kC,SAAU2kC,EAAgB,GAAGhmC,MAAM+3B,QAA6B,EAAnB4N,EAAuBA,EAAmB,GACvGM,EAAe5kC,SAAU4kC,EAAe,GAAGjmC,MAAM+3B,QAAgC,GAApB4N,GAAyBA,EAAmB,KAK7Gv3B,GAAQK,eAAiBL,GAAQQ,yBAC1BuE,EAAW,GAAGnT,MACpBkmC,kBAAoBV,EAAS,WAGpCjtB,cAAe,SAAuB/T,GACvBzF,KACN6U,OACJrP,WAAWC,GACXkG,KAAK,gHACLnG,WAAWC,KAgDd2hC,EAAS,CACX3kB,KAAM,WACJ,IAAI9O,EAAS3T,KAETqnC,EADM1zB,EAAO1F,OACMq5B,OACnBt2B,EAAc2C,EAAOjF,YACrB24B,EAAa1zB,kBAAkB3C,GACjC2C,EAAO2zB,OAAO3zB,OAAS0zB,EAAa1zB,OACpCrH,GAAMqC,OAAOgF,EAAO2zB,OAAO3zB,OAAO2W,eAAgB,CAChDrR,qBAAqB,EACrBqD,qBAAqB,IAEvBhQ,GAAMqC,OAAOgF,EAAO2zB,OAAO3zB,OAAO1F,OAAQ,CACxCgL,qBAAqB,EACrBqD,qBAAqB,KAEdhQ,GAAMkC,SAAS64B,EAAa1zB,UACrCA,EAAO2zB,OAAO3zB,OAAS,IAAI3C,EAAY1E,GAAMqC,OAAO,GAAI04B,EAAa1zB,OAAQ,CAC3EuF,uBAAuB,EACvBD,qBAAqB,EACrBqD,qBAAqB,KAEvB3I,EAAO2zB,OAAOC,eAAgB,GAEhC5zB,EAAO2zB,OAAO3zB,OAAOC,IAAIjQ,SAASgQ,EAAO1F,OAAOq5B,OAAOE,sBACvD7zB,EAAO2zB,OAAO3zB,OAAO/N,GAAG,MAAO+N,EAAO2zB,OAAOG,eAE/CA,aAAc,WACZ,IAAI9zB,EAAS3T,KACT0nC,EAAe/zB,EAAO2zB,OAAO3zB,OACjC,GAAK+zB,EAAL,CACA,IAAIrrB,EAAeqrB,EAAarrB,aAC5BD,EAAesrB,EAAatrB,aAChC,KAAIA,GAAgB7Z,EAAE6Z,GAAcjY,SAASwP,EAAO1F,OAAOq5B,OAAOK,wBAC9D,MAAOtrB,GAAX,CACA,IAAI2C,EAMJ,GAJEA,EADE0oB,EAAaz5B,OAAOkN,KACPlH,SAAS1R,EAAEmlC,EAAatrB,cAAc7X,KAAK,2BAA4B,IAEvE8X,EAEb1I,EAAO1F,OAAOkN,KAAM,CACtB,IAAIysB,EAAej0B,EAAO8F,YACtB9F,EAAOkB,OAAOtK,GAAGq9B,GAAczjC,SAASwP,EAAO1F,OAAOmN,uBACxDzH,EAAOwK,UAEPxK,EAAOyK,YAAczK,EAAOS,WAAW,GAAGjL,WAC1Cy+B,EAAej0B,EAAO8F,aAExB,IAAI+E,EAAY7K,EAAOkB,OAAOtK,GAAGq9B,GAAct8B,QAAS,6BAAgC0T,EAAe,MAAQzU,GAAG,GAAGH,QACjH4E,EAAY2E,EAAOkB,OAAOtK,GAAGq9B,GAAc18B,QAAS,6BAAgC8T,EAAe,MAAQzU,GAAG,GAAGH,QAC7E4U,OAAf,IAAdR,EAA4CxP,OACzB,IAAdA,EAA4CwP,EACnDxP,EAAY44B,EAAeA,EAAeppB,EAA4BxP,EACzDwP,EAExB7K,EAAO0J,QAAQ2B,MAEjBzL,OAAQ,SAAgBs0B,GACtB,IAAIl0B,EAAS3T,KACT0nC,EAAe/zB,EAAO2zB,OAAO3zB,OACjC,GAAK+zB,EAAL,CAEA,IAAIpxB,EAAsD,SAAtCoxB,EAAaz5B,OAAOqI,cACpCoxB,EAAa3oB,uBACb2oB,EAAaz5B,OAAOqI,cAExB,GAAI3C,EAAOsH,YAAcysB,EAAazsB,UAAW,CAC/C,IACI6sB,EADAC,EAAqBL,EAAajuB,YAEtC,GAAIiuB,EAAaz5B,OAAOkN,KAAM,CACxBusB,EAAa7yB,OAAOtK,GAAGw9B,GAAoB5jC,SAASujC,EAAaz5B,OAAOmN,uBAC1EssB,EAAavpB,UAEbupB,EAAatpB,YAAcspB,EAAatzB,WAAW,GAAGjL,WACtD4+B,EAAqBL,EAAajuB,aAGpC,IAAIuuB,EAAkBN,EAAa7yB,OAAOtK,GAAGw9B,GAAoBz8B,QAAS,6BAAiCqI,EAAgB,UAAI,MAAQpJ,GAAG,GAAGH,QACzI69B,EAAkBP,EAAa7yB,OAAOtK,GAAGw9B,GAAoB78B,QAAS,6BAAiCyI,EAAgB,UAAI,MAAQpJ,GAAG,GAAGH,QAC/F09B,OAAf,IAApBE,EAAoDC,OAC3B,IAApBA,EAAoDD,EAC3DC,EAAkBF,GAAuBA,EAAqBC,EAAoCD,EAClGE,EAAkBF,EAAqBA,EAAqBC,EAAoCC,EACjFD,OAExBF,EAAiBn0B,EAAOsH,UAEtBysB,EAAaztB,qBAAqBnX,QAAQglC,GAAkB,IAC1DJ,EAAaz5B,OAAO+J,eAEpB8vB,EADmBC,EAAjBD,EACeA,EAAiB3xB,KAAKC,MAAME,EAAgB,GAAK,EAEjDwxB,EAAiB3xB,KAAKC,MAAME,EAAgB,GAAK,EAE1CyxB,EAAjBD,IACTA,EAAiBA,EAAiBxxB,EAAgB,GAEpDoxB,EAAarqB,QAAQyqB,EAAgBD,EAAU,OAAI9gC,IAKvD,IAAImhC,EAAmB,EACnBC,EAAmBx0B,EAAO1F,OAAOq5B,OAAOK,sBAO5C,GALkC,EAA9Bh0B,EAAO1F,OAAOqI,gBAAsB3C,EAAO1F,OAAO+J,iBACpDkwB,EAAmBv0B,EAAO1F,OAAOqI,eAGnCoxB,EAAa7yB,OAAO5Q,YAAYkkC,GAC5BT,EAAaz5B,OAAOkN,KACtB,IAAK,IAAI9Y,EAAI,EAAGA,EAAI6lC,EAAkB7lC,GAAK,EACzCqlC,EAAatzB,WAAWrT,SAAU,8BAAiC4S,EAAOsH,UAAY5Y,GAAK,MAAQsB,SAASwkC,QAG9G,IAAK,IAAI9vB,EAAM,EAAGA,EAAM6vB,EAAkB7vB,GAAO,EAC/CqvB,EAAa7yB,OAAOtK,GAAGoJ,EAAOsH,UAAY5C,GAAK1U,SAASwkC,MAyE5D/2B,EAAa,CACfyc,EACAC,EACAE,EACAE,EACAsB,EACA6B,EACAuB,EAtoGiB,CACjB5f,KAAM,aACN/E,OAAQ,CACNqmB,WAAY,CACV3f,SAAS,EACT4f,gBAAgB,EAChBI,QAAQ,EACRD,aAAa,EACbE,YAAa,EACbM,aAAc,cAGlBtiB,OAAQ,WACN,IAAIe,EAAS3T,KACbsM,GAAMqC,OAAOgF,EAAQ,CACnB2gB,WAAY,CACV3f,SAAS,EACT+d,OAAQG,EAAWH,OAAOhgB,KAAKiB,GAC/Bgf,QAASE,EAAWF,QAAQjgB,KAAKiB,GACjC8d,OAAQoB,EAAWpB,OAAO/e,KAAKiB,GAC/BwgB,iBAAkBtB,EAAWsB,iBAAiBzhB,KAAKiB,GACnD0gB,iBAAkBxB,EAAWwB,iBAAiB3hB,KAAKiB,GACnDmf,eAAgBxmB,GAAMM,UAI5BhH,GAAI,CACF6c,KAAM,WACSziB,KACFiO,OAAOqmB,WAAW3f,SADhB3U,KACkCs0B,WAAW5B,UAE5DlF,QAAS,WACMxtB,KACFs0B,WAAW3f,SADT3U,KAC2Bs0B,WAAW3B,aAyGtC,CACjB3f,KAAM,aACN/E,OAAQ,CACN+c,WAAY,CACV0K,OAAQ,KACRC,OAAQ,KAERyS,aAAa,EACb9S,cAAe,yBACfiD,YAAa,uBACbhD,UAAW,uBAGf3iB,OAAQ,WACN,IAAIe,EAAS3T,KACbsM,GAAMqC,OAAOgF,EAAQ,CACnBqX,WAAY,CACVvI,KAAM0S,EAAW1S,KAAK/P,KAAKiB,GAC3BJ,OAAQ4hB,EAAW5hB,OAAOb,KAAKiB,GAC/B6Z,QAAS2H,EAAW3H,QAAQ9a,KAAKiB,GACjC8hB,YAAaN,EAAWM,YAAY/iB,KAAKiB,GACzC6hB,YAAaL,EAAWK,YAAY9iB,KAAKiB,OAI/C/N,GAAI,CACF6c,KAAM,WACSziB,KACNgrB,WAAWvI,OADLziB,KAENgrB,WAAWzX,UAEpB80B,OAAQ,WACOroC,KACNgrB,WAAWzX,UAEpB+0B,SAAU,WACKtoC,KACNgrB,WAAWzX,UAEpBia,QAAS,WACMxtB,KACNgrB,WAAWwC,WAEpBiU,MAAO,SAAep7B,GACpB,IASMkiC,EATF50B,EAAS3T,KACT2vB,EAAMhc,EAAOqX,WACboK,EAAUzF,EAAIyF,QACdC,EAAU1F,EAAI0F,SAEhB1hB,EAAO1F,OAAO+c,WAAWod,aACrB7lC,EAAE8D,EAAEC,QAAQI,GAAG2uB,IACf9yB,EAAE8D,EAAEC,QAAQI,GAAG0uB,KAGfA,EACFmT,EAAWnT,EAAQjxB,SAASwP,EAAO1F,OAAO+c,WAAWuN,aAC5ClD,IACTkT,EAAWlT,EAAQlxB,SAASwP,EAAO1F,OAAO+c,WAAWuN,eAEtC,IAAbgQ,EACF50B,EAAO/B,KAAK,iBAAkB+B,GAE9BA,EAAO/B,KAAK,iBAAkB+B,GAE5ByhB,GACFA,EAAQ/wB,YAAYsP,EAAO1F,OAAO+c,WAAWuN,aAE3ClD,GACFA,EAAQhxB,YAAYsP,EAAO1F,OAAO+c,WAAWuN,iBAmPpC,CACjBvlB,KAAM,aACN/E,OAAQ,CACN4nB,WAAY,CACV3wB,GAAI,KACJsjC,cAAe,OACfrQ,WAAW,EACXiQ,aAAa,EACbxQ,aAAc,KACdK,kBAAmB,KACnBH,eAAgB,KAChBN,aAAc,KACdJ,qBAAqB,EACrBxR,KAAM,UACNyQ,gBAAgB,EAChBE,mBAAoB,EACpBU,sBAAuB,SAAUwR,GAAU,OAAOA,GAClDvR,oBAAqB,SAAUuR,GAAU,OAAOA,GAChD5Q,YAAa,2BACbjB,kBAAmB,kCACnByB,cAAe,qBACfN,aAAc,4BACdC,WAAY,0BACZO,YAAa,2BACbL,qBAAsB,qCACtBI,yBAA0B,yCAC1BF,eAAgB,8BAChB7C,UAAW,2BAGf3iB,OAAQ,WACN,IAAIe,EAAS3T,KACbsM,GAAMqC,OAAOgF,EAAQ,CACnBkiB,WAAY,CACVpT,KAAMmT,EAAWnT,KAAK/P,KAAKiB,GAC3B8jB,OAAQ7B,EAAW6B,OAAO/kB,KAAKiB,GAC/BJ,OAAQqiB,EAAWriB,OAAOb,KAAKiB,GAC/B6Z,QAASoI,EAAWpI,QAAQ9a,KAAKiB,GACjC6iB,mBAAoB,MAI1B5wB,GAAI,CACF6c,KAAM,WACSziB,KACN61B,WAAWpT,OADLziB,KAEN61B,WAAW4B,SAFLz3B,KAGN61B,WAAWtiB,UAEpBm1B,kBAAmB,WACJ1oC,KACFiO,OAAOkN,KADLnb,KAEJ61B,WAAWtiB,cACmB,IAH1BvT,KAGY8Y,WAHZ9Y,KAIJ61B,WAAWtiB,UAGtBo1B,gBAAiB,WACF3oC,KACDiO,OAAOkN,MADNnb,KAEJ61B,WAAWtiB,UAGtBq1B,mBAAoB,WACL5oC,KACFiO,OAAOkN,OADLnb,KAEJ61B,WAAW4B,SAFPz3B,KAGJ61B,WAAWtiB,WAGtBs1B,qBAAsB,WACP7oC,KACDiO,OAAOkN,OADNnb,KAEJ61B,WAAW4B,SAFPz3B,KAGJ61B,WAAWtiB,WAGtBia,QAAS,WACMxtB,KACN61B,WAAWrI,WAEpBiU,MAAO,SAAep7B,GACpB,IAAIsN,EAAS3T,KAEX2T,EAAO1F,OAAO4nB,WAAW3wB,IACtByO,EAAO1F,OAAO4nB,WAAWuS,aACM,EAA/Bz0B,EAAOkiB,WAAWjiB,IAAItR,SACrBC,EAAE8D,EAAEC,QAAQnC,SAASwP,EAAO1F,OAAO4nB,WAAWgC,gBAGjC,IADFlkB,EAAOkiB,WAAWjiB,IAAIzP,SAASwP,EAAO1F,OAAO4nB,WAAW0C,aAErE5kB,EAAO/B,KAAK,iBAAkB+B,GAE9BA,EAAO/B,KAAK,iBAAkB+B,GAEhCA,EAAOkiB,WAAWjiB,IAAIvP,YAAYsP,EAAO1F,OAAO4nB,WAAW0C,iBAgRjD,CAChBvlB,KAAM,YACN/E,OAAQ,CACNwqB,UAAW,CACTvzB,GAAI,KACJwzB,SAAU,OACVK,MAAM,EACNmB,WAAW,EACXN,eAAe,EACfrE,UAAW,wBACXuT,UAAW,0BAGfl2B,OAAQ,WACN,IAAIe,EAAS3T,KACbsM,GAAMqC,OAAOgF,EAAQ,CACnB8kB,UAAW,CACThW,KAAM+V,EAAU/V,KAAK/P,KAAKiB,GAC1B6Z,QAASgL,EAAUhL,QAAQ9a,KAAKiB,GAChCH,WAAYglB,EAAUhlB,WAAWd,KAAKiB,GACtC8I,aAAc+b,EAAU/b,aAAa/J,KAAKiB,GAC1C6F,cAAegf,EAAUhf,cAAc9G,KAAKiB,GAC5CkmB,gBAAiBrB,EAAUqB,gBAAgBnnB,KAAKiB,GAChDomB,iBAAkBvB,EAAUuB,iBAAiBrnB,KAAKiB,GAClDylB,gBAAiBZ,EAAUY,gBAAgB1mB,KAAKiB,GAChD6lB,YAAahB,EAAUgB,YAAY9mB,KAAKiB,GACxC+lB,WAAYlB,EAAUkB,WAAWhnB,KAAKiB,GACtCgmB,UAAWnB,EAAUmB,UAAUjnB,KAAKiB,GACpCoS,WAAW,EACX8O,QAAS,KACT4E,YAAa,SAInB7zB,GAAI,CACF6c,KAAM,WACSziB,KACNy4B,UAAUhW,OADJziB,KAENy4B,UAAUjlB,aAFJxT,KAGNy4B,UAAUhc,gBAEnBlJ,OAAQ,WACOvT,KACNy4B,UAAUjlB,cAEnB2a,OAAQ,WACOnuB,KACNy4B,UAAUjlB,cAEnBqb,eAAgB,WACD7uB,KACNy4B,UAAUjlB,cAEnBiJ,aAAc,WACCzc,KACNy4B,UAAUhc,gBAEnBjD,cAAe,SAAuB/T,GACvBzF,KACNy4B,UAAUjf,cAAc/T,IAEjC+nB,QAAS,WACMxtB,KACNy4B,UAAUjL,aAyFN,CACfxa,KAAM,WACN/E,OAAQ,CACNusB,SAAU,CACR7lB,SAAS,IAGb/B,OAAQ,WAENtG,GAAMqC,OADO3O,KACQ,CACnBw6B,SAAU,CACRJ,aAAcD,EAASC,aAAa1nB,KAH3B1S,MAITyc,aAAc0d,EAAS1d,aAAa/J,KAJ3B1S,MAKTwZ,cAAe2gB,EAAS3gB,cAAc9G,KAL7B1S,UASf4F,GAAI,CACF0rB,WAAY,WACGtxB,KACDiO,OAAOusB,SAAS7lB,UADf3U,KAENiO,OAAOgL,qBAAsB,EAFvBjZ,KAGNsqB,eAAerR,qBAAsB,IAE9CwJ,KAAM,WACSziB,KACDiO,OAAOusB,SAAS7lB,SADf3U,KAENw6B,SAAS/d,gBAElBA,aAAc,WACCzc,KACDiO,OAAOusB,SAAS7lB,SADf3U,KAENw6B,SAAS/d,gBAElBjD,cAAe,SAAuB/T,GACvBzF,KACDiO,OAAOusB,SAAS7lB,SADf3U,KAENw6B,SAAShhB,cAAc/T,MAwavB,CACXuN,KAAM,OACN/E,OAAQ,CACNktB,KAAM,CACJxmB,SAAS,EACT+mB,SAAU,EACVI,SAAU,EACVx3B,QAAQ,EACRykC,eAAgB,wBAChBC,iBAAkB,wBAGtBp2B,OAAQ,WACN,IAAIe,EAAS3T,KACTm7B,EAAO,CACTxmB,SAAS,EACT0iB,MAAO,EACPkD,aAAc,EACdoB,WAAW,EACXP,QAAS,CACPnK,cAAUlqB,EACVm1B,gBAAYn1B,EACZo1B,iBAAap1B,EACby0B,cAAUz0B,EACV00B,kBAAc10B,EACd20B,SAAU,GAEZ7P,MAAO,CACL9F,eAAWhf,EACXif,aAASjf,EACTmf,cAAUnf,EACVsf,cAAUtf,EACVu1B,UAAMv1B,EACNy1B,UAAMz1B,EACNw1B,UAAMx1B,EACN01B,UAAM11B,EACN0M,WAAO1M,EACP2M,YAAQ3M,EACRwf,YAAQxf,EACRyf,YAAQzf,EACRk1B,aAAc,GACdS,eAAgB,IAElB5T,SAAU,CACRnM,OAAG5V,EACH6V,OAAG7V,EACH41B,mBAAe51B,EACf61B,mBAAe71B,EACf81B,cAAU91B,IAId,+HAAiI5D,MAAM,KAAK+I,QAAQ,SAAUC,GAC5JgvB,EAAKhvB,GAAcyuB,EAAKzuB,GAAYuG,KAAKiB,KAE3CrH,GAAMqC,OAAOgF,EAAQ,CACnBwnB,KAAMA,IAGR,IAAI9D,EAAQ,EACZrrB,OAAOsE,eAAeqD,EAAOwnB,KAAM,QAAS,CAC1C5qB,IAAK,WACH,OAAO8mB,GAETxkB,IAAK,SAAapO,GAChB,GAAI4yB,IAAU5yB,EAAO,CACnB,IAAI+mB,EAAU7X,EAAOwnB,KAAKC,QAAQI,SAAW7nB,EAAOwnB,KAAKC,QAAQI,SAAS,QAAKz0B,EAC3EwmB,EAAU5Z,EAAOwnB,KAAKC,QAAQnK,SAAWtd,EAAOwnB,KAAKC,QAAQnK,SAAS,QAAKlqB,EAC/E4M,EAAO/B,KAAK,aAAcnN,EAAO+mB,EAAS+B,GAE5C8J,EAAQ5yB,MAIdmB,GAAI,CACF6c,KAAM,WACSziB,KACFiO,OAAOktB,KAAKxmB,SADV3U,KAEJm7B,KAAKzI,UAGhBlF,QAAS,WACMxtB,KACNm7B,KAAKxI,WAEdsW,WAAY,SAAoB5iC,GACjBrG,KACDm7B,KAAKxmB,SADJ3U,KAENm7B,KAAK5V,aAAalf,IAE3B6iC,SAAU,SAAkB7iC,GACbrG,KACDm7B,KAAKxmB,SADJ3U,KAENm7B,KAAK/S,WAAW/hB,IAEzB8iC,UAAW,SAAmB9iC,GACfrG,KACFiO,OAAOktB,KAAKxmB,SADV3U,KAC4Bm7B,KAAKxmB,SADjC3U,KACmDiO,OAAOktB,KAAK72B,QAD/DtE,KAEJm7B,KAAK72B,OAAO+B,IAGvB8B,cAAe,WACAnI,KACFm7B,KAAKxmB,SADH3U,KACqBiO,OAAOktB,KAAKxmB,SADjC3U,KAEJm7B,KAAKiC,qBA4IP,CACXpqB,KAAM,OACN/E,OAAQ,CACNyiB,KAAM,CACJ/b,SAAS,EACTqqB,cAAc,EACdC,mBAAoB,EACpBmK,uBAAuB,EAEvB/K,aAAc,cACdE,aAAc,sBACdD,YAAa,qBACb+K,eAAgB,0BAGpBz2B,OAAQ,WAENtG,GAAMqC,OADO3O,KACQ,CACnB0wB,KAAM,CACJoO,oBAAoB,EACpBnO,KAAMsN,EAAKtN,KAAKje,KAJP1S,MAKTk+B,YAAaD,EAAKC,YAAYxrB,KALrB1S,UASf4F,GAAI,CACF0rB,WAAY,WACGtxB,KACFiO,OAAOyiB,KAAK/b,SADV3U,KAC4BiO,OAAOsW,gBADnCvkB,KAEJiO,OAAOsW,eAAgB,IAGlC9B,KAAM,WACSziB,KACFiO,OAAOyiB,KAAK/b,UADV3U,KAC6BiO,OAAOkN,MAAuC,IAD3Enb,KACmDiO,OAAOwP,cAD1Dzd,KAEJ0wB,KAAKC,QAGhB2Y,OAAQ,WACOtpC,KACFiO,OAAOoU,WADLriB,KACyBiO,OAAOiV,gBADhCljB,KAEJ0wB,KAAKC,QAGhBxC,OAAQ,WACOnuB,KACFiO,OAAOyiB,KAAK/b,SADV3U,KAEJ0wB,KAAKC,QAGhB4Y,kBAAmB,WACJvpC,KACFiO,OAAOyiB,KAAK/b,SADV3U,KAEJ0wB,KAAKC,QAGhB5T,gBAAiB,WACf,IAAIpJ,EAAS3T,KACT2T,EAAO1F,OAAOyiB,KAAK/b,UACjBhB,EAAO1F,OAAOyiB,KAAK0Y,wBAA2Bz1B,EAAO1F,OAAOyiB,KAAK0Y,wBAA0Bz1B,EAAO+c,KAAKoO,qBACzGnrB,EAAO+c,KAAKC,QAIlBxoB,cAAe,WACAnI,KACFiO,OAAOyiB,KAAK/b,UADV3U,KAC6BiO,OAAOyiB,KAAK0Y,uBADzCppC,KAEJ0wB,KAAKC,UAqID,CACjB3d,KAAM,aACN/E,OAAQ,CACN8xB,WAAY,CACVM,aAASt5B,EACTy5B,SAAS,EACTD,GAAI,UAGR3tB,OAAQ,WACN,IAAIe,EAAS3T,KACbsM,GAAMqC,OAAOgF,EAAQ,CACnBosB,WAAY,CACVM,QAAS1sB,EAAO1F,OAAO8xB,WAAWM,QAClCR,uBAAwBR,EAAWQ,uBAAuBntB,KAAKiB,GAC/D8I,aAAc4iB,EAAW5iB,aAAa/J,KAAKiB,GAC3C6F,cAAe6lB,EAAW7lB,cAAc9G,KAAKiB,OAInD/N,GAAI,CACF2N,OAAQ,WACOvT,KACD+/B,WAAWM,SADVrgC,KAEF+/B,WAAWC,SAFThgC,KAGJ+/B,WAAWC,YAASj5B,SAHhB/G,KAIG+/B,WAAWC,SAG7B7R,OAAQ,WACOnuB,KACD+/B,WAAWM,SADVrgC,KAEF+/B,WAAWC,SAFThgC,KAGJ+/B,WAAWC,YAASj5B,SAHhB/G,KAIG+/B,WAAWC,SAG7BnR,eAAgB,WACD7uB,KACD+/B,WAAWM,SADVrgC,KAEF+/B,WAAWC,SAFThgC,KAGJ+/B,WAAWC,YAASj5B,SAHhB/G,KAIG+/B,WAAWC,SAG7BvjB,aAAc,SAAsB3C,EAAW4C,GAChC1c,KACD+/B,WAAWM,SADVrgC,KAEN+/B,WAAWtjB,aAAa3C,EAAW4C,IAE5ClD,cAAe,SAAuB/T,EAAUiX,GACjC1c,KACD+/B,WAAWM,SADVrgC,KAEN+/B,WAAWvmB,cAAc/T,EAAUiX,MA2JrC,CACT1J,KAAM,OACN/E,OAAQ,CACNyyB,KAAM,CACJ/rB,SAAS,EACT60B,kBAAmB,sBACnBhI,iBAAkB,iBAClBF,iBAAkB,aAClBC,kBAAmB,0BACnBF,iBAAkB,yBAClBY,wBAAyB,0BAG7BrvB,OAAQ,WACN,IAAIe,EAAS3T,KACbsM,GAAMqC,OAAOgF,EAAQ,CACnB+sB,KAAM,CACJkB,WAAYr/B,EAAG,gBAAoBoR,EAAO1F,OAAOyyB,KAAsB,kBAAI,yDAG/E10B,OAAOC,KAAKy0B,GAAMx0B,QAAQ,SAAUC,GAClCwH,EAAO+sB,KAAKv0B,GAAcu0B,EAAKv0B,GAAYuG,KAAKiB,MAGpD/N,GAAI,CACF6c,KAAM,WACSziB,KACDiO,OAAOyyB,KAAK/rB,UADX3U,KAEN0gC,KAAKje,OAFCziB,KAGN0gC,KAAKmB,qBAEdwG,OAAQ,WACOroC,KACDiO,OAAOyyB,KAAK/rB,SADX3U,KAEN0gC,KAAKmB,oBAEdyG,SAAU,WACKtoC,KACDiO,OAAOyyB,KAAK/rB,SADX3U,KAEN0gC,KAAKmB,oBAEd4H,iBAAkB,WACHzpC,KACDiO,OAAOyyB,KAAK/rB,SADX3U,KAEN0gC,KAAKoB,oBAEdtU,QAAS,WACMxtB,KACDiO,OAAOyyB,KAAK/rB,SADX3U,KAEN0gC,KAAKlT,aAoFF,CACdxa,KAAM,UACN/E,OAAQ,CACNvM,QAAS,CACPiT,SAAS,EACT6tB,cAAc,EACdv9B,IAAK,WAGT2N,OAAQ,WACN,IAAIe,EAAS3T,KACbsM,GAAMqC,OAAOgF,EAAQ,CACnBjS,QAAS,CACP+gB,KAAMyf,EAAQzf,KAAK/P,KAAKiB,GACxBkvB,WAAYX,EAAQW,WAAWnwB,KAAKiB,GACpC8uB,mBAAoBP,EAAQO,mBAAmB/vB,KAAKiB,GACpD4uB,cAAeL,EAAQK,cAAc7vB,KAAKiB,GAC1C6Z,QAAS0U,EAAQ1U,QAAQ9a,KAAKiB,OAIpC/N,GAAI,CACF6c,KAAM,WACSziB,KACFiO,OAAOvM,QAAQiT,SADb3U,KAEJ0B,QAAQ+gB,QAGnB+K,QAAS,WACMxtB,KACFiO,OAAOvM,QAAQiT,SADb3U,KAEJ0B,QAAQ8rB,WAGnBrlB,cAAe,WACAnI,KACF0B,QAAQgc,aADN1d,KAEJ0B,QAAQmhC,WAFJ7iC,KAEsBiO,OAAOvM,QAAQuD,IAFrCjF,KAEiDyZ,gBAuD7C,CACrBzG,KAAM,kBACN/E,OAAQ,CACNm0B,eAAgB,CACdztB,SAAS,EACT6tB,cAAc,EACdc,YAAY,IAGhB1wB,OAAQ,WACN,IAAIe,EAAS3T,KACbsM,GAAMqC,OAAOgF,EAAQ,CACnByuB,eAAgB,CACd1kB,aAAa,EACb+E,KAAMygB,EAAezgB,KAAK/P,KAAKiB,GAC/B6Z,QAAS0V,EAAe1V,QAAQ9a,KAAKiB,GACrC0vB,QAASH,EAAeG,QAAQ3wB,KAAKiB,GACrCwvB,YAAaD,EAAeC,YAAYzwB,KAAKiB,OAInD/N,GAAI,CACF6c,KAAM,WACSziB,KACFiO,OAAOm0B,eAAeztB,SADpB3U,KAEJoiC,eAAe3f,QAG1B+K,QAAS,WACMxtB,KACFiO,OAAOm0B,eAAeztB,SADpB3U,KAEJoiC,eAAe5U,WAG1BrlB,cAAe,WACAnI,KACFoiC,eAAe1kB,aADb1d,KAEJoiC,eAAeiB,aAoFb,CACfrwB,KAAM,WACN/E,OAAQ,CACN6mB,SAAU,CACRngB,SAAS,EACThI,MAAO,IACPo3B,mBAAmB,EACnB2F,sBAAsB,EACtB/F,iBAAiB,EACjBD,kBAAkB,IAGtB9wB,OAAQ,WACN,IAAIe,EAAS3T,KACbsM,GAAMqC,OAAOgF,EAAQ,CACnBmhB,SAAU,CACR8O,SAAS,EACTE,QAAQ,EACRN,IAAKD,EAASC,IAAI9wB,KAAKiB,GACvBgW,MAAO4Z,EAAS5Z,MAAMjX,KAAKiB,GAC3BqhB,KAAMuO,EAASvO,KAAKtiB,KAAKiB,GACzBkwB,MAAON,EAASM,MAAMnxB,KAAKiB,GAC3BypB,gBAAiB,SAAyB/2B,GACnCsN,IAAUA,EAAOmK,WAAcnK,EAAOS,YACvC/N,EAAEC,SAAWtG,OACjB2T,EAAOS,WAAW,GAAG/T,oBAAoB,gBAAiBsT,EAAOmhB,SAASsI,iBAC1EzpB,EAAOS,WAAW,GAAG/T,oBAAoB,sBAAuBsT,EAAOmhB,SAASsI,iBAChFzpB,EAAOmhB,SAASgP,QAAS,EACpBnwB,EAAOmhB,SAAS8O,QAGnBjwB,EAAOmhB,SAAS0O,MAFhB7vB,EAAOmhB,SAASE,aAQ1BpvB,GAAI,CACF6c,KAAM,WACSziB,KACFiO,OAAO6mB,SAASngB,SADd3U,KAEJ80B,SAASnL,SAGpBggB,sBAAuB,SAA+BtwB,EAAOiE,GAC9Ctd,KACF80B,SAAS8O,UACdtmB,IAFOtd,KAEaiO,OAAO6mB,SAAS4U,qBAF7B1pC,KAGF80B,SAAS+O,MAAMxqB,GAHbrZ,KAKF80B,SAASE,SAItB4U,gBAAiB,WACF5pC,KACF80B,SAAS8O,UADP5jC,KAEAiO,OAAO6mB,SAAS4U,qBAFhB1pC,KAGF80B,SAASE,OAHPh1B,KAKF80B,SAAS+O,UAItBrW,QAAS,WACMxtB,KACF80B,SAAS8O,SADP5jC,KAEJ80B,SAASE,UAmDP,CACfhiB,KAAM,cACN/E,OAAQ,CACNm2B,WAAY,CACVC,WAAW,IAGfzxB,OAAQ,WAENtG,GAAMqC,OADO3O,KACQ,CACnBokC,WAAY,CACV3nB,aAAcunB,EAAKvnB,aAAa/J,KAHvB1S,MAITwZ,cAAewqB,EAAKxqB,cAAc9G,KAJzB1S,UAQf4F,GAAI,CACF0rB,WAAY,WACV,IAAI3d,EAAS3T,KACb,GAA6B,SAAzB2T,EAAO1F,OAAOkK,OAAlB,CACAxE,EAAOuX,WAAWjoB,KAAO0Q,EAAO1F,OAA6B,uBAAI,QACjE,IAAIsjB,EAAkB,CACpBjb,cAAe,EACfJ,gBAAiB,EACjBgC,eAAgB,EAChBe,qBAAqB,EACrBzD,aAAc,EACd+G,kBAAkB,GAEpBjQ,GAAMqC,OAAOgF,EAAO1F,OAAQsjB,GAC5BjlB,GAAMqC,OAAOgF,EAAO2W,eAAgBiH,KAEtC9U,aAAc,WAEiB,SADhBzc,KACFiO,OAAOkK,QADLnY,KAENokC,WAAW3nB,gBAEpBjD,cAAe,SAAuB/T,GAEP,SADhBzF,KACFiO,OAAOkK,QADLnY,KAENokC,WAAW5qB,cAAc/T,MAwIrB,CACfuN,KAAM,cACN/E,OAAQ,CACN22B,WAAY,CACVM,cAAc,EACdJ,QAAQ,EACRW,aAAc,GACdK,YAAa,MAGjBlzB,OAAQ,WAENtG,GAAMqC,OADO3O,KACQ,CACnB4kC,WAAY,CACVnoB,aAAc+nB,EAAK/nB,aAAa/J,KAHvB1S,MAITwZ,cAAegrB,EAAKhrB,cAAc9G,KAJzB1S,UAQf4F,GAAI,CACF0rB,WAAY,WACV,IAAI3d,EAAS3T,KACb,GAA6B,SAAzB2T,EAAO1F,OAAOkK,OAAlB,CACAxE,EAAOuX,WAAWjoB,KAAO0Q,EAAO1F,OAA6B,uBAAI,QACjE0F,EAAOuX,WAAWjoB,KAAO0Q,EAAO1F,OAA6B,uBAAI,MACjE,IAAIsjB,EAAkB,CACpBjb,cAAe,EACfJ,gBAAiB,EACjBgC,eAAgB,EAChBe,qBAAqB,EACrBmL,gBAAiB,EACjB5O,aAAc,EACdwC,gBAAgB,EAChBuE,kBAAkB,GAEpBjQ,GAAMqC,OAAOgF,EAAO1F,OAAQsjB,GAC5BjlB,GAAMqC,OAAOgF,EAAO2W,eAAgBiH,KAEtC9U,aAAc,WAEiB,SADhBzc,KACFiO,OAAOkK,QADLnY,KAEN4kC,WAAWnoB,gBAEpBjD,cAAe,SAAuB/T,GAEP,SADhBzF,KACFiO,OAAOkK,QADLnY,KAEN4kC,WAAWprB,cAAc/T,MA+ErB,CACfuN,KAAM,cACN/E,OAAQ,CACNi4B,WAAY,CACVhB,cAAc,EACdiB,eAAe,IAGnBvzB,OAAQ,WAENtG,GAAMqC,OADO3O,KACQ,CACnBkmC,WAAY,CACVzpB,aAAcwpB,EAAKxpB,aAAa/J,KAHvB1S,MAITwZ,cAAeysB,EAAKzsB,cAAc9G,KAJzB1S,UAQf4F,GAAI,CACF0rB,WAAY,WACV,IAAI3d,EAAS3T,KACb,GAA6B,SAAzB2T,EAAO1F,OAAOkK,OAAlB,CACAxE,EAAOuX,WAAWjoB,KAAO0Q,EAAO1F,OAA6B,uBAAI,QACjE0F,EAAOuX,WAAWjoB,KAAO0Q,EAAO1F,OAA6B,uBAAI,MACjE,IAAIsjB,EAAkB,CACpBjb,cAAe,EACfJ,gBAAiB,EACjBgC,eAAgB,EAChBe,qBAAqB,EACrBzD,aAAc,EACd+G,kBAAkB,GAEpBjQ,GAAMqC,OAAOgF,EAAO1F,OAAQsjB,GAC5BjlB,GAAMqC,OAAOgF,EAAO2W,eAAgBiH,KAEtC9U,aAAc,WAEiB,SADhBzc,KACFiO,OAAOkK,QADLnY,KAENkmC,WAAWzpB,gBAEpBjD,cAAe,SAAuB/T,GAEP,SADhBzF,KACFiO,OAAOkK,QADLnY,KAENkmC,WAAW1sB,cAAc/T,MA6EhB,CACpBuN,KAAM,mBACN/E,OAAQ,CACNu4B,gBAAiB,CACfE,OAAQ,GACRK,QAAS,EACTJ,MAAO,IACPE,SAAU,EACV3B,cAAc,IAGlBtyB,OAAQ,WAENtG,GAAMqC,OADO3O,KACQ,CACnBwmC,gBAAiB,CACf/pB,aAAc8pB,EAAU9pB,aAAa/J,KAH5B1S,MAITwZ,cAAe+sB,EAAU/sB,cAAc9G,KAJ9B1S,UAQf4F,GAAI,CACF0rB,WAAY,WACV,IAAI3d,EAAS3T,KACgB,cAAzB2T,EAAO1F,OAAOkK,SAElBxE,EAAOuX,WAAWjoB,KAAO0Q,EAAO1F,OAA6B,uBAAI,aACjE0F,EAAOuX,WAAWjoB,KAAO0Q,EAAO1F,OAA6B,uBAAI,MAEjE0F,EAAO1F,OAAOgL,qBAAsB,EACpCtF,EAAO2W,eAAerR,qBAAsB,IAE9CwD,aAAc,WAEiB,cADhBzc,KACFiO,OAAOkK,QADLnY,KAENwmC,gBAAgB/pB,gBAEzBjD,cAAe,SAAuB/T,GAEP,cADhBzF,KACFiO,OAAOkK,QADLnY,KAENwmC,gBAAgBhtB,cAAc/T,MA+H5B,CACbuN,KAAM,SACN/E,OAAQ,CACNq5B,OAAQ,CACN3zB,OAAQ,KACRg0B,sBAAuB,4BACvBH,qBAAsB,4BAG1B50B,OAAQ,WAENtG,GAAMqC,OADO3O,KACQ,CACnBsnC,OAAQ,CACN3zB,OAAQ,KACR8O,KAAM2kB,EAAO3kB,KAAK/P,KAJT1S,MAKTuT,OAAQ6zB,EAAO7zB,OAAOb,KALb1S,MAMTynC,aAAcL,EAAOK,aAAa/0B,KANzB1S,UAUf4F,GAAI,CACF0rB,WAAY,WACV,IAEIgW,EAFStnC,KACIiO,OACAq5B,OACZA,GAAWA,EAAO3zB,SAHV3T,KAINsnC,OAAO7kB,OAJDziB,KAKNsnC,OAAO/zB,QAAO,KAEvBs2B,YAAa,WACE7pC,KACDsnC,OAAO3zB,QADN3T,KAENsnC,OAAO/zB,UAEhBA,OAAQ,WACOvT,KACDsnC,OAAO3zB,QADN3T,KAENsnC,OAAO/zB,UAEhB4a,OAAQ,WACOnuB,KACDsnC,OAAO3zB,QADN3T,KAENsnC,OAAO/zB,UAEhBsb,eAAgB,WACD7uB,KACDsnC,OAAO3zB,QADN3T,KAENsnC,OAAO/zB,UAEhBiG,cAAe,SAAuB/T,GACpC,IACIiiC,EADS1nC,KACasnC,OAAO3zB,OAC5B+zB,GACLA,EAAaluB,cAAc/T,IAE7BqkC,cAAe,WACb,IACIpC,EADS1nC,KACasnC,OAAO3zB,OAC5B+zB,GAFQ1nC,KAGFsnC,OAAOC,eAAiBG,GACjCA,EAAala,cA0CrB,YAP0B,IAAfztB,EAAO+S,MAChB/S,EAAO+S,IAAM/S,EAAO0D,MAAMqP,IAC1B/S,EAAOgT,cAAgBhT,EAAO0D,MAAMsP,eAGtChT,EAAO+S,IAAI1B,GAEJrR", "file": "swiper.min.js", "sourcesContent": ["/**\n * Swiper 4.5.0\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * http://www.idangero.us/swiper/\n *\n * Copyright 2014-2019 <PERSON>\n *\n * Released under the MIT License\n *\n * Released on: February 22, 2019\n */\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n  typeof define === 'function' && define.amd ? define(factory) :\n  (global = global || self, global.Swiper = factory());\n}(this, function () { 'use strict';\n\n  /**\n   * SSR Window 1.0.1\n   * Better handling for window object in SSR environment\n   * https://github.com/nolimits4web/ssr-window\n   *\n   * Copyright 2018, <PERSON>\n   *\n   * Licensed under MIT\n   *\n   * Released on: July 18, 2018\n   */\n  var doc = (typeof document === 'undefined') ? {\n    body: {},\n    addEventListener: function addEventListener() {},\n    removeEventListener: function removeEventListener() {},\n    activeElement: {\n      blur: function blur() {},\n      nodeName: '',\n    },\n    querySelector: function querySelector() {\n      return null;\n    },\n    querySelectorAll: function querySelectorAll() {\n      return [];\n    },\n    getElementById: function getElementById() {\n      return null;\n    },\n    createEvent: function createEvent() {\n      return {\n        initEvent: function initEvent() {},\n      };\n    },\n    createElement: function createElement() {\n      return {\n        children: [],\n        childNodes: [],\n        style: {},\n        setAttribute: function setAttribute() {},\n        getElementsByTagName: function getElementsByTagName() {\n          return [];\n        },\n      };\n    },\n    location: { hash: '' },\n  } : document; // eslint-disable-line\n\n  var win = (typeof window === 'undefined') ? {\n    document: doc,\n    navigator: {\n      userAgent: '',\n    },\n    location: {},\n    history: {},\n    CustomEvent: function CustomEvent() {\n      return this;\n    },\n    addEventListener: function addEventListener() {},\n    removeEventListener: function removeEventListener() {},\n    getComputedStyle: function getComputedStyle() {\n      return {\n        getPropertyValue: function getPropertyValue() {\n          return '';\n        },\n      };\n    },\n    Image: function Image() {},\n    Date: function Date() {},\n    screen: {},\n    setTimeout: function setTimeout() {},\n    clearTimeout: function clearTimeout() {},\n  } : window; // eslint-disable-line\n\n  /**\n   * Dom7 2.1.3\n   * Minimalistic JavaScript library for DOM manipulation, with a jQuery-compatible API\n   * http://framework7.io/docs/dom.html\n   *\n   * Copyright 2019, Vladimir Kharlampidi\n   * The iDangero.us\n   * http://www.idangero.us/\n   *\n   * Licensed under MIT\n   *\n   * Released on: February 11, 2019\n   */\n\n  var Dom7 = function Dom7(arr) {\n    var self = this;\n    // Create array-like object\n    for (var i = 0; i < arr.length; i += 1) {\n      self[i] = arr[i];\n    }\n    self.length = arr.length;\n    // Return collection with methods\n    return this;\n  };\n\n  function $(selector, context) {\n    var arr = [];\n    var i = 0;\n    if (selector && !context) {\n      if (selector instanceof Dom7) {\n        return selector;\n      }\n    }\n    if (selector) {\n        // String\n      if (typeof selector === 'string') {\n        var els;\n        var tempParent;\n        var html = selector.trim();\n        if (html.indexOf('<') >= 0 && html.indexOf('>') >= 0) {\n          var toCreate = 'div';\n          if (html.indexOf('<li') === 0) { toCreate = 'ul'; }\n          if (html.indexOf('<tr') === 0) { toCreate = 'tbody'; }\n          if (html.indexOf('<td') === 0 || html.indexOf('<th') === 0) { toCreate = 'tr'; }\n          if (html.indexOf('<tbody') === 0) { toCreate = 'table'; }\n          if (html.indexOf('<option') === 0) { toCreate = 'select'; }\n          tempParent = doc.createElement(toCreate);\n          tempParent.innerHTML = html;\n          for (i = 0; i < tempParent.childNodes.length; i += 1) {\n            arr.push(tempParent.childNodes[i]);\n          }\n        } else {\n          if (!context && selector[0] === '#' && !selector.match(/[ .<>:~]/)) {\n            // Pure ID selector\n            els = [doc.getElementById(selector.trim().split('#')[1])];\n          } else {\n            // Other selectors\n            els = (context || doc).querySelectorAll(selector.trim());\n          }\n          for (i = 0; i < els.length; i += 1) {\n            if (els[i]) { arr.push(els[i]); }\n          }\n        }\n      } else if (selector.nodeType || selector === win || selector === doc) {\n        // Node/element\n        arr.push(selector);\n      } else if (selector.length > 0 && selector[0].nodeType) {\n        // Array of elements or instance of Dom\n        for (i = 0; i < selector.length; i += 1) {\n          arr.push(selector[i]);\n        }\n      }\n    }\n    return new Dom7(arr);\n  }\n\n  $.fn = Dom7.prototype;\n  $.Class = Dom7;\n  $.Dom7 = Dom7;\n\n  function unique(arr) {\n    var uniqueArray = [];\n    for (var i = 0; i < arr.length; i += 1) {\n      if (uniqueArray.indexOf(arr[i]) === -1) { uniqueArray.push(arr[i]); }\n    }\n    return uniqueArray;\n  }\n\n  // Classes and attributes\n  function addClass(className) {\n    if (typeof className === 'undefined') {\n      return this;\n    }\n    var classes = className.split(' ');\n    for (var i = 0; i < classes.length; i += 1) {\n      for (var j = 0; j < this.length; j += 1) {\n        if (typeof this[j] !== 'undefined' && typeof this[j].classList !== 'undefined') { this[j].classList.add(classes[i]); }\n      }\n    }\n    return this;\n  }\n  function removeClass(className) {\n    var classes = className.split(' ');\n    for (var i = 0; i < classes.length; i += 1) {\n      for (var j = 0; j < this.length; j += 1) {\n        if (typeof this[j] !== 'undefined' && typeof this[j].classList !== 'undefined') { this[j].classList.remove(classes[i]); }\n      }\n    }\n    return this;\n  }\n  function hasClass(className) {\n    if (!this[0]) { return false; }\n    return this[0].classList.contains(className);\n  }\n  function toggleClass(className) {\n    var classes = className.split(' ');\n    for (var i = 0; i < classes.length; i += 1) {\n      for (var j = 0; j < this.length; j += 1) {\n        if (typeof this[j] !== 'undefined' && typeof this[j].classList !== 'undefined') { this[j].classList.toggle(classes[i]); }\n      }\n    }\n    return this;\n  }\n  function attr(attrs, value) {\n    var arguments$1 = arguments;\n\n    if (arguments.length === 1 && typeof attrs === 'string') {\n      // Get attr\n      if (this[0]) { return this[0].getAttribute(attrs); }\n      return undefined;\n    }\n\n    // Set attrs\n    for (var i = 0; i < this.length; i += 1) {\n      if (arguments$1.length === 2) {\n        // String\n        this[i].setAttribute(attrs, value);\n      } else {\n        // Object\n        // eslint-disable-next-line\n        for (var attrName in attrs) {\n          this[i][attrName] = attrs[attrName];\n          this[i].setAttribute(attrName, attrs[attrName]);\n        }\n      }\n    }\n    return this;\n  }\n  // eslint-disable-next-line\n  function removeAttr(attr) {\n    for (var i = 0; i < this.length; i += 1) {\n      this[i].removeAttribute(attr);\n    }\n    return this;\n  }\n  function data(key, value) {\n    var el;\n    if (typeof value === 'undefined') {\n      el = this[0];\n      // Get value\n      if (el) {\n        if (el.dom7ElementDataStorage && (key in el.dom7ElementDataStorage)) {\n          return el.dom7ElementDataStorage[key];\n        }\n\n        var dataKey = el.getAttribute((\"data-\" + key));\n        if (dataKey) {\n          return dataKey;\n        }\n        return undefined;\n      }\n      return undefined;\n    }\n\n    // Set value\n    for (var i = 0; i < this.length; i += 1) {\n      el = this[i];\n      if (!el.dom7ElementDataStorage) { el.dom7ElementDataStorage = {}; }\n      el.dom7ElementDataStorage[key] = value;\n    }\n    return this;\n  }\n  // Transforms\n  // eslint-disable-next-line\n  function transform(transform) {\n    for (var i = 0; i < this.length; i += 1) {\n      var elStyle = this[i].style;\n      elStyle.webkitTransform = transform;\n      elStyle.transform = transform;\n    }\n    return this;\n  }\n  function transition(duration) {\n    if (typeof duration !== 'string') {\n      duration = duration + \"ms\"; // eslint-disable-line\n    }\n    for (var i = 0; i < this.length; i += 1) {\n      var elStyle = this[i].style;\n      elStyle.webkitTransitionDuration = duration;\n      elStyle.transitionDuration = duration;\n    }\n    return this;\n  }\n  // Events\n  function on() {\n    var assign;\n\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n    var eventType = args[0];\n    var targetSelector = args[1];\n    var listener = args[2];\n    var capture = args[3];\n    if (typeof args[1] === 'function') {\n      (assign = args, eventType = assign[0], listener = assign[1], capture = assign[2]);\n      targetSelector = undefined;\n    }\n    if (!capture) { capture = false; }\n\n    function handleLiveEvent(e) {\n      var target = e.target;\n      if (!target) { return; }\n      var eventData = e.target.dom7EventData || [];\n      if (eventData.indexOf(e) < 0) {\n        eventData.unshift(e);\n      }\n      if ($(target).is(targetSelector)) { listener.apply(target, eventData); }\n      else {\n        var parents = $(target).parents(); // eslint-disable-line\n        for (var k = 0; k < parents.length; k += 1) {\n          if ($(parents[k]).is(targetSelector)) { listener.apply(parents[k], eventData); }\n        }\n      }\n    }\n    function handleEvent(e) {\n      var eventData = e && e.target ? e.target.dom7EventData || [] : [];\n      if (eventData.indexOf(e) < 0) {\n        eventData.unshift(e);\n      }\n      listener.apply(this, eventData);\n    }\n    var events = eventType.split(' ');\n    var j;\n    for (var i = 0; i < this.length; i += 1) {\n      var el = this[i];\n      if (!targetSelector) {\n        for (j = 0; j < events.length; j += 1) {\n          var event = events[j];\n          if (!el.dom7Listeners) { el.dom7Listeners = {}; }\n          if (!el.dom7Listeners[event]) { el.dom7Listeners[event] = []; }\n          el.dom7Listeners[event].push({\n            listener: listener,\n            proxyListener: handleEvent,\n          });\n          el.addEventListener(event, handleEvent, capture);\n        }\n      } else {\n        // Live events\n        for (j = 0; j < events.length; j += 1) {\n          var event$1 = events[j];\n          if (!el.dom7LiveListeners) { el.dom7LiveListeners = {}; }\n          if (!el.dom7LiveListeners[event$1]) { el.dom7LiveListeners[event$1] = []; }\n          el.dom7LiveListeners[event$1].push({\n            listener: listener,\n            proxyListener: handleLiveEvent,\n          });\n          el.addEventListener(event$1, handleLiveEvent, capture);\n        }\n      }\n    }\n    return this;\n  }\n  function off() {\n    var assign;\n\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n    var eventType = args[0];\n    var targetSelector = args[1];\n    var listener = args[2];\n    var capture = args[3];\n    if (typeof args[1] === 'function') {\n      (assign = args, eventType = assign[0], listener = assign[1], capture = assign[2]);\n      targetSelector = undefined;\n    }\n    if (!capture) { capture = false; }\n\n    var events = eventType.split(' ');\n    for (var i = 0; i < events.length; i += 1) {\n      var event = events[i];\n      for (var j = 0; j < this.length; j += 1) {\n        var el = this[j];\n        var handlers = (void 0);\n        if (!targetSelector && el.dom7Listeners) {\n          handlers = el.dom7Listeners[event];\n        } else if (targetSelector && el.dom7LiveListeners) {\n          handlers = el.dom7LiveListeners[event];\n        }\n        if (handlers && handlers.length) {\n          for (var k = handlers.length - 1; k >= 0; k -= 1) {\n            var handler = handlers[k];\n            if (listener && handler.listener === listener) {\n              el.removeEventListener(event, handler.proxyListener, capture);\n              handlers.splice(k, 1);\n            } else if (listener && handler.listener && handler.listener.dom7proxy && handler.listener.dom7proxy === listener) {\n              el.removeEventListener(event, handler.proxyListener, capture);\n              handlers.splice(k, 1);\n            } else if (!listener) {\n              el.removeEventListener(event, handler.proxyListener, capture);\n              handlers.splice(k, 1);\n            }\n          }\n        }\n      }\n    }\n    return this;\n  }\n  function trigger() {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var events = args[0].split(' ');\n    var eventData = args[1];\n    for (var i = 0; i < events.length; i += 1) {\n      var event = events[i];\n      for (var j = 0; j < this.length; j += 1) {\n        var el = this[j];\n        var evt = (void 0);\n        try {\n          evt = new win.CustomEvent(event, {\n            detail: eventData,\n            bubbles: true,\n            cancelable: true,\n          });\n        } catch (e) {\n          evt = doc.createEvent('Event');\n          evt.initEvent(event, true, true);\n          evt.detail = eventData;\n        }\n        // eslint-disable-next-line\n        el.dom7EventData = args.filter(function (data, dataIndex) { return dataIndex > 0; });\n        el.dispatchEvent(evt);\n        el.dom7EventData = [];\n        delete el.dom7EventData;\n      }\n    }\n    return this;\n  }\n  function transitionEnd(callback) {\n    var events = ['webkitTransitionEnd', 'transitionend'];\n    var dom = this;\n    var i;\n    function fireCallBack(e) {\n      /* jshint validthis:true */\n      if (e.target !== this) { return; }\n      callback.call(this, e);\n      for (i = 0; i < events.length; i += 1) {\n        dom.off(events[i], fireCallBack);\n      }\n    }\n    if (callback) {\n      for (i = 0; i < events.length; i += 1) {\n        dom.on(events[i], fireCallBack);\n      }\n    }\n    return this;\n  }\n  function outerWidth(includeMargins) {\n    if (this.length > 0) {\n      if (includeMargins) {\n        // eslint-disable-next-line\n        var styles = this.styles();\n        return this[0].offsetWidth + parseFloat(styles.getPropertyValue('margin-right')) + parseFloat(styles.getPropertyValue('margin-left'));\n      }\n      return this[0].offsetWidth;\n    }\n    return null;\n  }\n  function outerHeight(includeMargins) {\n    if (this.length > 0) {\n      if (includeMargins) {\n        // eslint-disable-next-line\n        var styles = this.styles();\n        return this[0].offsetHeight + parseFloat(styles.getPropertyValue('margin-top')) + parseFloat(styles.getPropertyValue('margin-bottom'));\n      }\n      return this[0].offsetHeight;\n    }\n    return null;\n  }\n  function offset() {\n    if (this.length > 0) {\n      var el = this[0];\n      var box = el.getBoundingClientRect();\n      var body = doc.body;\n      var clientTop = el.clientTop || body.clientTop || 0;\n      var clientLeft = el.clientLeft || body.clientLeft || 0;\n      var scrollTop = el === win ? win.scrollY : el.scrollTop;\n      var scrollLeft = el === win ? win.scrollX : el.scrollLeft;\n      return {\n        top: (box.top + scrollTop) - clientTop,\n        left: (box.left + scrollLeft) - clientLeft,\n      };\n    }\n\n    return null;\n  }\n  function styles() {\n    if (this[0]) { return win.getComputedStyle(this[0], null); }\n    return {};\n  }\n  function css(props, value) {\n    var i;\n    if (arguments.length === 1) {\n      if (typeof props === 'string') {\n        if (this[0]) { return win.getComputedStyle(this[0], null).getPropertyValue(props); }\n      } else {\n        for (i = 0; i < this.length; i += 1) {\n          // eslint-disable-next-line\n          for (var prop in props) {\n            this[i].style[prop] = props[prop];\n          }\n        }\n        return this;\n      }\n    }\n    if (arguments.length === 2 && typeof props === 'string') {\n      for (i = 0; i < this.length; i += 1) {\n        this[i].style[props] = value;\n      }\n      return this;\n    }\n    return this;\n  }\n  // Iterate over the collection passing elements to `callback`\n  function each(callback) {\n    // Don't bother continuing without a callback\n    if (!callback) { return this; }\n    // Iterate over the current collection\n    for (var i = 0; i < this.length; i += 1) {\n      // If the callback returns false\n      if (callback.call(this[i], i, this[i]) === false) {\n        // End the loop early\n        return this;\n      }\n    }\n    // Return `this` to allow chained DOM operations\n    return this;\n  }\n  // eslint-disable-next-line\n  function html(html) {\n    if (typeof html === 'undefined') {\n      return this[0] ? this[0].innerHTML : undefined;\n    }\n\n    for (var i = 0; i < this.length; i += 1) {\n      this[i].innerHTML = html;\n    }\n    return this;\n  }\n  // eslint-disable-next-line\n  function text(text) {\n    if (typeof text === 'undefined') {\n      if (this[0]) {\n        return this[0].textContent.trim();\n      }\n      return null;\n    }\n\n    for (var i = 0; i < this.length; i += 1) {\n      this[i].textContent = text;\n    }\n    return this;\n  }\n  function is(selector) {\n    var el = this[0];\n    var compareWith;\n    var i;\n    if (!el || typeof selector === 'undefined') { return false; }\n    if (typeof selector === 'string') {\n      if (el.matches) { return el.matches(selector); }\n      else if (el.webkitMatchesSelector) { return el.webkitMatchesSelector(selector); }\n      else if (el.msMatchesSelector) { return el.msMatchesSelector(selector); }\n\n      compareWith = $(selector);\n      for (i = 0; i < compareWith.length; i += 1) {\n        if (compareWith[i] === el) { return true; }\n      }\n      return false;\n    } else if (selector === doc) { return el === doc; }\n    else if (selector === win) { return el === win; }\n\n    if (selector.nodeType || selector instanceof Dom7) {\n      compareWith = selector.nodeType ? [selector] : selector;\n      for (i = 0; i < compareWith.length; i += 1) {\n        if (compareWith[i] === el) { return true; }\n      }\n      return false;\n    }\n    return false;\n  }\n  function index() {\n    var child = this[0];\n    var i;\n    if (child) {\n      i = 0;\n      // eslint-disable-next-line\n      while ((child = child.previousSibling) !== null) {\n        if (child.nodeType === 1) { i += 1; }\n      }\n      return i;\n    }\n    return undefined;\n  }\n  // eslint-disable-next-line\n  function eq(index) {\n    if (typeof index === 'undefined') { return this; }\n    var length = this.length;\n    var returnIndex;\n    if (index > length - 1) {\n      return new Dom7([]);\n    }\n    if (index < 0) {\n      returnIndex = length + index;\n      if (returnIndex < 0) { return new Dom7([]); }\n      return new Dom7([this[returnIndex]]);\n    }\n    return new Dom7([this[index]]);\n  }\n  function append() {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var newChild;\n\n    for (var k = 0; k < args.length; k += 1) {\n      newChild = args[k];\n      for (var i = 0; i < this.length; i += 1) {\n        if (typeof newChild === 'string') {\n          var tempDiv = doc.createElement('div');\n          tempDiv.innerHTML = newChild;\n          while (tempDiv.firstChild) {\n            this[i].appendChild(tempDiv.firstChild);\n          }\n        } else if (newChild instanceof Dom7) {\n          for (var j = 0; j < newChild.length; j += 1) {\n            this[i].appendChild(newChild[j]);\n          }\n        } else {\n          this[i].appendChild(newChild);\n        }\n      }\n    }\n\n    return this;\n  }\n  function prepend(newChild) {\n    var i;\n    var j;\n    for (i = 0; i < this.length; i += 1) {\n      if (typeof newChild === 'string') {\n        var tempDiv = doc.createElement('div');\n        tempDiv.innerHTML = newChild;\n        for (j = tempDiv.childNodes.length - 1; j >= 0; j -= 1) {\n          this[i].insertBefore(tempDiv.childNodes[j], this[i].childNodes[0]);\n        }\n      } else if (newChild instanceof Dom7) {\n        for (j = 0; j < newChild.length; j += 1) {\n          this[i].insertBefore(newChild[j], this[i].childNodes[0]);\n        }\n      } else {\n        this[i].insertBefore(newChild, this[i].childNodes[0]);\n      }\n    }\n    return this;\n  }\n  function next(selector) {\n    if (this.length > 0) {\n      if (selector) {\n        if (this[0].nextElementSibling && $(this[0].nextElementSibling).is(selector)) {\n          return new Dom7([this[0].nextElementSibling]);\n        }\n        return new Dom7([]);\n      }\n\n      if (this[0].nextElementSibling) { return new Dom7([this[0].nextElementSibling]); }\n      return new Dom7([]);\n    }\n    return new Dom7([]);\n  }\n  function nextAll(selector) {\n    var nextEls = [];\n    var el = this[0];\n    if (!el) { return new Dom7([]); }\n    while (el.nextElementSibling) {\n      var next = el.nextElementSibling; // eslint-disable-line\n      if (selector) {\n        if ($(next).is(selector)) { nextEls.push(next); }\n      } else { nextEls.push(next); }\n      el = next;\n    }\n    return new Dom7(nextEls);\n  }\n  function prev(selector) {\n    if (this.length > 0) {\n      var el = this[0];\n      if (selector) {\n        if (el.previousElementSibling && $(el.previousElementSibling).is(selector)) {\n          return new Dom7([el.previousElementSibling]);\n        }\n        return new Dom7([]);\n      }\n\n      if (el.previousElementSibling) { return new Dom7([el.previousElementSibling]); }\n      return new Dom7([]);\n    }\n    return new Dom7([]);\n  }\n  function prevAll(selector) {\n    var prevEls = [];\n    var el = this[0];\n    if (!el) { return new Dom7([]); }\n    while (el.previousElementSibling) {\n      var prev = el.previousElementSibling; // eslint-disable-line\n      if (selector) {\n        if ($(prev).is(selector)) { prevEls.push(prev); }\n      } else { prevEls.push(prev); }\n      el = prev;\n    }\n    return new Dom7(prevEls);\n  }\n  function parent(selector) {\n    var parents = []; // eslint-disable-line\n    for (var i = 0; i < this.length; i += 1) {\n      if (this[i].parentNode !== null) {\n        if (selector) {\n          if ($(this[i].parentNode).is(selector)) { parents.push(this[i].parentNode); }\n        } else {\n          parents.push(this[i].parentNode);\n        }\n      }\n    }\n    return $(unique(parents));\n  }\n  function parents(selector) {\n    var parents = []; // eslint-disable-line\n    for (var i = 0; i < this.length; i += 1) {\n      var parent = this[i].parentNode; // eslint-disable-line\n      while (parent) {\n        if (selector) {\n          if ($(parent).is(selector)) { parents.push(parent); }\n        } else {\n          parents.push(parent);\n        }\n        parent = parent.parentNode;\n      }\n    }\n    return $(unique(parents));\n  }\n  function closest(selector) {\n    var closest = this; // eslint-disable-line\n    if (typeof selector === 'undefined') {\n      return new Dom7([]);\n    }\n    if (!closest.is(selector)) {\n      closest = closest.parents(selector).eq(0);\n    }\n    return closest;\n  }\n  function find(selector) {\n    var foundElements = [];\n    for (var i = 0; i < this.length; i += 1) {\n      var found = this[i].querySelectorAll(selector);\n      for (var j = 0; j < found.length; j += 1) {\n        foundElements.push(found[j]);\n      }\n    }\n    return new Dom7(foundElements);\n  }\n  function children(selector) {\n    var children = []; // eslint-disable-line\n    for (var i = 0; i < this.length; i += 1) {\n      var childNodes = this[i].childNodes;\n\n      for (var j = 0; j < childNodes.length; j += 1) {\n        if (!selector) {\n          if (childNodes[j].nodeType === 1) { children.push(childNodes[j]); }\n        } else if (childNodes[j].nodeType === 1 && $(childNodes[j]).is(selector)) {\n          children.push(childNodes[j]);\n        }\n      }\n    }\n    return new Dom7(unique(children));\n  }\n  function remove() {\n    for (var i = 0; i < this.length; i += 1) {\n      if (this[i].parentNode) { this[i].parentNode.removeChild(this[i]); }\n    }\n    return this;\n  }\n  function add() {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var dom = this;\n    var i;\n    var j;\n    for (i = 0; i < args.length; i += 1) {\n      var toAdd = $(args[i]);\n      for (j = 0; j < toAdd.length; j += 1) {\n        dom[dom.length] = toAdd[j];\n        dom.length += 1;\n      }\n    }\n    return dom;\n  }\n\n  var Methods = {\n    addClass: addClass,\n    removeClass: removeClass,\n    hasClass: hasClass,\n    toggleClass: toggleClass,\n    attr: attr,\n    removeAttr: removeAttr,\n    data: data,\n    transform: transform,\n    transition: transition,\n    on: on,\n    off: off,\n    trigger: trigger,\n    transitionEnd: transitionEnd,\n    outerWidth: outerWidth,\n    outerHeight: outerHeight,\n    offset: offset,\n    css: css,\n    each: each,\n    html: html,\n    text: text,\n    is: is,\n    index: index,\n    eq: eq,\n    append: append,\n    prepend: prepend,\n    next: next,\n    nextAll: nextAll,\n    prev: prev,\n    prevAll: prevAll,\n    parent: parent,\n    parents: parents,\n    closest: closest,\n    find: find,\n    children: children,\n    remove: remove,\n    add: add,\n    styles: styles,\n  };\n\n  Object.keys(Methods).forEach(function (methodName) {\n    $.fn[methodName] = Methods[methodName];\n  });\n\n  var Utils = {\n    deleteProps: function deleteProps(obj) {\n      var object = obj;\n      Object.keys(object).forEach(function (key) {\n        try {\n          object[key] = null;\n        } catch (e) {\n          // no getter for object\n        }\n        try {\n          delete object[key];\n        } catch (e) {\n          // something got wrong\n        }\n      });\n    },\n    nextTick: function nextTick(callback, delay) {\n      if ( delay === void 0 ) delay = 0;\n\n      return setTimeout(callback, delay);\n    },\n    now: function now() {\n      return Date.now();\n    },\n    getTranslate: function getTranslate(el, axis) {\n      if ( axis === void 0 ) axis = 'x';\n\n      var matrix;\n      var curTransform;\n      var transformMatrix;\n\n      var curStyle = win.getComputedStyle(el, null);\n\n      if (win.WebKitCSSMatrix) {\n        curTransform = curStyle.transform || curStyle.webkitTransform;\n        if (curTransform.split(',').length > 6) {\n          curTransform = curTransform.split(', ').map(function (a) { return a.replace(',', '.'); }).join(', ');\n        }\n        // Some old versions of Webkit choke when 'none' is passed; pass\n        // empty string instead in this case\n        transformMatrix = new win.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n      } else {\n        transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n        matrix = transformMatrix.toString().split(',');\n      }\n\n      if (axis === 'x') {\n        // Latest Chrome and webkits Fix\n        if (win.WebKitCSSMatrix) { curTransform = transformMatrix.m41; }\n        // Crazy IE10 Matrix\n        else if (matrix.length === 16) { curTransform = parseFloat(matrix[12]); }\n        // Normal Browsers\n        else { curTransform = parseFloat(matrix[4]); }\n      }\n      if (axis === 'y') {\n        // Latest Chrome and webkits Fix\n        if (win.WebKitCSSMatrix) { curTransform = transformMatrix.m42; }\n        // Crazy IE10 Matrix\n        else if (matrix.length === 16) { curTransform = parseFloat(matrix[13]); }\n        // Normal Browsers\n        else { curTransform = parseFloat(matrix[5]); }\n      }\n      return curTransform || 0;\n    },\n    parseUrlQuery: function parseUrlQuery(url) {\n      var query = {};\n      var urlToParse = url || win.location.href;\n      var i;\n      var params;\n      var param;\n      var length;\n      if (typeof urlToParse === 'string' && urlToParse.length) {\n        urlToParse = urlToParse.indexOf('?') > -1 ? urlToParse.replace(/\\S*\\?/, '') : '';\n        params = urlToParse.split('&').filter(function (paramsPart) { return paramsPart !== ''; });\n        length = params.length;\n\n        for (i = 0; i < length; i += 1) {\n          param = params[i].replace(/#\\S+/g, '').split('=');\n          query[decodeURIComponent(param[0])] = typeof param[1] === 'undefined' ? undefined : decodeURIComponent(param[1]) || '';\n        }\n      }\n      return query;\n    },\n    isObject: function isObject(o) {\n      return typeof o === 'object' && o !== null && o.constructor && o.constructor === Object;\n    },\n    extend: function extend() {\n      var args = [], len$1 = arguments.length;\n      while ( len$1-- ) args[ len$1 ] = arguments[ len$1 ];\n\n      var to = Object(args[0]);\n      for (var i = 1; i < args.length; i += 1) {\n        var nextSource = args[i];\n        if (nextSource !== undefined && nextSource !== null) {\n          var keysArray = Object.keys(Object(nextSource));\n          for (var nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n            var nextKey = keysArray[nextIndex];\n            var desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n            if (desc !== undefined && desc.enumerable) {\n              if (Utils.isObject(to[nextKey]) && Utils.isObject(nextSource[nextKey])) {\n                Utils.extend(to[nextKey], nextSource[nextKey]);\n              } else if (!Utils.isObject(to[nextKey]) && Utils.isObject(nextSource[nextKey])) {\n                to[nextKey] = {};\n                Utils.extend(to[nextKey], nextSource[nextKey]);\n              } else {\n                to[nextKey] = nextSource[nextKey];\n              }\n            }\n          }\n        }\n      }\n      return to;\n    },\n  };\n\n  var Support = (function Support() {\n    var testDiv = doc.createElement('div');\n    return {\n      touch: (win.Modernizr && win.Modernizr.touch === true) || (function checkTouch() {\n        return !!((win.navigator.maxTouchPoints > 0) || ('ontouchstart' in win) || (win.DocumentTouch && doc instanceof win.DocumentTouch));\n      }()),\n\n      pointerEvents: !!(win.navigator.pointerEnabled || win.PointerEvent || ('maxTouchPoints' in win.navigator && win.navigator.maxTouchPoints > 0)),\n      prefixedPointerEvents: !!win.navigator.msPointerEnabled,\n\n      transition: (function checkTransition() {\n        var style = testDiv.style;\n        return ('transition' in style || 'webkitTransition' in style || 'MozTransition' in style);\n      }()),\n      transforms3d: (win.Modernizr && win.Modernizr.csstransforms3d === true) || (function checkTransforms3d() {\n        var style = testDiv.style;\n        return ('webkitPerspective' in style || 'MozPerspective' in style || 'OPerspective' in style || 'MsPerspective' in style || 'perspective' in style);\n      }()),\n\n      flexbox: (function checkFlexbox() {\n        var style = testDiv.style;\n        var styles = ('alignItems webkitAlignItems webkitBoxAlign msFlexAlign mozBoxAlign webkitFlexDirection msFlexDirection mozBoxDirection mozBoxOrient webkitBoxDirection webkitBoxOrient').split(' ');\n        for (var i = 0; i < styles.length; i += 1) {\n          if (styles[i] in style) { return true; }\n        }\n        return false;\n      }()),\n\n      observer: (function checkObserver() {\n        return ('MutationObserver' in win || 'WebkitMutationObserver' in win);\n      }()),\n\n      passiveListener: (function checkPassiveListener() {\n        var supportsPassive = false;\n        try {\n          var opts = Object.defineProperty({}, 'passive', {\n            // eslint-disable-next-line\n            get: function get() {\n              supportsPassive = true;\n            },\n          });\n          win.addEventListener('testPassiveListener', null, opts);\n        } catch (e) {\n          // No support\n        }\n        return supportsPassive;\n      }()),\n\n      gestures: (function checkGestures() {\n        return 'ongesturestart' in win;\n      }()),\n    };\n  }());\n\n  var Browser = (function Browser() {\n    function isSafari() {\n      var ua = win.navigator.userAgent.toLowerCase();\n      return (ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0);\n    }\n    return {\n      isIE: !!win.navigator.userAgent.match(/Trident/g) || !!win.navigator.userAgent.match(/MSIE/g),\n      isEdge: !!win.navigator.userAgent.match(/Edge/g),\n      isSafari: isSafari(),\n      isUiWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(win.navigator.userAgent),\n    };\n  }());\n\n  var SwiperClass = function SwiperClass(params) {\n    if ( params === void 0 ) params = {};\n\n    var self = this;\n    self.params = params;\n\n    // Events\n    self.eventsListeners = {};\n\n    if (self.params && self.params.on) {\n      Object.keys(self.params.on).forEach(function (eventName) {\n        self.on(eventName, self.params.on[eventName]);\n      });\n    }\n  };\n\n  var staticAccessors = { components: { configurable: true } };\n\n  SwiperClass.prototype.on = function on (events, handler, priority) {\n    var self = this;\n    if (typeof handler !== 'function') { return self; }\n    var method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(function (event) {\n      if (!self.eventsListeners[event]) { self.eventsListeners[event] = []; }\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  };\n\n  SwiperClass.prototype.once = function once (events, handler, priority) {\n    var self = this;\n    if (typeof handler !== 'function') { return self; }\n    function onceHandler() {\n        var args = [], len = arguments.length;\n        while ( len-- ) args[ len ] = arguments[ len ];\n\n      handler.apply(self, args);\n      self.off(events, onceHandler);\n      if (onceHandler.f7proxy) {\n        delete onceHandler.f7proxy;\n      }\n    }\n    onceHandler.f7proxy = handler;\n    return self.on(events, onceHandler, priority);\n  };\n\n  SwiperClass.prototype.off = function off (events, handler) {\n    var self = this;\n    if (!self.eventsListeners) { return self; }\n    events.split(' ').forEach(function (event) {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event] && self.eventsListeners[event].length) {\n        self.eventsListeners[event].forEach(function (eventHandler, index) {\n          if (eventHandler === handler || (eventHandler.f7proxy && eventHandler.f7proxy === handler)) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  };\n\n  SwiperClass.prototype.emit = function emit () {\n      var args = [], len = arguments.length;\n      while ( len-- ) args[ len ] = arguments[ len ];\n\n    var self = this;\n    if (!self.eventsListeners) { return self; }\n    var events;\n    var data;\n    var context;\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    var eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(function (event) {\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        var handlers = [];\n        self.eventsListeners[event].forEach(function (eventHandler) {\n          handlers.push(eventHandler);\n        });\n        handlers.forEach(function (eventHandler) {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  };\n\n  SwiperClass.prototype.useModulesParams = function useModulesParams (instanceParams) {\n    var instance = this;\n    if (!instance.modules) { return; }\n    Object.keys(instance.modules).forEach(function (moduleName) {\n      var module = instance.modules[moduleName];\n      // Extend params\n      if (module.params) {\n        Utils.extend(instanceParams, module.params);\n      }\n    });\n  };\n\n  SwiperClass.prototype.useModules = function useModules (modulesParams) {\n      if ( modulesParams === void 0 ) modulesParams = {};\n\n    var instance = this;\n    if (!instance.modules) { return; }\n    Object.keys(instance.modules).forEach(function (moduleName) {\n      var module = instance.modules[moduleName];\n      var moduleParams = modulesParams[moduleName] || {};\n      // Extend instance methods and props\n      if (module.instance) {\n        Object.keys(module.instance).forEach(function (modulePropName) {\n          var moduleProp = module.instance[modulePropName];\n          if (typeof moduleProp === 'function') {\n            instance[modulePropName] = moduleProp.bind(instance);\n          } else {\n            instance[modulePropName] = moduleProp;\n          }\n        });\n      }\n      // Add event listeners\n      if (module.on && instance.on) {\n        Object.keys(module.on).forEach(function (moduleEventName) {\n          instance.on(moduleEventName, module.on[moduleEventName]);\n        });\n      }\n\n      // Module create callback\n      if (module.create) {\n        module.create.bind(instance)(moduleParams);\n      }\n    });\n  };\n\n  staticAccessors.components.set = function (components) {\n    var Class = this;\n    if (!Class.use) { return; }\n    Class.use(components);\n  };\n\n  SwiperClass.installModule = function installModule (module) {\n      var params = [], len = arguments.length - 1;\n      while ( len-- > 0 ) params[ len ] = arguments[ len + 1 ];\n\n    var Class = this;\n    if (!Class.prototype.modules) { Class.prototype.modules = {}; }\n    var name = module.name || (((Object.keys(Class.prototype.modules).length) + \"_\" + (Utils.now())));\n    Class.prototype.modules[name] = module;\n    // Prototype\n    if (module.proto) {\n      Object.keys(module.proto).forEach(function (key) {\n        Class.prototype[key] = module.proto[key];\n      });\n    }\n    // Class\n    if (module.static) {\n      Object.keys(module.static).forEach(function (key) {\n        Class[key] = module.static[key];\n      });\n    }\n    // Callback\n    if (module.install) {\n      module.install.apply(Class, params);\n    }\n    return Class;\n  };\n\n  SwiperClass.use = function use (module) {\n      var params = [], len = arguments.length - 1;\n      while ( len-- > 0 ) params[ len ] = arguments[ len + 1 ];\n\n    var Class = this;\n    if (Array.isArray(module)) {\n      module.forEach(function (m) { return Class.installModule(m); });\n      return Class;\n    }\n    return Class.installModule.apply(Class, [ module ].concat( params ));\n  };\n\n  Object.defineProperties( SwiperClass, staticAccessors );\n\n  function updateSize () {\n    var swiper = this;\n    var width;\n    var height;\n    var $el = swiper.$el;\n    if (typeof swiper.params.width !== 'undefined') {\n      width = swiper.params.width;\n    } else {\n      width = $el[0].clientWidth;\n    }\n    if (typeof swiper.params.height !== 'undefined') {\n      height = swiper.params.height;\n    } else {\n      height = $el[0].clientHeight;\n    }\n    if ((width === 0 && swiper.isHorizontal()) || (height === 0 && swiper.isVertical())) {\n      return;\n    }\n\n    // Subtract paddings\n    width = width - parseInt($el.css('padding-left'), 10) - parseInt($el.css('padding-right'), 10);\n    height = height - parseInt($el.css('padding-top'), 10) - parseInt($el.css('padding-bottom'), 10);\n\n    Utils.extend(swiper, {\n      width: width,\n      height: height,\n      size: swiper.isHorizontal() ? width : height,\n    });\n  }\n\n  function updateSlides () {\n    var swiper = this;\n    var params = swiper.params;\n\n    var $wrapperEl = swiper.$wrapperEl;\n    var swiperSize = swiper.size;\n    var rtl = swiper.rtlTranslate;\n    var wrongRTL = swiper.wrongRTL;\n    var isVirtual = swiper.virtual && params.virtual.enabled;\n    var previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n    var slides = $wrapperEl.children((\".\" + (swiper.params.slideClass)));\n    var slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n    var snapGrid = [];\n    var slidesGrid = [];\n    var slidesSizesGrid = [];\n\n    var offsetBefore = params.slidesOffsetBefore;\n    if (typeof offsetBefore === 'function') {\n      offsetBefore = params.slidesOffsetBefore.call(swiper);\n    }\n\n    var offsetAfter = params.slidesOffsetAfter;\n    if (typeof offsetAfter === 'function') {\n      offsetAfter = params.slidesOffsetAfter.call(swiper);\n    }\n\n    var previousSnapGridLength = swiper.snapGrid.length;\n    var previousSlidesGridLength = swiper.snapGrid.length;\n\n    var spaceBetween = params.spaceBetween;\n    var slidePosition = -offsetBefore;\n    var prevSlideSize = 0;\n    var index = 0;\n    if (typeof swiperSize === 'undefined') {\n      return;\n    }\n    if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n      spaceBetween = (parseFloat(spaceBetween.replace('%', '')) / 100) * swiperSize;\n    }\n\n    swiper.virtualSize = -spaceBetween;\n\n    // reset margins\n    if (rtl) { slides.css({ marginLeft: '', marginTop: '' }); }\n    else { slides.css({ marginRight: '', marginBottom: '' }); }\n\n    var slidesNumberEvenToRows;\n    if (params.slidesPerColumn > 1) {\n      if (Math.floor(slidesLength / params.slidesPerColumn) === slidesLength / swiper.params.slidesPerColumn) {\n        slidesNumberEvenToRows = slidesLength;\n      } else {\n        slidesNumberEvenToRows = Math.ceil(slidesLength / params.slidesPerColumn) * params.slidesPerColumn;\n      }\n      if (params.slidesPerView !== 'auto' && params.slidesPerColumnFill === 'row') {\n        slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, params.slidesPerView * params.slidesPerColumn);\n      }\n    }\n\n    // Calc slides\n    var slideSize;\n    var slidesPerColumn = params.slidesPerColumn;\n    var slidesPerRow = slidesNumberEvenToRows / slidesPerColumn;\n    var numFullColumns = Math.floor(slidesLength / params.slidesPerColumn);\n    for (var i = 0; i < slidesLength; i += 1) {\n      slideSize = 0;\n      var slide = slides.eq(i);\n      if (params.slidesPerColumn > 1) {\n        // Set slides order\n        var newSlideOrderIndex = (void 0);\n        var column = (void 0);\n        var row = (void 0);\n        if (params.slidesPerColumnFill === 'column') {\n          column = Math.floor(i / slidesPerColumn);\n          row = i - (column * slidesPerColumn);\n          if (column > numFullColumns || (column === numFullColumns && row === slidesPerColumn - 1)) {\n            row += 1;\n            if (row >= slidesPerColumn) {\n              row = 0;\n              column += 1;\n            }\n          }\n          newSlideOrderIndex = column + ((row * slidesNumberEvenToRows) / slidesPerColumn);\n          slide\n            .css({\n              '-webkit-box-ordinal-group': newSlideOrderIndex,\n              '-moz-box-ordinal-group': newSlideOrderIndex,\n              '-ms-flex-order': newSlideOrderIndex,\n              '-webkit-order': newSlideOrderIndex,\n              order: newSlideOrderIndex,\n            });\n        } else {\n          row = Math.floor(i / slidesPerRow);\n          column = i - (row * slidesPerRow);\n        }\n        slide\n          .css(\n            (\"margin-\" + (swiper.isHorizontal() ? 'top' : 'left')),\n            (row !== 0 && params.spaceBetween) && (((params.spaceBetween) + \"px\"))\n          )\n          .attr('data-swiper-column', column)\n          .attr('data-swiper-row', row);\n      }\n      if (slide.css('display') === 'none') { continue; } // eslint-disable-line\n\n      if (params.slidesPerView === 'auto') {\n        var slideStyles = win.getComputedStyle(slide[0], null);\n        var currentTransform = slide[0].style.transform;\n        var currentWebKitTransform = slide[0].style.webkitTransform;\n        if (currentTransform) {\n          slide[0].style.transform = 'none';\n        }\n        if (currentWebKitTransform) {\n          slide[0].style.webkitTransform = 'none';\n        }\n        if (params.roundLengths) {\n          slideSize = swiper.isHorizontal()\n            ? slide.outerWidth(true)\n            : slide.outerHeight(true);\n        } else {\n          // eslint-disable-next-line\n          if (swiper.isHorizontal()) {\n            var width = parseFloat(slideStyles.getPropertyValue('width'));\n            var paddingLeft = parseFloat(slideStyles.getPropertyValue('padding-left'));\n            var paddingRight = parseFloat(slideStyles.getPropertyValue('padding-right'));\n            var marginLeft = parseFloat(slideStyles.getPropertyValue('margin-left'));\n            var marginRight = parseFloat(slideStyles.getPropertyValue('margin-right'));\n            var boxSizing = slideStyles.getPropertyValue('box-sizing');\n            if (boxSizing && boxSizing === 'border-box') {\n              slideSize = width + marginLeft + marginRight;\n            } else {\n              slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight;\n            }\n          } else {\n            var height = parseFloat(slideStyles.getPropertyValue('height'));\n            var paddingTop = parseFloat(slideStyles.getPropertyValue('padding-top'));\n            var paddingBottom = parseFloat(slideStyles.getPropertyValue('padding-bottom'));\n            var marginTop = parseFloat(slideStyles.getPropertyValue('margin-top'));\n            var marginBottom = parseFloat(slideStyles.getPropertyValue('margin-bottom'));\n            var boxSizing$1 = slideStyles.getPropertyValue('box-sizing');\n            if (boxSizing$1 && boxSizing$1 === 'border-box') {\n              slideSize = height + marginTop + marginBottom;\n            } else {\n              slideSize = height + paddingTop + paddingBottom + marginTop + marginBottom;\n            }\n          }\n        }\n        if (currentTransform) {\n          slide[0].style.transform = currentTransform;\n        }\n        if (currentWebKitTransform) {\n          slide[0].style.webkitTransform = currentWebKitTransform;\n        }\n        if (params.roundLengths) { slideSize = Math.floor(slideSize); }\n      } else {\n        slideSize = (swiperSize - ((params.slidesPerView - 1) * spaceBetween)) / params.slidesPerView;\n        if (params.roundLengths) { slideSize = Math.floor(slideSize); }\n\n        if (slides[i]) {\n          if (swiper.isHorizontal()) {\n            slides[i].style.width = slideSize + \"px\";\n          } else {\n            slides[i].style.height = slideSize + \"px\";\n          }\n        }\n      }\n      if (slides[i]) {\n        slides[i].swiperSlideSize = slideSize;\n      }\n      slidesSizesGrid.push(slideSize);\n\n\n      if (params.centeredSlides) {\n        slidePosition = slidePosition + (slideSize / 2) + (prevSlideSize / 2) + spaceBetween;\n        if (prevSlideSize === 0 && i !== 0) { slidePosition = slidePosition - (swiperSize / 2) - spaceBetween; }\n        if (i === 0) { slidePosition = slidePosition - (swiperSize / 2) - spaceBetween; }\n        if (Math.abs(slidePosition) < 1 / 1000) { slidePosition = 0; }\n        if (params.roundLengths) { slidePosition = Math.floor(slidePosition); }\n        if ((index) % params.slidesPerGroup === 0) { snapGrid.push(slidePosition); }\n        slidesGrid.push(slidePosition);\n      } else {\n        if (params.roundLengths) { slidePosition = Math.floor(slidePosition); }\n        if ((index) % params.slidesPerGroup === 0) { snapGrid.push(slidePosition); }\n        slidesGrid.push(slidePosition);\n        slidePosition = slidePosition + slideSize + spaceBetween;\n      }\n\n      swiper.virtualSize += slideSize + spaceBetween;\n\n      prevSlideSize = slideSize;\n\n      index += 1;\n    }\n    swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n    var newSlidesGrid;\n\n    if (\n      rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n      $wrapperEl.css({ width: ((swiper.virtualSize + params.spaceBetween) + \"px\") });\n    }\n    if (!Support.flexbox || params.setWrapperSize) {\n      if (swiper.isHorizontal()) { $wrapperEl.css({ width: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n      else { $wrapperEl.css({ height: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n    }\n\n    if (params.slidesPerColumn > 1) {\n      swiper.virtualSize = (slideSize + params.spaceBetween) * slidesNumberEvenToRows;\n      swiper.virtualSize = Math.ceil(swiper.virtualSize / params.slidesPerColumn) - params.spaceBetween;\n      if (swiper.isHorizontal()) { $wrapperEl.css({ width: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n      else { $wrapperEl.css({ height: ((swiper.virtualSize + params.spaceBetween) + \"px\") }); }\n      if (params.centeredSlides) {\n        newSlidesGrid = [];\n        for (var i$1 = 0; i$1 < snapGrid.length; i$1 += 1) {\n          var slidesGridItem = snapGrid[i$1];\n          if (params.roundLengths) { slidesGridItem = Math.floor(slidesGridItem); }\n          if (snapGrid[i$1] < swiper.virtualSize + snapGrid[0]) { newSlidesGrid.push(slidesGridItem); }\n        }\n        snapGrid = newSlidesGrid;\n      }\n    }\n\n    // Remove last grid elements depending on width\n    if (!params.centeredSlides) {\n      newSlidesGrid = [];\n      for (var i$2 = 0; i$2 < snapGrid.length; i$2 += 1) {\n        var slidesGridItem$1 = snapGrid[i$2];\n        if (params.roundLengths) { slidesGridItem$1 = Math.floor(slidesGridItem$1); }\n        if (snapGrid[i$2] <= swiper.virtualSize - swiperSize) {\n          newSlidesGrid.push(slidesGridItem$1);\n        }\n      }\n      snapGrid = newSlidesGrid;\n      if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n        snapGrid.push(swiper.virtualSize - swiperSize);\n      }\n    }\n    if (snapGrid.length === 0) { snapGrid = [0]; }\n\n    if (params.spaceBetween !== 0) {\n      if (swiper.isHorizontal()) {\n        if (rtl) { slides.css({ marginLeft: (spaceBetween + \"px\") }); }\n        else { slides.css({ marginRight: (spaceBetween + \"px\") }); }\n      } else { slides.css({ marginBottom: (spaceBetween + \"px\") }); }\n    }\n\n    if (params.centerInsufficientSlides) {\n      var allSlidesSize = 0;\n      slidesSizesGrid.forEach(function (slideSizeValue) {\n        allSlidesSize += slideSizeValue + (params.spaceBetween ? params.spaceBetween : 0);\n      });\n      allSlidesSize -= params.spaceBetween;\n      if (allSlidesSize < swiperSize) {\n        var allSlidesOffset = (swiperSize - allSlidesSize) / 2;\n        snapGrid.forEach(function (snap, snapIndex) {\n          snapGrid[snapIndex] = snap - allSlidesOffset;\n        });\n        slidesGrid.forEach(function (snap, snapIndex) {\n          slidesGrid[snapIndex] = snap + allSlidesOffset;\n        });\n      }\n    }\n\n    Utils.extend(swiper, {\n      slides: slides,\n      snapGrid: snapGrid,\n      slidesGrid: slidesGrid,\n      slidesSizesGrid: slidesSizesGrid,\n    });\n\n    if (slidesLength !== previousSlidesLength) {\n      swiper.emit('slidesLengthChange');\n    }\n    if (snapGrid.length !== previousSnapGridLength) {\n      if (swiper.params.watchOverflow) { swiper.checkOverflow(); }\n      swiper.emit('snapGridLengthChange');\n    }\n    if (slidesGrid.length !== previousSlidesGridLength) {\n      swiper.emit('slidesGridLengthChange');\n    }\n\n    if (params.watchSlidesProgress || params.watchSlidesVisibility) {\n      swiper.updateSlidesOffset();\n    }\n  }\n\n  function updateAutoHeight (speed) {\n    var swiper = this;\n    var activeSlides = [];\n    var newHeight = 0;\n    var i;\n    if (typeof speed === 'number') {\n      swiper.setTransition(speed);\n    } else if (speed === true) {\n      swiper.setTransition(swiper.params.speed);\n    }\n    // Find slides currently in view\n    if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        var index = swiper.activeIndex + i;\n        if (index > swiper.slides.length) { break; }\n        activeSlides.push(swiper.slides.eq(index)[0]);\n      }\n    } else {\n      activeSlides.push(swiper.slides.eq(swiper.activeIndex)[0]);\n    }\n\n    // Find new height from highest slide in view\n    for (i = 0; i < activeSlides.length; i += 1) {\n      if (typeof activeSlides[i] !== 'undefined') {\n        var height = activeSlides[i].offsetHeight;\n        newHeight = height > newHeight ? height : newHeight;\n      }\n    }\n\n    // Update Height\n    if (newHeight) { swiper.$wrapperEl.css('height', (newHeight + \"px\")); }\n  }\n\n  function updateSlidesOffset () {\n    var swiper = this;\n    var slides = swiper.slides;\n    for (var i = 0; i < slides.length; i += 1) {\n      slides[i].swiperSlideOffset = swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop;\n    }\n  }\n\n  function updateSlidesProgress (translate) {\n    if ( translate === void 0 ) translate = (this && this.translate) || 0;\n\n    var swiper = this;\n    var params = swiper.params;\n\n    var slides = swiper.slides;\n    var rtl = swiper.rtlTranslate;\n\n    if (slides.length === 0) { return; }\n    if (typeof slides[0].swiperSlideOffset === 'undefined') { swiper.updateSlidesOffset(); }\n\n    var offsetCenter = -translate;\n    if (rtl) { offsetCenter = translate; }\n\n    // Visible Slides\n    slides.removeClass(params.slideVisibleClass);\n\n    swiper.visibleSlidesIndexes = [];\n    swiper.visibleSlides = [];\n\n    for (var i = 0; i < slides.length; i += 1) {\n      var slide = slides[i];\n      var slideProgress = (\n        (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0)) - slide.swiperSlideOffset\n      ) / (slide.swiperSlideSize + params.spaceBetween);\n      if (params.watchSlidesVisibility) {\n        var slideBefore = -(offsetCenter - slide.swiperSlideOffset);\n        var slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n        var isVisible = (slideBefore >= 0 && slideBefore < swiper.size)\n                  || (slideAfter > 0 && slideAfter <= swiper.size)\n                  || (slideBefore <= 0 && slideAfter >= swiper.size);\n        if (isVisible) {\n          swiper.visibleSlides.push(slide);\n          swiper.visibleSlidesIndexes.push(i);\n          slides.eq(i).addClass(params.slideVisibleClass);\n        }\n      }\n      slide.progress = rtl ? -slideProgress : slideProgress;\n    }\n    swiper.visibleSlides = $(swiper.visibleSlides);\n  }\n\n  function updateProgress (translate) {\n    if ( translate === void 0 ) translate = (this && this.translate) || 0;\n\n    var swiper = this;\n    var params = swiper.params;\n\n    var translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n    var progress = swiper.progress;\n    var isBeginning = swiper.isBeginning;\n    var isEnd = swiper.isEnd;\n    var wasBeginning = isBeginning;\n    var wasEnd = isEnd;\n    if (translatesDiff === 0) {\n      progress = 0;\n      isBeginning = true;\n      isEnd = true;\n    } else {\n      progress = (translate - swiper.minTranslate()) / (translatesDiff);\n      isBeginning = progress <= 0;\n      isEnd = progress >= 1;\n    }\n    Utils.extend(swiper, {\n      progress: progress,\n      isBeginning: isBeginning,\n      isEnd: isEnd,\n    });\n\n    if (params.watchSlidesProgress || params.watchSlidesVisibility) { swiper.updateSlidesProgress(translate); }\n\n    if (isBeginning && !wasBeginning) {\n      swiper.emit('reachBeginning toEdge');\n    }\n    if (isEnd && !wasEnd) {\n      swiper.emit('reachEnd toEdge');\n    }\n    if ((wasBeginning && !isBeginning) || (wasEnd && !isEnd)) {\n      swiper.emit('fromEdge');\n    }\n\n    swiper.emit('progress', progress);\n  }\n\n  function updateSlidesClasses () {\n    var swiper = this;\n\n    var slides = swiper.slides;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var activeIndex = swiper.activeIndex;\n    var realIndex = swiper.realIndex;\n    var isVirtual = swiper.virtual && params.virtual.enabled;\n\n    slides.removeClass(((params.slideActiveClass) + \" \" + (params.slideNextClass) + \" \" + (params.slidePrevClass) + \" \" + (params.slideDuplicateActiveClass) + \" \" + (params.slideDuplicateNextClass) + \" \" + (params.slideDuplicatePrevClass)));\n\n    var activeSlide;\n    if (isVirtual) {\n      activeSlide = swiper.$wrapperEl.find((\".\" + (params.slideClass) + \"[data-swiper-slide-index=\\\"\" + activeIndex + \"\\\"]\"));\n    } else {\n      activeSlide = slides.eq(activeIndex);\n    }\n\n    // Active classes\n    activeSlide.addClass(params.slideActiveClass);\n\n    if (params.loop) {\n      // Duplicate to all looped slides\n      if (activeSlide.hasClass(params.slideDuplicateClass)) {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \":not(.\" + (params.slideDuplicateClass) + \")[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]\"))\n          .addClass(params.slideDuplicateActiveClass);\n      } else {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]\"))\n          .addClass(params.slideDuplicateActiveClass);\n      }\n    }\n    // Next Slide\n    var nextSlide = activeSlide.nextAll((\".\" + (params.slideClass))).eq(0).addClass(params.slideNextClass);\n    if (params.loop && nextSlide.length === 0) {\n      nextSlide = slides.eq(0);\n      nextSlide.addClass(params.slideNextClass);\n    }\n    // Prev Slide\n    var prevSlide = activeSlide.prevAll((\".\" + (params.slideClass))).eq(0).addClass(params.slidePrevClass);\n    if (params.loop && prevSlide.length === 0) {\n      prevSlide = slides.eq(-1);\n      prevSlide.addClass(params.slidePrevClass);\n    }\n    if (params.loop) {\n      // Duplicate to all looped slides\n      if (nextSlide.hasClass(params.slideDuplicateClass)) {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \":not(.\" + (params.slideDuplicateClass) + \")[data-swiper-slide-index=\\\"\" + (nextSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicateNextClass);\n      } else {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + (nextSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicateNextClass);\n      }\n      if (prevSlide.hasClass(params.slideDuplicateClass)) {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \":not(.\" + (params.slideDuplicateClass) + \")[data-swiper-slide-index=\\\"\" + (prevSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicatePrevClass);\n      } else {\n        $wrapperEl\n          .children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + (prevSlide.attr('data-swiper-slide-index')) + \"\\\"]\"))\n          .addClass(params.slideDuplicatePrevClass);\n      }\n    }\n  }\n\n  function updateActiveIndex (newActiveIndex) {\n    var swiper = this;\n    var translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n    var slidesGrid = swiper.slidesGrid;\n    var snapGrid = swiper.snapGrid;\n    var params = swiper.params;\n    var previousIndex = swiper.activeIndex;\n    var previousRealIndex = swiper.realIndex;\n    var previousSnapIndex = swiper.snapIndex;\n    var activeIndex = newActiveIndex;\n    var snapIndex;\n    if (typeof activeIndex === 'undefined') {\n      for (var i = 0; i < slidesGrid.length; i += 1) {\n        if (typeof slidesGrid[i + 1] !== 'undefined') {\n          if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - ((slidesGrid[i + 1] - slidesGrid[i]) / 2)) {\n            activeIndex = i;\n          } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n            activeIndex = i + 1;\n          }\n        } else if (translate >= slidesGrid[i]) {\n          activeIndex = i;\n        }\n      }\n      // Normalize slideIndex\n      if (params.normalizeSlideIndex) {\n        if (activeIndex < 0 || typeof activeIndex === 'undefined') { activeIndex = 0; }\n      }\n    }\n    if (snapGrid.indexOf(translate) >= 0) {\n      snapIndex = snapGrid.indexOf(translate);\n    } else {\n      snapIndex = Math.floor(activeIndex / params.slidesPerGroup);\n    }\n    if (snapIndex >= snapGrid.length) { snapIndex = snapGrid.length - 1; }\n    if (activeIndex === previousIndex) {\n      if (snapIndex !== previousSnapIndex) {\n        swiper.snapIndex = snapIndex;\n        swiper.emit('snapIndexChange');\n      }\n      return;\n    }\n\n    // Get real index\n    var realIndex = parseInt(swiper.slides.eq(activeIndex).attr('data-swiper-slide-index') || activeIndex, 10);\n\n    Utils.extend(swiper, {\n      snapIndex: snapIndex,\n      realIndex: realIndex,\n      previousIndex: previousIndex,\n      activeIndex: activeIndex,\n    });\n    swiper.emit('activeIndexChange');\n    swiper.emit('snapIndexChange');\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n\n  function updateClickedSlide (e) {\n    var swiper = this;\n    var params = swiper.params;\n    var slide = $(e.target).closest((\".\" + (params.slideClass)))[0];\n    var slideFound = false;\n    if (slide) {\n      for (var i = 0; i < swiper.slides.length; i += 1) {\n        if (swiper.slides[i] === slide) { slideFound = true; }\n      }\n    }\n\n    if (slide && slideFound) {\n      swiper.clickedSlide = slide;\n      if (swiper.virtual && swiper.params.virtual.enabled) {\n        swiper.clickedIndex = parseInt($(slide).attr('data-swiper-slide-index'), 10);\n      } else {\n        swiper.clickedIndex = $(slide).index();\n      }\n    } else {\n      swiper.clickedSlide = undefined;\n      swiper.clickedIndex = undefined;\n      return;\n    }\n    if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n      swiper.slideToClickedSlide();\n    }\n  }\n\n  var update = {\n    updateSize: updateSize,\n    updateSlides: updateSlides,\n    updateAutoHeight: updateAutoHeight,\n    updateSlidesOffset: updateSlidesOffset,\n    updateSlidesProgress: updateSlidesProgress,\n    updateProgress: updateProgress,\n    updateSlidesClasses: updateSlidesClasses,\n    updateActiveIndex: updateActiveIndex,\n    updateClickedSlide: updateClickedSlide,\n  };\n\n  function getTranslate (axis) {\n    if ( axis === void 0 ) axis = this.isHorizontal() ? 'x' : 'y';\n\n    var swiper = this;\n\n    var params = swiper.params;\n    var rtl = swiper.rtlTranslate;\n    var translate = swiper.translate;\n    var $wrapperEl = swiper.$wrapperEl;\n\n    if (params.virtualTranslate) {\n      return rtl ? -translate : translate;\n    }\n\n    var currentTranslate = Utils.getTranslate($wrapperEl[0], axis);\n    if (rtl) { currentTranslate = -currentTranslate; }\n\n    return currentTranslate || 0;\n  }\n\n  function setTranslate (translate, byController) {\n    var swiper = this;\n    var rtl = swiper.rtlTranslate;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var progress = swiper.progress;\n    var x = 0;\n    var y = 0;\n    var z = 0;\n\n    if (swiper.isHorizontal()) {\n      x = rtl ? -translate : translate;\n    } else {\n      y = translate;\n    }\n\n    if (params.roundLengths) {\n      x = Math.floor(x);\n      y = Math.floor(y);\n    }\n\n    if (!params.virtualTranslate) {\n      if (Support.transforms3d) { $wrapperEl.transform((\"translate3d(\" + x + \"px, \" + y + \"px, \" + z + \"px)\")); }\n      else { $wrapperEl.transform((\"translate(\" + x + \"px, \" + y + \"px)\")); }\n    }\n    swiper.previousTranslate = swiper.translate;\n    swiper.translate = swiper.isHorizontal() ? x : y;\n\n    // Check if we need to update progress\n    var newProgress;\n    var translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n    if (translatesDiff === 0) {\n      newProgress = 0;\n    } else {\n      newProgress = (translate - swiper.minTranslate()) / (translatesDiff);\n    }\n    if (newProgress !== progress) {\n      swiper.updateProgress(translate);\n    }\n\n    swiper.emit('setTranslate', swiper.translate, byController);\n  }\n\n  function minTranslate () {\n    return (-this.snapGrid[0]);\n  }\n\n  function maxTranslate () {\n    return (-this.snapGrid[this.snapGrid.length - 1]);\n  }\n\n  var translate = {\n    getTranslate: getTranslate,\n    setTranslate: setTranslate,\n    minTranslate: minTranslate,\n    maxTranslate: maxTranslate,\n  };\n\n  function setTransition (duration, byController) {\n    var swiper = this;\n\n    swiper.$wrapperEl.transition(duration);\n\n    swiper.emit('setTransition', duration, byController);\n  }\n\n  function transitionStart (runCallbacks, direction) {\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var activeIndex = swiper.activeIndex;\n    var params = swiper.params;\n    var previousIndex = swiper.previousIndex;\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n\n    var dir = direction;\n    if (!dir) {\n      if (activeIndex > previousIndex) { dir = 'next'; }\n      else if (activeIndex < previousIndex) { dir = 'prev'; }\n      else { dir = 'reset'; }\n    }\n\n    swiper.emit('transitionStart');\n\n    if (runCallbacks && activeIndex !== previousIndex) {\n      if (dir === 'reset') {\n        swiper.emit('slideResetTransitionStart');\n        return;\n      }\n      swiper.emit('slideChangeTransitionStart');\n      if (dir === 'next') {\n        swiper.emit('slideNextTransitionStart');\n      } else {\n        swiper.emit('slidePrevTransitionStart');\n      }\n    }\n  }\n\n  function transitionEnd$1 (runCallbacks, direction) {\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var activeIndex = swiper.activeIndex;\n    var previousIndex = swiper.previousIndex;\n    swiper.animating = false;\n    swiper.setTransition(0);\n\n    var dir = direction;\n    if (!dir) {\n      if (activeIndex > previousIndex) { dir = 'next'; }\n      else if (activeIndex < previousIndex) { dir = 'prev'; }\n      else { dir = 'reset'; }\n    }\n\n    swiper.emit('transitionEnd');\n\n    if (runCallbacks && activeIndex !== previousIndex) {\n      if (dir === 'reset') {\n        swiper.emit('slideResetTransitionEnd');\n        return;\n      }\n      swiper.emit('slideChangeTransitionEnd');\n      if (dir === 'next') {\n        swiper.emit('slideNextTransitionEnd');\n      } else {\n        swiper.emit('slidePrevTransitionEnd');\n      }\n    }\n  }\n\n  var transition$1 = {\n    setTransition: setTransition,\n    transitionStart: transitionStart,\n    transitionEnd: transitionEnd$1,\n  };\n\n  function slideTo (index, speed, runCallbacks, internal) {\n    if ( index === void 0 ) index = 0;\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var slideIndex = index;\n    if (slideIndex < 0) { slideIndex = 0; }\n\n    var params = swiper.params;\n    var snapGrid = swiper.snapGrid;\n    var slidesGrid = swiper.slidesGrid;\n    var previousIndex = swiper.previousIndex;\n    var activeIndex = swiper.activeIndex;\n    var rtl = swiper.rtlTranslate;\n    if (swiper.animating && params.preventInteractionOnTransition) {\n      return false;\n    }\n\n    var snapIndex = Math.floor(slideIndex / params.slidesPerGroup);\n    if (snapIndex >= snapGrid.length) { snapIndex = snapGrid.length - 1; }\n\n    if ((activeIndex || params.initialSlide || 0) === (previousIndex || 0) && runCallbacks) {\n      swiper.emit('beforeSlideChangeStart');\n    }\n\n    var translate = -snapGrid[snapIndex];\n\n    // Update progress\n    swiper.updateProgress(translate);\n\n    // Normalize slideIndex\n    if (params.normalizeSlideIndex) {\n      for (var i = 0; i < slidesGrid.length; i += 1) {\n        if (-Math.floor(translate * 100) >= Math.floor(slidesGrid[i] * 100)) {\n          slideIndex = i;\n        }\n      }\n    }\n    // Directions locks\n    if (swiper.initialized && slideIndex !== activeIndex) {\n      if (!swiper.allowSlideNext && translate < swiper.translate && translate < swiper.minTranslate()) {\n        return false;\n      }\n      if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n        if ((activeIndex || 0) !== slideIndex) { return false; }\n      }\n    }\n\n    var direction;\n    if (slideIndex > activeIndex) { direction = 'next'; }\n    else if (slideIndex < activeIndex) { direction = 'prev'; }\n    else { direction = 'reset'; }\n\n\n    // Update Index\n    if ((rtl && -translate === swiper.translate) || (!rtl && translate === swiper.translate)) {\n      swiper.updateActiveIndex(slideIndex);\n      // Update Height\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n      swiper.updateSlidesClasses();\n      if (params.effect !== 'slide') {\n        swiper.setTranslate(translate);\n      }\n      if (direction !== 'reset') {\n        swiper.transitionStart(runCallbacks, direction);\n        swiper.transitionEnd(runCallbacks, direction);\n      }\n      return false;\n    }\n\n    if (speed === 0 || !Support.transition) {\n      swiper.setTransition(0);\n      swiper.setTranslate(translate);\n      swiper.updateActiveIndex(slideIndex);\n      swiper.updateSlidesClasses();\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    } else {\n      swiper.setTransition(speed);\n      swiper.setTranslate(translate);\n      swiper.updateActiveIndex(slideIndex);\n      swiper.updateSlidesClasses();\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.transitionStart(runCallbacks, direction);\n      if (!swiper.animating) {\n        swiper.animating = true;\n        if (!swiper.onSlideToWrapperTransitionEnd) {\n          swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n            if (!swiper || swiper.destroyed) { return; }\n            if (e.target !== this) { return; }\n            swiper.$wrapperEl[0].removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n            swiper.$wrapperEl[0].removeEventListener('webkitTransitionEnd', swiper.onSlideToWrapperTransitionEnd);\n            swiper.onSlideToWrapperTransitionEnd = null;\n            delete swiper.onSlideToWrapperTransitionEnd;\n            swiper.transitionEnd(runCallbacks, direction);\n          };\n        }\n        swiper.$wrapperEl[0].addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.$wrapperEl[0].addEventListener('webkitTransitionEnd', swiper.onSlideToWrapperTransitionEnd);\n      }\n    }\n\n    return true;\n  }\n\n  function slideToLoop (index, speed, runCallbacks, internal) {\n    if ( index === void 0 ) index = 0;\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var newIndex = index;\n    if (swiper.params.loop) {\n      newIndex += swiper.loopedSlides;\n    }\n\n    return swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slideNext (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var params = swiper.params;\n    var animating = swiper.animating;\n    if (params.loop) {\n      if (animating) { return false; }\n      swiper.loopFix();\n      // eslint-disable-next-line\n      swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n      return swiper.slideTo(swiper.activeIndex + params.slidesPerGroup, speed, runCallbacks, internal);\n    }\n    return swiper.slideTo(swiper.activeIndex + params.slidesPerGroup, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slidePrev (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var params = swiper.params;\n    var animating = swiper.animating;\n    var snapGrid = swiper.snapGrid;\n    var slidesGrid = swiper.slidesGrid;\n    var rtlTranslate = swiper.rtlTranslate;\n\n    if (params.loop) {\n      if (animating) { return false; }\n      swiper.loopFix();\n      // eslint-disable-next-line\n      swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n    }\n    var translate = rtlTranslate ? swiper.translate : -swiper.translate;\n    function normalize(val) {\n      if (val < 0) { return -Math.floor(Math.abs(val)); }\n      return Math.floor(val);\n    }\n    var normalizedTranslate = normalize(translate);\n    var normalizedSnapGrid = snapGrid.map(function (val) { return normalize(val); });\n    var normalizedSlidesGrid = slidesGrid.map(function (val) { return normalize(val); });\n\n    var currentSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate)];\n    var prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n    var prevIndex;\n    if (typeof prevSnap !== 'undefined') {\n      prevIndex = slidesGrid.indexOf(prevSnap);\n      if (prevIndex < 0) { prevIndex = swiper.activeIndex - 1; }\n    }\n    return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slideReset (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n  }\n\n  /* eslint no-unused-vars: \"off\" */\n  function slideToClosest (speed, runCallbacks, internal) {\n    if ( speed === void 0 ) speed = this.params.speed;\n    if ( runCallbacks === void 0 ) runCallbacks = true;\n\n    var swiper = this;\n    var index = swiper.activeIndex;\n    var snapIndex = Math.floor(index / swiper.params.slidesPerGroup);\n\n    if (snapIndex < swiper.snapGrid.length - 1) {\n      var translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n\n      var currentSnap = swiper.snapGrid[snapIndex];\n      var nextSnap = swiper.snapGrid[snapIndex + 1];\n\n      if ((translate - currentSnap) > (nextSnap - currentSnap) / 2) {\n        index = swiper.params.slidesPerGroup;\n      }\n    }\n\n    return swiper.slideTo(index, speed, runCallbacks, internal);\n  }\n\n  function slideToClickedSlide () {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n\n    var slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n    var slideToIndex = swiper.clickedIndex;\n    var realIndex;\n    if (params.loop) {\n      if (swiper.animating) { return; }\n      realIndex = parseInt($(swiper.clickedSlide).attr('data-swiper-slide-index'), 10);\n      if (params.centeredSlides) {\n        if (\n          (slideToIndex < swiper.loopedSlides - (slidesPerView / 2))\n          || (slideToIndex > (swiper.slides.length - swiper.loopedSlides) + (slidesPerView / 2))\n        ) {\n          swiper.loopFix();\n          slideToIndex = $wrapperEl\n            .children((\".\" + (params.slideClass) + \"[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]:not(.\" + (params.slideDuplicateClass) + \")\"))\n            .eq(0)\n            .index();\n\n          Utils.nextTick(function () {\n            swiper.slideTo(slideToIndex);\n          });\n        } else {\n          swiper.slideTo(slideToIndex);\n        }\n      } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n        swiper.loopFix();\n        slideToIndex = $wrapperEl\n          .children((\".\" + (params.slideClass) + \"[data-swiper-slide-index=\\\"\" + realIndex + \"\\\"]:not(.\" + (params.slideDuplicateClass) + \")\"))\n          .eq(0)\n          .index();\n\n        Utils.nextTick(function () {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  }\n\n  var slide = {\n    slideTo: slideTo,\n    slideToLoop: slideToLoop,\n    slideNext: slideNext,\n    slidePrev: slidePrev,\n    slideReset: slideReset,\n    slideToClosest: slideToClosest,\n    slideToClickedSlide: slideToClickedSlide,\n  };\n\n  function loopCreate () {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    // Remove duplicated slides\n    $wrapperEl.children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass))).remove();\n\n    var slides = $wrapperEl.children((\".\" + (params.slideClass)));\n\n    if (params.loopFillGroupWithBlank) {\n      var blankSlidesNum = params.slidesPerGroup - (slides.length % params.slidesPerGroup);\n      if (blankSlidesNum !== params.slidesPerGroup) {\n        for (var i = 0; i < blankSlidesNum; i += 1) {\n          var blankNode = $(doc.createElement('div')).addClass(((params.slideClass) + \" \" + (params.slideBlankClass)));\n          $wrapperEl.append(blankNode);\n        }\n        slides = $wrapperEl.children((\".\" + (params.slideClass)));\n      }\n    }\n\n    if (params.slidesPerView === 'auto' && !params.loopedSlides) { params.loopedSlides = slides.length; }\n\n    swiper.loopedSlides = parseInt(params.loopedSlides || params.slidesPerView, 10);\n    swiper.loopedSlides += params.loopAdditionalSlides;\n    if (swiper.loopedSlides > slides.length) {\n      swiper.loopedSlides = slides.length;\n    }\n\n    var prependSlides = [];\n    var appendSlides = [];\n    slides.each(function (index, el) {\n      var slide = $(el);\n      if (index < swiper.loopedSlides) { appendSlides.push(el); }\n      if (index < slides.length && index >= slides.length - swiper.loopedSlides) { prependSlides.push(el); }\n      slide.attr('data-swiper-slide-index', index);\n    });\n    for (var i$1 = 0; i$1 < appendSlides.length; i$1 += 1) {\n      $wrapperEl.append($(appendSlides[i$1].cloneNode(true)).addClass(params.slideDuplicateClass));\n    }\n    for (var i$2 = prependSlides.length - 1; i$2 >= 0; i$2 -= 1) {\n      $wrapperEl.prepend($(prependSlides[i$2].cloneNode(true)).addClass(params.slideDuplicateClass));\n    }\n  }\n\n  function loopFix () {\n    var swiper = this;\n    var params = swiper.params;\n    var activeIndex = swiper.activeIndex;\n    var slides = swiper.slides;\n    var loopedSlides = swiper.loopedSlides;\n    var allowSlidePrev = swiper.allowSlidePrev;\n    var allowSlideNext = swiper.allowSlideNext;\n    var snapGrid = swiper.snapGrid;\n    var rtl = swiper.rtlTranslate;\n    var newIndex;\n    swiper.allowSlidePrev = true;\n    swiper.allowSlideNext = true;\n\n    var snapTranslate = -snapGrid[activeIndex];\n    var diff = snapTranslate - swiper.getTranslate();\n\n\n    // Fix For Negative Oversliding\n    if (activeIndex < loopedSlides) {\n      newIndex = (slides.length - (loopedSlides * 3)) + activeIndex;\n      newIndex += loopedSlides;\n      var slideChanged = swiper.slideTo(newIndex, 0, false, true);\n      if (slideChanged && diff !== 0) {\n        swiper.setTranslate((rtl ? -swiper.translate : swiper.translate) - diff);\n      }\n    } else if ((params.slidesPerView === 'auto' && activeIndex >= loopedSlides * 2) || (activeIndex >= slides.length - loopedSlides)) {\n      // Fix For Positive Oversliding\n      newIndex = -slides.length + activeIndex + loopedSlides;\n      newIndex += loopedSlides;\n      var slideChanged$1 = swiper.slideTo(newIndex, 0, false, true);\n      if (slideChanged$1 && diff !== 0) {\n        swiper.setTranslate((rtl ? -swiper.translate : swiper.translate) - diff);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n  }\n\n  function loopDestroy () {\n    var swiper = this;\n    var $wrapperEl = swiper.$wrapperEl;\n    var params = swiper.params;\n    var slides = swiper.slides;\n    $wrapperEl.children((\".\" + (params.slideClass) + \".\" + (params.slideDuplicateClass) + \",.\" + (params.slideClass) + \".\" + (params.slideBlankClass))).remove();\n    slides.removeAttr('data-swiper-slide-index');\n  }\n\n  var loop = {\n    loopCreate: loopCreate,\n    loopFix: loopFix,\n    loopDestroy: loopDestroy,\n  };\n\n  function setGrabCursor (moving) {\n    var swiper = this;\n    if (Support.touch || !swiper.params.simulateTouch || (swiper.params.watchOverflow && swiper.isLocked)) { return; }\n    var el = swiper.el;\n    el.style.cursor = 'move';\n    el.style.cursor = moving ? '-webkit-grabbing' : '-webkit-grab';\n    el.style.cursor = moving ? '-moz-grabbin' : '-moz-grab';\n    el.style.cursor = moving ? 'grabbing' : 'grab';\n  }\n\n  function unsetGrabCursor () {\n    var swiper = this;\n    if (Support.touch || (swiper.params.watchOverflow && swiper.isLocked)) { return; }\n    swiper.el.style.cursor = '';\n  }\n\n  var grabCursor = {\n    setGrabCursor: setGrabCursor,\n    unsetGrabCursor: unsetGrabCursor,\n  };\n\n  function appendSlide (slides) {\n    var swiper = this;\n    var $wrapperEl = swiper.$wrapperEl;\n    var params = swiper.params;\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (var i = 0; i < slides.length; i += 1) {\n        if (slides[i]) { $wrapperEl.append(slides[i]); }\n      }\n    } else {\n      $wrapperEl.append(slides);\n    }\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n  }\n\n  function prependSlide (slides) {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var activeIndex = swiper.activeIndex;\n\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n    var newActiveIndex = activeIndex + 1;\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (var i = 0; i < slides.length; i += 1) {\n        if (slides[i]) { $wrapperEl.prepend(slides[i]); }\n      }\n      newActiveIndex = activeIndex + slides.length;\n    } else {\n      $wrapperEl.prepend(slides);\n    }\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n\n  function addSlide (index, slides) {\n    var swiper = this;\n    var $wrapperEl = swiper.$wrapperEl;\n    var params = swiper.params;\n    var activeIndex = swiper.activeIndex;\n    var activeIndexBuffer = activeIndex;\n    if (params.loop) {\n      activeIndexBuffer -= swiper.loopedSlides;\n      swiper.loopDestroy();\n      swiper.slides = $wrapperEl.children((\".\" + (params.slideClass)));\n    }\n    var baseLength = swiper.slides.length;\n    if (index <= 0) {\n      swiper.prependSlide(slides);\n      return;\n    }\n    if (index >= baseLength) {\n      swiper.appendSlide(slides);\n      return;\n    }\n    var newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n\n    var slidesBuffer = [];\n    for (var i = baseLength - 1; i >= index; i -= 1) {\n      var currentSlide = swiper.slides.eq(i);\n      currentSlide.remove();\n      slidesBuffer.unshift(currentSlide);\n    }\n\n    if (typeof slides === 'object' && 'length' in slides) {\n      for (var i$1 = 0; i$1 < slides.length; i$1 += 1) {\n        if (slides[i$1]) { $wrapperEl.append(slides[i$1]); }\n      }\n      newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n    } else {\n      $wrapperEl.append(slides);\n    }\n\n    for (var i$2 = 0; i$2 < slidesBuffer.length; i$2 += 1) {\n      $wrapperEl.append(slidesBuffer[i$2]);\n    }\n\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n    if (params.loop) {\n      swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n    } else {\n      swiper.slideTo(newActiveIndex, 0, false);\n    }\n  }\n\n  function removeSlide (slidesIndexes) {\n    var swiper = this;\n    var params = swiper.params;\n    var $wrapperEl = swiper.$wrapperEl;\n    var activeIndex = swiper.activeIndex;\n\n    var activeIndexBuffer = activeIndex;\n    if (params.loop) {\n      activeIndexBuffer -= swiper.loopedSlides;\n      swiper.loopDestroy();\n      swiper.slides = $wrapperEl.children((\".\" + (params.slideClass)));\n    }\n    var newActiveIndex = activeIndexBuffer;\n    var indexToRemove;\n\n    if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n      for (var i = 0; i < slidesIndexes.length; i += 1) {\n        indexToRemove = slidesIndexes[i];\n        if (swiper.slides[indexToRemove]) { swiper.slides.eq(indexToRemove).remove(); }\n        if (indexToRemove < newActiveIndex) { newActiveIndex -= 1; }\n      }\n      newActiveIndex = Math.max(newActiveIndex, 0);\n    } else {\n      indexToRemove = slidesIndexes;\n      if (swiper.slides[indexToRemove]) { swiper.slides.eq(indexToRemove).remove(); }\n      if (indexToRemove < newActiveIndex) { newActiveIndex -= 1; }\n      newActiveIndex = Math.max(newActiveIndex, 0);\n    }\n\n    if (params.loop) {\n      swiper.loopCreate();\n    }\n\n    if (!(params.observer && Support.observer)) {\n      swiper.update();\n    }\n    if (params.loop) {\n      swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n    } else {\n      swiper.slideTo(newActiveIndex, 0, false);\n    }\n  }\n\n  function removeAllSlides () {\n    var swiper = this;\n\n    var slidesIndexes = [];\n    for (var i = 0; i < swiper.slides.length; i += 1) {\n      slidesIndexes.push(i);\n    }\n    swiper.removeSlide(slidesIndexes);\n  }\n\n  var manipulation = {\n    appendSlide: appendSlide,\n    prependSlide: prependSlide,\n    addSlide: addSlide,\n    removeSlide: removeSlide,\n    removeAllSlides: removeAllSlides,\n  };\n\n  var Device = (function Device() {\n    var ua = win.navigator.userAgent;\n\n    var device = {\n      ios: false,\n      android: false,\n      androidChrome: false,\n      desktop: false,\n      windows: false,\n      iphone: false,\n      ipod: false,\n      ipad: false,\n      cordova: win.cordova || win.phonegap,\n      phonegap: win.cordova || win.phonegap,\n    };\n\n    var windows = ua.match(/(Windows Phone);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n    var android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n    var ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n    var ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n    var iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n\n\n    // Windows\n    if (windows) {\n      device.os = 'windows';\n      device.osVersion = windows[2];\n      device.windows = true;\n    }\n    // Android\n    if (android && !windows) {\n      device.os = 'android';\n      device.osVersion = android[2];\n      device.android = true;\n      device.androidChrome = ua.toLowerCase().indexOf('chrome') >= 0;\n    }\n    if (ipad || iphone || ipod) {\n      device.os = 'ios';\n      device.ios = true;\n    }\n    // iOS\n    if (iphone && !ipod) {\n      device.osVersion = iphone[2].replace(/_/g, '.');\n      device.iphone = true;\n    }\n    if (ipad) {\n      device.osVersion = ipad[2].replace(/_/g, '.');\n      device.ipad = true;\n    }\n    if (ipod) {\n      device.osVersion = ipod[3] ? ipod[3].replace(/_/g, '.') : null;\n      device.iphone = true;\n    }\n    // iOS 8+ changed UA\n    if (device.ios && device.osVersion && ua.indexOf('Version/') >= 0) {\n      if (device.osVersion.split('.')[0] === '10') {\n        device.osVersion = ua.toLowerCase().split('version/')[1].split(' ')[0];\n      }\n    }\n\n    // Desktop\n    device.desktop = !(device.os || device.android || device.webView);\n\n    // Webview\n    device.webView = (iphone || ipad || ipod) && ua.match(/.*AppleWebKit(?!.*Safari)/i);\n\n    // Minimal UI\n    if (device.os && device.os === 'ios') {\n      var osVersionArr = device.osVersion.split('.');\n      var metaViewport = doc.querySelector('meta[name=\"viewport\"]');\n      device.minimalUi = !device.webView\n        && (ipod || iphone)\n        && (osVersionArr[0] * 1 === 7 ? osVersionArr[1] * 1 >= 1 : osVersionArr[0] * 1 > 7)\n        && metaViewport && metaViewport.getAttribute('content').indexOf('minimal-ui') >= 0;\n    }\n\n    // Pixel Ratio\n    device.pixelRatio = win.devicePixelRatio || 1;\n\n    // Export object\n    return device;\n  }());\n\n  function onTouchStart (event) {\n    var swiper = this;\n    var data = swiper.touchEventsData;\n    var params = swiper.params;\n    var touches = swiper.touches;\n    if (swiper.animating && params.preventInteractionOnTransition) {\n      return;\n    }\n    var e = event;\n    if (e.originalEvent) { e = e.originalEvent; }\n    data.isTouchEvent = e.type === 'touchstart';\n    if (!data.isTouchEvent && 'which' in e && e.which === 3) { return; }\n    if (!data.isTouchEvent && 'button' in e && e.button > 0) { return; }\n    if (data.isTouched && data.isMoved) { return; }\n    if (params.noSwiping && $(e.target).closest(params.noSwipingSelector ? params.noSwipingSelector : (\".\" + (params.noSwipingClass)))[0]) {\n      swiper.allowClick = true;\n      return;\n    }\n    if (params.swipeHandler) {\n      if (!$(e).closest(params.swipeHandler)[0]) { return; }\n    }\n\n    touches.currentX = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n    touches.currentY = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n    var startX = touches.currentX;\n    var startY = touches.currentY;\n\n    // Do NOT start if iOS edge swipe is detected. Otherwise iOS app (UIWebView) cannot swipe-to-go-back anymore\n\n    var edgeSwipeDetection = params.edgeSwipeDetection || params.iOSEdgeSwipeDetection;\n    var edgeSwipeThreshold = params.edgeSwipeThreshold || params.iOSEdgeSwipeThreshold;\n    if (\n      edgeSwipeDetection\n      && ((startX <= edgeSwipeThreshold)\n      || (startX >= win.screen.width - edgeSwipeThreshold))\n    ) {\n      return;\n    }\n\n    Utils.extend(data, {\n      isTouched: true,\n      isMoved: false,\n      allowTouchCallbacks: true,\n      isScrolling: undefined,\n      startMoving: undefined,\n    });\n\n    touches.startX = startX;\n    touches.startY = startY;\n    data.touchStartTime = Utils.now();\n    swiper.allowClick = true;\n    swiper.updateSize();\n    swiper.swipeDirection = undefined;\n    if (params.threshold > 0) { data.allowThresholdMove = false; }\n    if (e.type !== 'touchstart') {\n      var preventDefault = true;\n      if ($(e.target).is(data.formElements)) { preventDefault = false; }\n      if (\n        doc.activeElement\n        && $(doc.activeElement).is(data.formElements)\n        && doc.activeElement !== e.target\n      ) {\n        doc.activeElement.blur();\n      }\n\n      var shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n      if (params.touchStartForcePreventDefault || shouldPreventDefault) {\n        e.preventDefault();\n      }\n    }\n    swiper.emit('touchStart', e);\n  }\n\n  function onTouchMove (event) {\n    var swiper = this;\n    var data = swiper.touchEventsData;\n    var params = swiper.params;\n    var touches = swiper.touches;\n    var rtl = swiper.rtlTranslate;\n    var e = event;\n    if (e.originalEvent) { e = e.originalEvent; }\n    if (!data.isTouched) {\n      if (data.startMoving && data.isScrolling) {\n        swiper.emit('touchMoveOpposite', e);\n      }\n      return;\n    }\n    if (data.isTouchEvent && e.type === 'mousemove') { return; }\n    var pageX = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n    var pageY = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n    if (e.preventedByNestedSwiper) {\n      touches.startX = pageX;\n      touches.startY = pageY;\n      return;\n    }\n    if (!swiper.allowTouchMove) {\n      // isMoved = true;\n      swiper.allowClick = false;\n      if (data.isTouched) {\n        Utils.extend(touches, {\n          startX: pageX,\n          startY: pageY,\n          currentX: pageX,\n          currentY: pageY,\n        });\n        data.touchStartTime = Utils.now();\n      }\n      return;\n    }\n    if (data.isTouchEvent && params.touchReleaseOnEdges && !params.loop) {\n      if (swiper.isVertical()) {\n        // Vertical\n        if (\n          (pageY < touches.startY && swiper.translate <= swiper.maxTranslate())\n          || (pageY > touches.startY && swiper.translate >= swiper.minTranslate())\n        ) {\n          data.isTouched = false;\n          data.isMoved = false;\n          return;\n        }\n      } else if (\n        (pageX < touches.startX && swiper.translate <= swiper.maxTranslate())\n        || (pageX > touches.startX && swiper.translate >= swiper.minTranslate())\n      ) {\n        return;\n      }\n    }\n    if (data.isTouchEvent && doc.activeElement) {\n      if (e.target === doc.activeElement && $(e.target).is(data.formElements)) {\n        data.isMoved = true;\n        swiper.allowClick = false;\n        return;\n      }\n    }\n    if (data.allowTouchCallbacks) {\n      swiper.emit('touchMove', e);\n    }\n    if (e.targetTouches && e.targetTouches.length > 1) { return; }\n\n    touches.currentX = pageX;\n    touches.currentY = pageY;\n\n    var diffX = touches.currentX - touches.startX;\n    var diffY = touches.currentY - touches.startY;\n    if (swiper.params.threshold && Math.sqrt((Math.pow( diffX, 2 )) + (Math.pow( diffY, 2 ))) < swiper.params.threshold) { return; }\n\n    if (typeof data.isScrolling === 'undefined') {\n      var touchAngle;\n      if ((swiper.isHorizontal() && touches.currentY === touches.startY) || (swiper.isVertical() && touches.currentX === touches.startX)) {\n        data.isScrolling = false;\n      } else {\n        // eslint-disable-next-line\n        if ((diffX * diffX) + (diffY * diffY) >= 25) {\n          touchAngle = (Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180) / Math.PI;\n          data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : (90 - touchAngle > params.touchAngle);\n        }\n      }\n    }\n    if (data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    if (typeof data.startMoving === 'undefined') {\n      if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n        data.startMoving = true;\n      }\n    }\n    if (data.isScrolling) {\n      data.isTouched = false;\n      return;\n    }\n    if (!data.startMoving) {\n      return;\n    }\n    swiper.allowClick = false;\n    e.preventDefault();\n    if (params.touchMoveStopPropagation && !params.nested) {\n      e.stopPropagation();\n    }\n\n    if (!data.isMoved) {\n      if (params.loop) {\n        swiper.loopFix();\n      }\n      data.startTranslate = swiper.getTranslate();\n      swiper.setTransition(0);\n      if (swiper.animating) {\n        swiper.$wrapperEl.trigger('webkitTransitionEnd transitionend');\n      }\n      data.allowMomentumBounce = false;\n      // Grab Cursor\n      if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n        swiper.setGrabCursor(true);\n      }\n      swiper.emit('sliderFirstMove', e);\n    }\n    swiper.emit('sliderMove', e);\n    data.isMoved = true;\n\n    var diff = swiper.isHorizontal() ? diffX : diffY;\n    touches.diff = diff;\n\n    diff *= params.touchRatio;\n    if (rtl) { diff = -diff; }\n\n    swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n    data.currentTranslate = diff + data.startTranslate;\n\n    var disableParentSwiper = true;\n    var resistanceRatio = params.resistanceRatio;\n    if (params.touchReleaseOnEdges) {\n      resistanceRatio = 0;\n    }\n    if ((diff > 0 && data.currentTranslate > swiper.minTranslate())) {\n      disableParentSwiper = false;\n      if (params.resistance) { data.currentTranslate = (swiper.minTranslate() - 1) + (Math.pow( (-swiper.minTranslate() + data.startTranslate + diff), resistanceRatio )); }\n    } else if (diff < 0 && data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) { data.currentTranslate = (swiper.maxTranslate() + 1) - (Math.pow( (swiper.maxTranslate() - data.startTranslate - diff), resistanceRatio )); }\n    }\n\n    if (disableParentSwiper) {\n      e.preventedByNestedSwiper = true;\n    }\n\n    // Directions locks\n    if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n      data.currentTranslate = data.startTranslate;\n    }\n    if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n      data.currentTranslate = data.startTranslate;\n    }\n\n\n    // Threshold\n    if (params.threshold > 0) {\n      if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n        if (!data.allowThresholdMove) {\n          data.allowThresholdMove = true;\n          touches.startX = touches.currentX;\n          touches.startY = touches.currentY;\n          data.currentTranslate = data.startTranslate;\n          touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n          return;\n        }\n      } else {\n        data.currentTranslate = data.startTranslate;\n        return;\n      }\n    }\n\n    if (!params.followFinger) { return; }\n\n    // Update active index in free mode\n    if (params.freeMode || params.watchSlidesProgress || params.watchSlidesVisibility) {\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    if (params.freeMode) {\n      // Velocity\n      if (data.velocities.length === 0) {\n        data.velocities.push({\n          position: touches[swiper.isHorizontal() ? 'startX' : 'startY'],\n          time: data.touchStartTime,\n        });\n      }\n      data.velocities.push({\n        position: touches[swiper.isHorizontal() ? 'currentX' : 'currentY'],\n        time: Utils.now(),\n      });\n    }\n    // Update progress\n    swiper.updateProgress(data.currentTranslate);\n    // Update translate\n    swiper.setTranslate(data.currentTranslate);\n  }\n\n  function onTouchEnd (event) {\n    var swiper = this;\n    var data = swiper.touchEventsData;\n\n    var params = swiper.params;\n    var touches = swiper.touches;\n    var rtl = swiper.rtlTranslate;\n    var $wrapperEl = swiper.$wrapperEl;\n    var slidesGrid = swiper.slidesGrid;\n    var snapGrid = swiper.snapGrid;\n    var e = event;\n    if (e.originalEvent) { e = e.originalEvent; }\n    if (data.allowTouchCallbacks) {\n      swiper.emit('touchEnd', e);\n    }\n    data.allowTouchCallbacks = false;\n    if (!data.isTouched) {\n      if (data.isMoved && params.grabCursor) {\n        swiper.setGrabCursor(false);\n      }\n      data.isMoved = false;\n      data.startMoving = false;\n      return;\n    }\n    // Return Grab Cursor\n    if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(false);\n    }\n\n    // Time diff\n    var touchEndTime = Utils.now();\n    var timeDiff = touchEndTime - data.touchStartTime;\n\n    // Tap, doubleTap, Click\n    if (swiper.allowClick) {\n      swiper.updateClickedSlide(e);\n      swiper.emit('tap', e);\n      if (timeDiff < 300 && (touchEndTime - data.lastClickTime) > 300) {\n        if (data.clickTimeout) { clearTimeout(data.clickTimeout); }\n        data.clickTimeout = Utils.nextTick(function () {\n          if (!swiper || swiper.destroyed) { return; }\n          swiper.emit('click', e);\n        }, 300);\n      }\n      if (timeDiff < 300 && (touchEndTime - data.lastClickTime) < 300) {\n        if (data.clickTimeout) { clearTimeout(data.clickTimeout); }\n        swiper.emit('doubleTap', e);\n      }\n    }\n\n    data.lastClickTime = Utils.now();\n    Utils.nextTick(function () {\n      if (!swiper.destroyed) { swiper.allowClick = true; }\n    });\n\n    if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 || data.currentTranslate === data.startTranslate) {\n      data.isTouched = false;\n      data.isMoved = false;\n      data.startMoving = false;\n      return;\n    }\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n\n    var currentPos;\n    if (params.followFinger) {\n      currentPos = rtl ? swiper.translate : -swiper.translate;\n    } else {\n      currentPos = -data.currentTranslate;\n    }\n\n    if (params.freeMode) {\n      if (currentPos < -swiper.minTranslate()) {\n        swiper.slideTo(swiper.activeIndex);\n        return;\n      }\n      if (currentPos > -swiper.maxTranslate()) {\n        if (swiper.slides.length < snapGrid.length) {\n          swiper.slideTo(snapGrid.length - 1);\n        } else {\n          swiper.slideTo(swiper.slides.length - 1);\n        }\n        return;\n      }\n\n      if (params.freeModeMomentum) {\n        if (data.velocities.length > 1) {\n          var lastMoveEvent = data.velocities.pop();\n          var velocityEvent = data.velocities.pop();\n\n          var distance = lastMoveEvent.position - velocityEvent.position;\n          var time = lastMoveEvent.time - velocityEvent.time;\n          swiper.velocity = distance / time;\n          swiper.velocity /= 2;\n          if (Math.abs(swiper.velocity) < params.freeModeMinimumVelocity) {\n            swiper.velocity = 0;\n          }\n          // this implies that the user stopped moving a finger then released.\n          // There would be no events with distance zero, so the last event is stale.\n          if (time > 150 || (Utils.now() - lastMoveEvent.time) > 300) {\n            swiper.velocity = 0;\n          }\n        } else {\n          swiper.velocity = 0;\n        }\n        swiper.velocity *= params.freeModeMomentumVelocityRatio;\n\n        data.velocities.length = 0;\n        var momentumDuration = 1000 * params.freeModeMomentumRatio;\n        var momentumDistance = swiper.velocity * momentumDuration;\n\n        var newPosition = swiper.translate + momentumDistance;\n        if (rtl) { newPosition = -newPosition; }\n\n        var doBounce = false;\n        var afterBouncePosition;\n        var bounceAmount = Math.abs(swiper.velocity) * 20 * params.freeModeMomentumBounceRatio;\n        var needsLoopFix;\n        if (newPosition < swiper.maxTranslate()) {\n          if (params.freeModeMomentumBounce) {\n            if (newPosition + swiper.maxTranslate() < -bounceAmount) {\n              newPosition = swiper.maxTranslate() - bounceAmount;\n            }\n            afterBouncePosition = swiper.maxTranslate();\n            doBounce = true;\n            data.allowMomentumBounce = true;\n          } else {\n            newPosition = swiper.maxTranslate();\n          }\n          if (params.loop && params.centeredSlides) { needsLoopFix = true; }\n        } else if (newPosition > swiper.minTranslate()) {\n          if (params.freeModeMomentumBounce) {\n            if (newPosition - swiper.minTranslate() > bounceAmount) {\n              newPosition = swiper.minTranslate() + bounceAmount;\n            }\n            afterBouncePosition = swiper.minTranslate();\n            doBounce = true;\n            data.allowMomentumBounce = true;\n          } else {\n            newPosition = swiper.minTranslate();\n          }\n          if (params.loop && params.centeredSlides) { needsLoopFix = true; }\n        } else if (params.freeModeSticky) {\n          var nextSlide;\n          for (var j = 0; j < snapGrid.length; j += 1) {\n            if (snapGrid[j] > -newPosition) {\n              nextSlide = j;\n              break;\n            }\n          }\n\n          if (Math.abs(snapGrid[nextSlide] - newPosition) < Math.abs(snapGrid[nextSlide - 1] - newPosition) || swiper.swipeDirection === 'next') {\n            newPosition = snapGrid[nextSlide];\n          } else {\n            newPosition = snapGrid[nextSlide - 1];\n          }\n          newPosition = -newPosition;\n        }\n        if (needsLoopFix) {\n          swiper.once('transitionEnd', function () {\n            swiper.loopFix();\n          });\n        }\n        // Fix duration\n        if (swiper.velocity !== 0) {\n          if (rtl) {\n            momentumDuration = Math.abs((-newPosition - swiper.translate) / swiper.velocity);\n          } else {\n            momentumDuration = Math.abs((newPosition - swiper.translate) / swiper.velocity);\n          }\n        } else if (params.freeModeSticky) {\n          swiper.slideToClosest();\n          return;\n        }\n\n        if (params.freeModeMomentumBounce && doBounce) {\n          swiper.updateProgress(afterBouncePosition);\n          swiper.setTransition(momentumDuration);\n          swiper.setTranslate(newPosition);\n          swiper.transitionStart(true, swiper.swipeDirection);\n          swiper.animating = true;\n          $wrapperEl.transitionEnd(function () {\n            if (!swiper || swiper.destroyed || !data.allowMomentumBounce) { return; }\n            swiper.emit('momentumBounce');\n\n            swiper.setTransition(params.speed);\n            swiper.setTranslate(afterBouncePosition);\n            $wrapperEl.transitionEnd(function () {\n              if (!swiper || swiper.destroyed) { return; }\n              swiper.transitionEnd();\n            });\n          });\n        } else if (swiper.velocity) {\n          swiper.updateProgress(newPosition);\n          swiper.setTransition(momentumDuration);\n          swiper.setTranslate(newPosition);\n          swiper.transitionStart(true, swiper.swipeDirection);\n          if (!swiper.animating) {\n            swiper.animating = true;\n            $wrapperEl.transitionEnd(function () {\n              if (!swiper || swiper.destroyed) { return; }\n              swiper.transitionEnd();\n            });\n          }\n        } else {\n          swiper.updateProgress(newPosition);\n        }\n\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n      } else if (params.freeModeSticky) {\n        swiper.slideToClosest();\n        return;\n      }\n\n      if (!params.freeModeMomentum || timeDiff >= params.longSwipesMs) {\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n      }\n      return;\n    }\n\n    // Find current slide\n    var stopIndex = 0;\n    var groupSize = swiper.slidesSizesGrid[0];\n    for (var i = 0; i < slidesGrid.length; i += params.slidesPerGroup) {\n      if (typeof slidesGrid[i + params.slidesPerGroup] !== 'undefined') {\n        if (currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + params.slidesPerGroup]) {\n          stopIndex = i;\n          groupSize = slidesGrid[i + params.slidesPerGroup] - slidesGrid[i];\n        }\n      } else if (currentPos >= slidesGrid[i]) {\n        stopIndex = i;\n        groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n      }\n    }\n\n    // Find current slide size\n    var ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n\n    if (timeDiff > params.longSwipesMs) {\n      // Long touches\n      if (!params.longSwipes) {\n        swiper.slideTo(swiper.activeIndex);\n        return;\n      }\n      if (swiper.swipeDirection === 'next') {\n        if (ratio >= params.longSwipesRatio) { swiper.slideTo(stopIndex + params.slidesPerGroup); }\n        else { swiper.slideTo(stopIndex); }\n      }\n      if (swiper.swipeDirection === 'prev') {\n        if (ratio > (1 - params.longSwipesRatio)) { swiper.slideTo(stopIndex + params.slidesPerGroup); }\n        else { swiper.slideTo(stopIndex); }\n      }\n    } else {\n      // Short swipes\n      if (!params.shortSwipes) {\n        swiper.slideTo(swiper.activeIndex);\n        return;\n      }\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(stopIndex + params.slidesPerGroup);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  }\n\n  function onResize () {\n    var swiper = this;\n\n    var params = swiper.params;\n    var el = swiper.el;\n\n    if (el && el.offsetWidth === 0) { return; }\n\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Save locks\n    var allowSlideNext = swiper.allowSlideNext;\n    var allowSlidePrev = swiper.allowSlidePrev;\n    var snapGrid = swiper.snapGrid;\n\n    // Disable locks on resize\n    swiper.allowSlideNext = true;\n    swiper.allowSlidePrev = true;\n\n    swiper.updateSize();\n    swiper.updateSlides();\n\n    if (params.freeMode) {\n      var newTranslate = Math.min(Math.max(swiper.translate, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      swiper.updateSlidesClasses();\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.params.centeredSlides) {\n        swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n      } else {\n        swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n    }\n    // Return locks after resize\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n\n    if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n  }\n\n  function onClick (e) {\n    var swiper = this;\n    if (!swiper.allowClick) {\n      if (swiper.params.preventClicks) { e.preventDefault(); }\n      if (swiper.params.preventClicksPropagation && swiper.animating) {\n        e.stopPropagation();\n        e.stopImmediatePropagation();\n      }\n    }\n  }\n\n  function attachEvents() {\n    var swiper = this;\n    var params = swiper.params;\n    var touchEvents = swiper.touchEvents;\n    var el = swiper.el;\n    var wrapperEl = swiper.wrapperEl;\n\n    {\n      swiper.onTouchStart = onTouchStart.bind(swiper);\n      swiper.onTouchMove = onTouchMove.bind(swiper);\n      swiper.onTouchEnd = onTouchEnd.bind(swiper);\n    }\n\n    swiper.onClick = onClick.bind(swiper);\n\n    var target = params.touchEventsTarget === 'container' ? el : wrapperEl;\n    var capture = !!params.nested;\n\n    // Touch Events\n    {\n      if (!Support.touch && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n        target.addEventListener(touchEvents.start, swiper.onTouchStart, false);\n        doc.addEventListener(touchEvents.move, swiper.onTouchMove, capture);\n        doc.addEventListener(touchEvents.end, swiper.onTouchEnd, false);\n      } else {\n        if (Support.touch) {\n          var passiveListener = touchEvents.start === 'touchstart' && Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n          target.addEventListener(touchEvents.start, swiper.onTouchStart, passiveListener);\n          target.addEventListener(touchEvents.move, swiper.onTouchMove, Support.passiveListener ? { passive: false, capture: capture } : capture);\n          target.addEventListener(touchEvents.end, swiper.onTouchEnd, passiveListener);\n        }\n        if ((params.simulateTouch && !Device.ios && !Device.android) || (params.simulateTouch && !Support.touch && Device.ios)) {\n          target.addEventListener('mousedown', swiper.onTouchStart, false);\n          doc.addEventListener('mousemove', swiper.onTouchMove, capture);\n          doc.addEventListener('mouseup', swiper.onTouchEnd, false);\n        }\n      }\n      // Prevent Links Clicks\n      if (params.preventClicks || params.preventClicksPropagation) {\n        target.addEventListener('click', swiper.onClick, true);\n      }\n    }\n\n    // Resize handler\n    swiper.on((Device.ios || Device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate'), onResize, true);\n  }\n\n  function detachEvents() {\n    var swiper = this;\n\n    var params = swiper.params;\n    var touchEvents = swiper.touchEvents;\n    var el = swiper.el;\n    var wrapperEl = swiper.wrapperEl;\n\n    var target = params.touchEventsTarget === 'container' ? el : wrapperEl;\n    var capture = !!params.nested;\n\n    // Touch Events\n    {\n      if (!Support.touch && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n        target.removeEventListener(touchEvents.start, swiper.onTouchStart, false);\n        doc.removeEventListener(touchEvents.move, swiper.onTouchMove, capture);\n        doc.removeEventListener(touchEvents.end, swiper.onTouchEnd, false);\n      } else {\n        if (Support.touch) {\n          var passiveListener = touchEvents.start === 'onTouchStart' && Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n          target.removeEventListener(touchEvents.start, swiper.onTouchStart, passiveListener);\n          target.removeEventListener(touchEvents.move, swiper.onTouchMove, capture);\n          target.removeEventListener(touchEvents.end, swiper.onTouchEnd, passiveListener);\n        }\n        if ((params.simulateTouch && !Device.ios && !Device.android) || (params.simulateTouch && !Support.touch && Device.ios)) {\n          target.removeEventListener('mousedown', swiper.onTouchStart, false);\n          doc.removeEventListener('mousemove', swiper.onTouchMove, capture);\n          doc.removeEventListener('mouseup', swiper.onTouchEnd, false);\n        }\n      }\n      // Prevent Links Clicks\n      if (params.preventClicks || params.preventClicksPropagation) {\n        target.removeEventListener('click', swiper.onClick, true);\n      }\n    }\n\n    // Resize handler\n    swiper.off((Device.ios || Device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate'), onResize);\n  }\n\n  var events = {\n    attachEvents: attachEvents,\n    detachEvents: detachEvents,\n  };\n\n  function setBreakpoint () {\n    var swiper = this;\n    var activeIndex = swiper.activeIndex;\n    var initialized = swiper.initialized;\n    var loopedSlides = swiper.loopedSlides; if ( loopedSlides === void 0 ) loopedSlides = 0;\n    var params = swiper.params;\n    var breakpoints = params.breakpoints;\n    if (!breakpoints || (breakpoints && Object.keys(breakpoints).length === 0)) { return; }\n\n    // Set breakpoint for window width and update parameters\n    var breakpoint = swiper.getBreakpoint(breakpoints);\n\n    if (breakpoint && swiper.currentBreakpoint !== breakpoint) {\n      var breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n      if (breakpointOnlyParams) {\n        ['slidesPerView', 'spaceBetween', 'slidesPerGroup'].forEach(function (param) {\n          var paramValue = breakpointOnlyParams[param];\n          if (typeof paramValue === 'undefined') { return; }\n          if (param === 'slidesPerView' && (paramValue === 'AUTO' || paramValue === 'auto')) {\n            breakpointOnlyParams[param] = 'auto';\n          } else if (param === 'slidesPerView') {\n            breakpointOnlyParams[param] = parseFloat(paramValue);\n          } else {\n            breakpointOnlyParams[param] = parseInt(paramValue, 10);\n          }\n        });\n      }\n\n      var breakpointParams = breakpointOnlyParams || swiper.originalParams;\n      var directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n      var needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n\n      if (directionChanged && initialized) {\n        swiper.changeDirection();\n      }\n\n      Utils.extend(swiper.params, breakpointParams);\n\n      Utils.extend(swiper, {\n        allowTouchMove: swiper.params.allowTouchMove,\n        allowSlideNext: swiper.params.allowSlideNext,\n        allowSlidePrev: swiper.params.allowSlidePrev,\n      });\n\n      swiper.currentBreakpoint = breakpoint;\n\n      if (needsReLoop && initialized) {\n        swiper.loopDestroy();\n        swiper.loopCreate();\n        swiper.updateSlides();\n        swiper.slideTo((activeIndex - loopedSlides) + swiper.loopedSlides, 0, false);\n      }\n\n      swiper.emit('breakpoint', breakpointParams);\n    }\n  }\n\n  function getBreakpoint (breakpoints) {\n    var swiper = this;\n    // Get breakpoint for window width\n    if (!breakpoints) { return undefined; }\n    var breakpoint = false;\n    var points = [];\n    Object.keys(breakpoints).forEach(function (point) {\n      points.push(point);\n    });\n    points.sort(function (a, b) { return parseInt(a, 10) - parseInt(b, 10); });\n    for (var i = 0; i < points.length; i += 1) {\n      var point = points[i];\n      if (swiper.params.breakpointsInverse) {\n        if (point <= win.innerWidth) {\n          breakpoint = point;\n        }\n      } else if (point >= win.innerWidth && !breakpoint) {\n        breakpoint = point;\n      }\n    }\n    return breakpoint || 'max';\n  }\n\n  var breakpoints = { setBreakpoint: setBreakpoint, getBreakpoint: getBreakpoint };\n\n  function addClasses () {\n    var swiper = this;\n    var classNames = swiper.classNames;\n    var params = swiper.params;\n    var rtl = swiper.rtl;\n    var $el = swiper.$el;\n    var suffixes = [];\n\n    suffixes.push('initialized');\n    suffixes.push(params.direction);\n\n    if (params.freeMode) {\n      suffixes.push('free-mode');\n    }\n    if (!Support.flexbox) {\n      suffixes.push('no-flexbox');\n    }\n    if (params.autoHeight) {\n      suffixes.push('autoheight');\n    }\n    if (rtl) {\n      suffixes.push('rtl');\n    }\n    if (params.slidesPerColumn > 1) {\n      suffixes.push('multirow');\n    }\n    if (Device.android) {\n      suffixes.push('android');\n    }\n    if (Device.ios) {\n      suffixes.push('ios');\n    }\n    // WP8 Touch Events Fix\n    if ((Browser.isIE || Browser.isEdge) && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n      suffixes.push((\"wp8-\" + (params.direction)));\n    }\n\n    suffixes.forEach(function (suffix) {\n      classNames.push(params.containerModifierClass + suffix);\n    });\n\n    $el.addClass(classNames.join(' '));\n  }\n\n  function removeClasses () {\n    var swiper = this;\n    var $el = swiper.$el;\n    var classNames = swiper.classNames;\n\n    $el.removeClass(classNames.join(' '));\n  }\n\n  var classes = { addClasses: addClasses, removeClasses: removeClasses };\n\n  function loadImage (imageEl, src, srcset, sizes, checkForComplete, callback) {\n    var image;\n    function onReady() {\n      if (callback) { callback(); }\n    }\n    if (!imageEl.complete || !checkForComplete) {\n      if (src) {\n        image = new win.Image();\n        image.onload = onReady;\n        image.onerror = onReady;\n        if (sizes) {\n          image.sizes = sizes;\n        }\n        if (srcset) {\n          image.srcset = srcset;\n        }\n        if (src) {\n          image.src = src;\n        }\n      } else {\n        onReady();\n      }\n    } else {\n      // image already loaded...\n      onReady();\n    }\n  }\n\n  function preloadImages () {\n    var swiper = this;\n    swiper.imagesToLoad = swiper.$el.find('img');\n    function onReady() {\n      if (typeof swiper === 'undefined' || swiper === null || !swiper || swiper.destroyed) { return; }\n      if (swiper.imagesLoaded !== undefined) { swiper.imagesLoaded += 1; }\n      if (swiper.imagesLoaded === swiper.imagesToLoad.length) {\n        if (swiper.params.updateOnImagesReady) { swiper.update(); }\n        swiper.emit('imagesReady');\n      }\n    }\n    for (var i = 0; i < swiper.imagesToLoad.length; i += 1) {\n      var imageEl = swiper.imagesToLoad[i];\n      swiper.loadImage(\n        imageEl,\n        imageEl.currentSrc || imageEl.getAttribute('src'),\n        imageEl.srcset || imageEl.getAttribute('srcset'),\n        imageEl.sizes || imageEl.getAttribute('sizes'),\n        true,\n        onReady\n      );\n    }\n  }\n\n  var images = {\n    loadImage: loadImage,\n    preloadImages: preloadImages,\n  };\n\n  function checkOverflow() {\n    var swiper = this;\n    var wasLocked = swiper.isLocked;\n\n    swiper.isLocked = swiper.snapGrid.length === 1;\n    swiper.allowSlideNext = !swiper.isLocked;\n    swiper.allowSlidePrev = !swiper.isLocked;\n\n    // events\n    if (wasLocked !== swiper.isLocked) { swiper.emit(swiper.isLocked ? 'lock' : 'unlock'); }\n\n    if (wasLocked && wasLocked !== swiper.isLocked) {\n      swiper.isEnd = false;\n      swiper.navigation.update();\n    }\n  }\n\n  var checkOverflow$1 = { checkOverflow: checkOverflow };\n\n  var defaults = {\n    init: true,\n    direction: 'horizontal',\n    touchEventsTarget: 'container',\n    initialSlide: 0,\n    speed: 300,\n    //\n    preventInteractionOnTransition: false,\n\n    // To support iOS's swipe-to-go-back gesture (when being used in-app, with UIWebView).\n    edgeSwipeDetection: false,\n    edgeSwipeThreshold: 20,\n\n    // Free mode\n    freeMode: false,\n    freeModeMomentum: true,\n    freeModeMomentumRatio: 1,\n    freeModeMomentumBounce: true,\n    freeModeMomentumBounceRatio: 1,\n    freeModeMomentumVelocityRatio: 1,\n    freeModeSticky: false,\n    freeModeMinimumVelocity: 0.02,\n\n    // Autoheight\n    autoHeight: false,\n\n    // Set wrapper width\n    setWrapperSize: false,\n\n    // Virtual Translate\n    virtualTranslate: false,\n\n    // Effects\n    effect: 'slide', // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n    // Breakpoints\n    breakpoints: undefined,\n    breakpointsInverse: false,\n\n    // Slides grid\n    spaceBetween: 0,\n    slidesPerView: 1,\n    slidesPerColumn: 1,\n    slidesPerColumnFill: 'column',\n    slidesPerGroup: 1,\n    centeredSlides: false,\n    slidesOffsetBefore: 0, // in px\n    slidesOffsetAfter: 0, // in px\n    normalizeSlideIndex: true,\n    centerInsufficientSlides: false,\n\n    // Disable swiper and hide navigation when container not overflow\n    watchOverflow: false,\n\n    // Round length\n    roundLengths: false,\n\n    // Touches\n    touchRatio: 1,\n    touchAngle: 45,\n    simulateTouch: true,\n    shortSwipes: true,\n    longSwipes: true,\n    longSwipesRatio: 0.5,\n    longSwipesMs: 300,\n    followFinger: true,\n    allowTouchMove: true,\n    threshold: 0,\n    touchMoveStopPropagation: true,\n    touchStartPreventDefault: true,\n    touchStartForcePreventDefault: false,\n    touchReleaseOnEdges: false,\n\n    // Unique Navigation Elements\n    uniqueNavElements: true,\n\n    // Resistance\n    resistance: true,\n    resistanceRatio: 0.85,\n\n    // Progress\n    watchSlidesProgress: false,\n    watchSlidesVisibility: false,\n\n    // Cursor\n    grabCursor: false,\n\n    // Clicks\n    preventClicks: true,\n    preventClicksPropagation: true,\n    slideToClickedSlide: false,\n\n    // Images\n    preloadImages: true,\n    updateOnImagesReady: true,\n\n    // loop\n    loop: false,\n    loopAdditionalSlides: 0,\n    loopedSlides: null,\n    loopFillGroupWithBlank: false,\n\n    // Swiping/no swiping\n    allowSlidePrev: true,\n    allowSlideNext: true,\n    swipeHandler: null, // '.swipe-handler',\n    noSwiping: true,\n    noSwipingClass: 'swiper-no-swiping',\n    noSwipingSelector: null,\n\n    // Passive Listeners\n    passiveListeners: true,\n\n    // NS\n    containerModifierClass: 'swiper-container-', // NEW\n    slideClass: 'swiper-slide',\n    slideBlankClass: 'swiper-slide-invisible-blank',\n    slideActiveClass: 'swiper-slide-active',\n    slideDuplicateActiveClass: 'swiper-slide-duplicate-active',\n    slideVisibleClass: 'swiper-slide-visible',\n    slideDuplicateClass: 'swiper-slide-duplicate',\n    slideNextClass: 'swiper-slide-next',\n    slideDuplicateNextClass: 'swiper-slide-duplicate-next',\n    slidePrevClass: 'swiper-slide-prev',\n    slideDuplicatePrevClass: 'swiper-slide-duplicate-prev',\n    wrapperClass: 'swiper-wrapper',\n\n    // Callbacks\n    runCallbacksOnInit: true,\n  };\n\n  /* eslint no-param-reassign: \"off\" */\n\n  var prototypes = {\n    update: update,\n    translate: translate,\n    transition: transition$1,\n    slide: slide,\n    loop: loop,\n    grabCursor: grabCursor,\n    manipulation: manipulation,\n    events: events,\n    breakpoints: breakpoints,\n    checkOverflow: checkOverflow$1,\n    classes: classes,\n    images: images,\n  };\n\n  var extendedDefaults = {};\n\n  var Swiper = /*@__PURE__*/(function (SwiperClass) {\n    function Swiper() {\n      var assign;\n\n      var args = [], len = arguments.length;\n      while ( len-- ) args[ len ] = arguments[ len ];\n      var el;\n      var params;\n      if (args.length === 1 && args[0].constructor && args[0].constructor === Object) {\n        params = args[0];\n      } else {\n        (assign = args, el = assign[0], params = assign[1]);\n      }\n      if (!params) { params = {}; }\n\n      params = Utils.extend({}, params);\n      if (el && !params.el) { params.el = el; }\n\n      SwiperClass.call(this, params);\n\n      Object.keys(prototypes).forEach(function (prototypeGroup) {\n        Object.keys(prototypes[prototypeGroup]).forEach(function (protoMethod) {\n          if (!Swiper.prototype[protoMethod]) {\n            Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n          }\n        });\n      });\n\n      // Swiper Instance\n      var swiper = this;\n      if (typeof swiper.modules === 'undefined') {\n        swiper.modules = {};\n      }\n      Object.keys(swiper.modules).forEach(function (moduleName) {\n        var module = swiper.modules[moduleName];\n        if (module.params) {\n          var moduleParamName = Object.keys(module.params)[0];\n          var moduleParams = module.params[moduleParamName];\n          if (typeof moduleParams !== 'object' || moduleParams === null) { return; }\n          if (!(moduleParamName in params && 'enabled' in moduleParams)) { return; }\n          if (params[moduleParamName] === true) {\n            params[moduleParamName] = { enabled: true };\n          }\n          if (\n            typeof params[moduleParamName] === 'object'\n            && !('enabled' in params[moduleParamName])\n          ) {\n            params[moduleParamName].enabled = true;\n          }\n          if (!params[moduleParamName]) { params[moduleParamName] = { enabled: false }; }\n        }\n      });\n\n      // Extend defaults with modules params\n      var swiperParams = Utils.extend({}, defaults);\n      swiper.useModulesParams(swiperParams);\n\n      // Extend defaults with passed params\n      swiper.params = Utils.extend({}, swiperParams, extendedDefaults, params);\n      swiper.originalParams = Utils.extend({}, swiper.params);\n      swiper.passedParams = Utils.extend({}, params);\n\n      // Save Dom lib\n      swiper.$ = $;\n\n      // Find el\n      var $el = $(swiper.params.el);\n      el = $el[0];\n\n      if (!el) {\n        return undefined;\n      }\n\n      if ($el.length > 1) {\n        var swipers = [];\n        $el.each(function (index, containerEl) {\n          var newParams = Utils.extend({}, params, { el: containerEl });\n          swipers.push(new Swiper(newParams));\n        });\n        return swipers;\n      }\n\n      el.swiper = swiper;\n      $el.data('swiper', swiper);\n\n      // Find Wrapper\n      var $wrapperEl = $el.children((\".\" + (swiper.params.wrapperClass)));\n\n      // Extend Swiper\n      Utils.extend(swiper, {\n        $el: $el,\n        el: el,\n        $wrapperEl: $wrapperEl,\n        wrapperEl: $wrapperEl[0],\n\n        // Classes\n        classNames: [],\n\n        // Slides\n        slides: $(),\n        slidesGrid: [],\n        snapGrid: [],\n        slidesSizesGrid: [],\n\n        // isDirection\n        isHorizontal: function isHorizontal() {\n          return swiper.params.direction === 'horizontal';\n        },\n        isVertical: function isVertical() {\n          return swiper.params.direction === 'vertical';\n        },\n        // RTL\n        rtl: (el.dir.toLowerCase() === 'rtl' || $el.css('direction') === 'rtl'),\n        rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || $el.css('direction') === 'rtl'),\n        wrongRTL: $wrapperEl.css('display') === '-webkit-box',\n\n        // Indexes\n        activeIndex: 0,\n        realIndex: 0,\n\n        //\n        isBeginning: true,\n        isEnd: false,\n\n        // Props\n        translate: 0,\n        previousTranslate: 0,\n        progress: 0,\n        velocity: 0,\n        animating: false,\n\n        // Locks\n        allowSlideNext: swiper.params.allowSlideNext,\n        allowSlidePrev: swiper.params.allowSlidePrev,\n\n        // Touch Events\n        touchEvents: (function touchEvents() {\n          var touch = ['touchstart', 'touchmove', 'touchend'];\n          var desktop = ['mousedown', 'mousemove', 'mouseup'];\n          if (Support.pointerEvents) {\n            desktop = ['pointerdown', 'pointermove', 'pointerup'];\n          } else if (Support.prefixedPointerEvents) {\n            desktop = ['MSPointerDown', 'MSPointerMove', 'MSPointerUp'];\n          }\n          swiper.touchEventsTouch = {\n            start: touch[0],\n            move: touch[1],\n            end: touch[2],\n          };\n          swiper.touchEventsDesktop = {\n            start: desktop[0],\n            move: desktop[1],\n            end: desktop[2],\n          };\n          return Support.touch || !swiper.params.simulateTouch ? swiper.touchEventsTouch : swiper.touchEventsDesktop;\n        }()),\n        touchEventsData: {\n          isTouched: undefined,\n          isMoved: undefined,\n          allowTouchCallbacks: undefined,\n          touchStartTime: undefined,\n          isScrolling: undefined,\n          currentTranslate: undefined,\n          startTranslate: undefined,\n          allowThresholdMove: undefined,\n          // Form elements to match\n          formElements: 'input, select, option, textarea, button, video',\n          // Last click time\n          lastClickTime: Utils.now(),\n          clickTimeout: undefined,\n          // Velocities\n          velocities: [],\n          allowMomentumBounce: undefined,\n          isTouchEvent: undefined,\n          startMoving: undefined,\n        },\n\n        // Clicks\n        allowClick: true,\n\n        // Touches\n        allowTouchMove: swiper.params.allowTouchMove,\n\n        touches: {\n          startX: 0,\n          startY: 0,\n          currentX: 0,\n          currentY: 0,\n          diff: 0,\n        },\n\n        // Images\n        imagesToLoad: [],\n        imagesLoaded: 0,\n\n      });\n\n      // Install Modules\n      swiper.useModules();\n\n      // Init\n      if (swiper.params.init) {\n        swiper.init();\n      }\n\n      // Return app instance\n      return swiper;\n    }\n\n    if ( SwiperClass ) Swiper.__proto__ = SwiperClass;\n    Swiper.prototype = Object.create( SwiperClass && SwiperClass.prototype );\n    Swiper.prototype.constructor = Swiper;\n\n    var staticAccessors = { extendedDefaults: { configurable: true },defaults: { configurable: true },Class: { configurable: true },$: { configurable: true } };\n\n    Swiper.prototype.slidesPerViewDynamic = function slidesPerViewDynamic () {\n      var swiper = this;\n      var params = swiper.params;\n      var slides = swiper.slides;\n      var slidesGrid = swiper.slidesGrid;\n      var swiperSize = swiper.size;\n      var activeIndex = swiper.activeIndex;\n      var spv = 1;\n      if (params.centeredSlides) {\n        var slideSize = slides[activeIndex].swiperSlideSize;\n        var breakLoop;\n        for (var i = activeIndex + 1; i < slides.length; i += 1) {\n          if (slides[i] && !breakLoop) {\n            slideSize += slides[i].swiperSlideSize;\n            spv += 1;\n            if (slideSize > swiperSize) { breakLoop = true; }\n          }\n        }\n        for (var i$1 = activeIndex - 1; i$1 >= 0; i$1 -= 1) {\n          if (slides[i$1] && !breakLoop) {\n            slideSize += slides[i$1].swiperSlideSize;\n            spv += 1;\n            if (slideSize > swiperSize) { breakLoop = true; }\n          }\n        }\n      } else {\n        for (var i$2 = activeIndex + 1; i$2 < slides.length; i$2 += 1) {\n          if (slidesGrid[i$2] - slidesGrid[activeIndex] < swiperSize) {\n            spv += 1;\n          }\n        }\n      }\n      return spv;\n    };\n\n    Swiper.prototype.update = function update () {\n      var swiper = this;\n      if (!swiper || swiper.destroyed) { return; }\n      var snapGrid = swiper.snapGrid;\n      var params = swiper.params;\n      // Breakpoints\n      if (params.breakpoints) {\n        swiper.setBreakpoint();\n      }\n      swiper.updateSize();\n      swiper.updateSlides();\n      swiper.updateProgress();\n      swiper.updateSlidesClasses();\n\n      function setTranslate() {\n        var translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n        var newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n        swiper.setTranslate(newTranslate);\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n      }\n      var translated;\n      if (swiper.params.freeMode) {\n        setTranslate();\n        if (swiper.params.autoHeight) {\n          swiper.updateAutoHeight();\n        }\n      } else {\n        if ((swiper.params.slidesPerView === 'auto' || swiper.params.slidesPerView > 1) && swiper.isEnd && !swiper.params.centeredSlides) {\n          translated = swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n        } else {\n          translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n        }\n        if (!translated) {\n          setTranslate();\n        }\n      }\n      if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n        swiper.checkOverflow();\n      }\n      swiper.emit('update');\n    };\n\n    Swiper.prototype.changeDirection = function changeDirection (newDirection, needUpdate) {\n      if ( needUpdate === void 0 ) needUpdate = true;\n\n      var swiper = this;\n      var currentDirection = swiper.params.direction;\n      if (!newDirection) {\n        // eslint-disable-next-line\n        newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n      }\n      if ((newDirection === currentDirection) || (newDirection !== 'horizontal' && newDirection !== 'vertical')) {\n        return swiper;\n      }\n\n      if (currentDirection === 'vertical') {\n        swiper.$el\n          .removeClass(((swiper.params.containerModifierClass) + \"vertical wp8-vertical\"))\n          .addClass((\"\" + (swiper.params.containerModifierClass) + newDirection));\n\n        if ((Browser.isIE || Browser.isEdge) && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n          swiper.$el.addClass(((swiper.params.containerModifierClass) + \"wp8-\" + newDirection));\n        }\n      }\n      if (currentDirection === 'horizontal') {\n        swiper.$el\n          .removeClass(((swiper.params.containerModifierClass) + \"horizontal wp8-horizontal\"))\n          .addClass((\"\" + (swiper.params.containerModifierClass) + newDirection));\n\n        if ((Browser.isIE || Browser.isEdge) && (Support.pointerEvents || Support.prefixedPointerEvents)) {\n          swiper.$el.addClass(((swiper.params.containerModifierClass) + \"wp8-\" + newDirection));\n        }\n      }\n\n      swiper.params.direction = newDirection;\n\n      swiper.slides.each(function (slideIndex, slideEl) {\n        if (newDirection === 'vertical') {\n          slideEl.style.width = '';\n        } else {\n          slideEl.style.height = '';\n        }\n      });\n\n      swiper.emit('changeDirection');\n      if (needUpdate) { swiper.update(); }\n\n      return swiper;\n    };\n\n    Swiper.prototype.init = function init () {\n      var swiper = this;\n      if (swiper.initialized) { return; }\n\n      swiper.emit('beforeInit');\n\n      // Set breakpoint\n      if (swiper.params.breakpoints) {\n        swiper.setBreakpoint();\n      }\n\n      // Add Classes\n      swiper.addClasses();\n\n      // Create loop\n      if (swiper.params.loop) {\n        swiper.loopCreate();\n      }\n\n      // Update size\n      swiper.updateSize();\n\n      // Update slides\n      swiper.updateSlides();\n\n      if (swiper.params.watchOverflow) {\n        swiper.checkOverflow();\n      }\n\n      // Set Grab Cursor\n      if (swiper.params.grabCursor) {\n        swiper.setGrabCursor();\n      }\n\n      if (swiper.params.preloadImages) {\n        swiper.preloadImages();\n      }\n\n      // Slide To Initial Slide\n      if (swiper.params.loop) {\n        swiper.slideTo(swiper.params.initialSlide + swiper.loopedSlides, 0, swiper.params.runCallbacksOnInit);\n      } else {\n        swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit);\n      }\n\n      // Attach events\n      swiper.attachEvents();\n\n      // Init Flag\n      swiper.initialized = true;\n\n      // Emit\n      swiper.emit('init');\n    };\n\n    Swiper.prototype.destroy = function destroy (deleteInstance, cleanStyles) {\n      if ( deleteInstance === void 0 ) deleteInstance = true;\n      if ( cleanStyles === void 0 ) cleanStyles = true;\n\n      var swiper = this;\n      var params = swiper.params;\n      var $el = swiper.$el;\n      var $wrapperEl = swiper.$wrapperEl;\n      var slides = swiper.slides;\n\n      if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n        return null;\n      }\n\n      swiper.emit('beforeDestroy');\n\n      // Init Flag\n      swiper.initialized = false;\n\n      // Detach events\n      swiper.detachEvents();\n\n      // Destroy loop\n      if (params.loop) {\n        swiper.loopDestroy();\n      }\n\n      // Cleanup styles\n      if (cleanStyles) {\n        swiper.removeClasses();\n        $el.removeAttr('style');\n        $wrapperEl.removeAttr('style');\n        if (slides && slides.length) {\n          slides\n            .removeClass([\n              params.slideVisibleClass,\n              params.slideActiveClass,\n              params.slideNextClass,\n              params.slidePrevClass ].join(' '))\n            .removeAttr('style')\n            .removeAttr('data-swiper-slide-index')\n            .removeAttr('data-swiper-column')\n            .removeAttr('data-swiper-row');\n        }\n      }\n\n      swiper.emit('destroy');\n\n      // Detach emitter events\n      Object.keys(swiper.eventsListeners).forEach(function (eventName) {\n        swiper.off(eventName);\n      });\n\n      if (deleteInstance !== false) {\n        swiper.$el[0].swiper = null;\n        swiper.$el.data('swiper', null);\n        Utils.deleteProps(swiper);\n      }\n      swiper.destroyed = true;\n\n      return null;\n    };\n\n    Swiper.extendDefaults = function extendDefaults (newDefaults) {\n      Utils.extend(extendedDefaults, newDefaults);\n    };\n\n    staticAccessors.extendedDefaults.get = function () {\n      return extendedDefaults;\n    };\n\n    staticAccessors.defaults.get = function () {\n      return defaults;\n    };\n\n    staticAccessors.Class.get = function () {\n      return SwiperClass;\n    };\n\n    staticAccessors.$.get = function () {\n      return $;\n    };\n\n    Object.defineProperties( Swiper, staticAccessors );\n\n    return Swiper;\n  }(SwiperClass));\n\n  var Device$1 = {\n    name: 'device',\n    proto: {\n      device: Device,\n    },\n    static: {\n      device: Device,\n    },\n  };\n\n  var Support$1 = {\n    name: 'support',\n    proto: {\n      support: Support,\n    },\n    static: {\n      support: Support,\n    },\n  };\n\n  var Browser$1 = {\n    name: 'browser',\n    proto: {\n      browser: Browser,\n    },\n    static: {\n      browser: Browser,\n    },\n  };\n\n  var Resize = {\n    name: 'resize',\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        resize: {\n          resizeHandler: function resizeHandler() {\n            if (!swiper || swiper.destroyed || !swiper.initialized) { return; }\n            swiper.emit('beforeResize');\n            swiper.emit('resize');\n          },\n          orientationChangeHandler: function orientationChangeHandler() {\n            if (!swiper || swiper.destroyed || !swiper.initialized) { return; }\n            swiper.emit('orientationchange');\n          },\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        // Emit resize\n        win.addEventListener('resize', swiper.resize.resizeHandler);\n\n        // Emit orientationchange\n        win.addEventListener('orientationchange', swiper.resize.orientationChangeHandler);\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        win.removeEventListener('resize', swiper.resize.resizeHandler);\n        win.removeEventListener('orientationchange', swiper.resize.orientationChangeHandler);\n      },\n    },\n  };\n\n  var Observer = {\n    func: win.MutationObserver || win.WebkitMutationObserver,\n    attach: function attach(target, options) {\n      if ( options === void 0 ) options = {};\n\n      var swiper = this;\n\n      var ObserverFunc = Observer.func;\n      var observer = new ObserverFunc(function (mutations) {\n        // The observerUpdate event should only be triggered\n        // once despite the number of mutations.  Additional\n        // triggers are redundant and are very costly\n        if (mutations.length === 1) {\n          swiper.emit('observerUpdate', mutations[0]);\n          return;\n        }\n        var observerUpdate = function observerUpdate() {\n          swiper.emit('observerUpdate', mutations[0]);\n        };\n\n        if (win.requestAnimationFrame) {\n          win.requestAnimationFrame(observerUpdate);\n        } else {\n          win.setTimeout(observerUpdate, 0);\n        }\n      });\n\n      observer.observe(target, {\n        attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n        childList: typeof options.childList === 'undefined' ? true : options.childList,\n        characterData: typeof options.characterData === 'undefined' ? true : options.characterData,\n      });\n\n      swiper.observer.observers.push(observer);\n    },\n    init: function init() {\n      var swiper = this;\n      if (!Support.observer || !swiper.params.observer) { return; }\n      if (swiper.params.observeParents) {\n        var containerParents = swiper.$el.parents();\n        for (var i = 0; i < containerParents.length; i += 1) {\n          swiper.observer.attach(containerParents[i]);\n        }\n      }\n      // Observe container\n      swiper.observer.attach(swiper.$el[0], { childList: swiper.params.observeSlideChildren });\n\n      // Observe wrapper\n      swiper.observer.attach(swiper.$wrapperEl[0], { attributes: false });\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      swiper.observer.observers.forEach(function (observer) {\n        observer.disconnect();\n      });\n      swiper.observer.observers = [];\n    },\n  };\n\n  var Observer$1 = {\n    name: 'observer',\n    params: {\n      observer: false,\n      observeParents: false,\n      observeSlideChildren: false,\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        observer: {\n          init: Observer.init.bind(swiper),\n          attach: Observer.attach.bind(swiper),\n          destroy: Observer.destroy.bind(swiper),\n          observers: [],\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.observer.init();\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.observer.destroy();\n      },\n    },\n  };\n\n  var Virtual = {\n    update: function update(force) {\n      var swiper = this;\n      var ref = swiper.params;\n      var slidesPerView = ref.slidesPerView;\n      var slidesPerGroup = ref.slidesPerGroup;\n      var centeredSlides = ref.centeredSlides;\n      var ref$1 = swiper.params.virtual;\n      var addSlidesBefore = ref$1.addSlidesBefore;\n      var addSlidesAfter = ref$1.addSlidesAfter;\n      var ref$2 = swiper.virtual;\n      var previousFrom = ref$2.from;\n      var previousTo = ref$2.to;\n      var slides = ref$2.slides;\n      var previousSlidesGrid = ref$2.slidesGrid;\n      var renderSlide = ref$2.renderSlide;\n      var previousOffset = ref$2.offset;\n      swiper.updateActiveIndex();\n      var activeIndex = swiper.activeIndex || 0;\n\n      var offsetProp;\n      if (swiper.rtlTranslate) { offsetProp = 'right'; }\n      else { offsetProp = swiper.isHorizontal() ? 'left' : 'top'; }\n\n      var slidesAfter;\n      var slidesBefore;\n      if (centeredSlides) {\n        slidesAfter = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesBefore;\n        slidesBefore = Math.floor(slidesPerView / 2) + slidesPerGroup + addSlidesAfter;\n      } else {\n        slidesAfter = slidesPerView + (slidesPerGroup - 1) + addSlidesBefore;\n        slidesBefore = slidesPerGroup + addSlidesAfter;\n      }\n      var from = Math.max((activeIndex || 0) - slidesBefore, 0);\n      var to = Math.min((activeIndex || 0) + slidesAfter, slides.length - 1);\n      var offset = (swiper.slidesGrid[from] || 0) - (swiper.slidesGrid[0] || 0);\n\n      Utils.extend(swiper.virtual, {\n        from: from,\n        to: to,\n        offset: offset,\n        slidesGrid: swiper.slidesGrid,\n      });\n\n      function onRendered() {\n        swiper.updateSlides();\n        swiper.updateProgress();\n        swiper.updateSlidesClasses();\n        if (swiper.lazy && swiper.params.lazy.enabled) {\n          swiper.lazy.load();\n        }\n      }\n\n      if (previousFrom === from && previousTo === to && !force) {\n        if (swiper.slidesGrid !== previousSlidesGrid && offset !== previousOffset) {\n          swiper.slides.css(offsetProp, (offset + \"px\"));\n        }\n        swiper.updateProgress();\n        return;\n      }\n      if (swiper.params.virtual.renderExternal) {\n        swiper.params.virtual.renderExternal.call(swiper, {\n          offset: offset,\n          from: from,\n          to: to,\n          slides: (function getSlides() {\n            var slidesToRender = [];\n            for (var i = from; i <= to; i += 1) {\n              slidesToRender.push(slides[i]);\n            }\n            return slidesToRender;\n          }()),\n        });\n        onRendered();\n        return;\n      }\n      var prependIndexes = [];\n      var appendIndexes = [];\n      if (force) {\n        swiper.$wrapperEl.find((\".\" + (swiper.params.slideClass))).remove();\n      } else {\n        for (var i = previousFrom; i <= previousTo; i += 1) {\n          if (i < from || i > to) {\n            swiper.$wrapperEl.find((\".\" + (swiper.params.slideClass) + \"[data-swiper-slide-index=\\\"\" + i + \"\\\"]\")).remove();\n          }\n        }\n      }\n      for (var i$1 = 0; i$1 < slides.length; i$1 += 1) {\n        if (i$1 >= from && i$1 <= to) {\n          if (typeof previousTo === 'undefined' || force) {\n            appendIndexes.push(i$1);\n          } else {\n            if (i$1 > previousTo) { appendIndexes.push(i$1); }\n            if (i$1 < previousFrom) { prependIndexes.push(i$1); }\n          }\n        }\n      }\n      appendIndexes.forEach(function (index) {\n        swiper.$wrapperEl.append(renderSlide(slides[index], index));\n      });\n      prependIndexes.sort(function (a, b) { return b - a; }).forEach(function (index) {\n        swiper.$wrapperEl.prepend(renderSlide(slides[index], index));\n      });\n      swiper.$wrapperEl.children('.swiper-slide').css(offsetProp, (offset + \"px\"));\n      onRendered();\n    },\n    renderSlide: function renderSlide(slide, index) {\n      var swiper = this;\n      var params = swiper.params.virtual;\n      if (params.cache && swiper.virtual.cache[index]) {\n        return swiper.virtual.cache[index];\n      }\n      var $slideEl = params.renderSlide\n        ? $(params.renderSlide.call(swiper, slide, index))\n        : $((\"<div class=\\\"\" + (swiper.params.slideClass) + \"\\\" data-swiper-slide-index=\\\"\" + index + \"\\\">\" + slide + \"</div>\"));\n      if (!$slideEl.attr('data-swiper-slide-index')) { $slideEl.attr('data-swiper-slide-index', index); }\n      if (params.cache) { swiper.virtual.cache[index] = $slideEl; }\n      return $slideEl;\n    },\n    appendSlide: function appendSlide(slides) {\n      var swiper = this;\n      if (typeof slides === 'object' && 'length' in slides) {\n        for (var i = 0; i < slides.length; i += 1) {\n          if (slides[i]) { swiper.virtual.slides.push(slides[i]); }\n        }\n      } else {\n        swiper.virtual.slides.push(slides);\n      }\n      swiper.virtual.update(true);\n    },\n    prependSlide: function prependSlide(slides) {\n      var swiper = this;\n      var activeIndex = swiper.activeIndex;\n      var newActiveIndex = activeIndex + 1;\n      var numberOfNewSlides = 1;\n\n      if (Array.isArray(slides)) {\n        for (var i = 0; i < slides.length; i += 1) {\n          if (slides[i]) { swiper.virtual.slides.unshift(slides[i]); }\n        }\n        newActiveIndex = activeIndex + slides.length;\n        numberOfNewSlides = slides.length;\n      } else {\n        swiper.virtual.slides.unshift(slides);\n      }\n      if (swiper.params.virtual.cache) {\n        var cache = swiper.virtual.cache;\n        var newCache = {};\n        Object.keys(cache).forEach(function (cachedIndex) {\n          newCache[parseInt(cachedIndex, 10) + numberOfNewSlides] = cache[cachedIndex];\n        });\n        swiper.virtual.cache = newCache;\n      }\n      swiper.virtual.update(true);\n      swiper.slideTo(newActiveIndex, 0);\n    },\n    removeSlide: function removeSlide(slidesIndexes) {\n      var swiper = this;\n      if (typeof slidesIndexes === 'undefined' || slidesIndexes === null) { return; }\n      var activeIndex = swiper.activeIndex;\n      if (Array.isArray(slidesIndexes)) {\n        for (var i = slidesIndexes.length - 1; i >= 0; i -= 1) {\n          swiper.virtual.slides.splice(slidesIndexes[i], 1);\n          if (swiper.params.virtual.cache) {\n            delete swiper.virtual.cache[slidesIndexes[i]];\n          }\n          if (slidesIndexes[i] < activeIndex) { activeIndex -= 1; }\n          activeIndex = Math.max(activeIndex, 0);\n        }\n      } else {\n        swiper.virtual.slides.splice(slidesIndexes, 1);\n        if (swiper.params.virtual.cache) {\n          delete swiper.virtual.cache[slidesIndexes];\n        }\n        if (slidesIndexes < activeIndex) { activeIndex -= 1; }\n        activeIndex = Math.max(activeIndex, 0);\n      }\n      swiper.virtual.update(true);\n      swiper.slideTo(activeIndex, 0);\n    },\n    removeAllSlides: function removeAllSlides() {\n      var swiper = this;\n      swiper.virtual.slides = [];\n      if (swiper.params.virtual.cache) {\n        swiper.virtual.cache = {};\n      }\n      swiper.virtual.update(true);\n      swiper.slideTo(0, 0);\n    },\n  };\n\n  var Virtual$1 = {\n    name: 'virtual',\n    params: {\n      virtual: {\n        enabled: false,\n        slides: [],\n        cache: true,\n        renderSlide: null,\n        renderExternal: null,\n        addSlidesBefore: 0,\n        addSlidesAfter: 0,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        virtual: {\n          update: Virtual.update.bind(swiper),\n          appendSlide: Virtual.appendSlide.bind(swiper),\n          prependSlide: Virtual.prependSlide.bind(swiper),\n          removeSlide: Virtual.removeSlide.bind(swiper),\n          removeAllSlides: Virtual.removeAllSlides.bind(swiper),\n          renderSlide: Virtual.renderSlide.bind(swiper),\n          slides: swiper.params.virtual.slides,\n          cache: {},\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (!swiper.params.virtual.enabled) { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"virtual\"));\n        var overwriteParams = {\n          watchSlidesProgress: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n\n        if (!swiper.params.initialSlide) {\n          swiper.virtual.update();\n        }\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (!swiper.params.virtual.enabled) { return; }\n        swiper.virtual.update();\n      },\n    },\n  };\n\n  var Keyboard = {\n    handle: function handle(event) {\n      var swiper = this;\n      var rtl = swiper.rtlTranslate;\n      var e = event;\n      if (e.originalEvent) { e = e.originalEvent; } // jquery fix\n      var kc = e.keyCode || e.charCode;\n      // Directions locks\n      if (!swiper.allowSlideNext && ((swiper.isHorizontal() && kc === 39) || (swiper.isVertical() && kc === 40))) {\n        return false;\n      }\n      if (!swiper.allowSlidePrev && ((swiper.isHorizontal() && kc === 37) || (swiper.isVertical() && kc === 38))) {\n        return false;\n      }\n      if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n        return undefined;\n      }\n      if (doc.activeElement && doc.activeElement.nodeName && (doc.activeElement.nodeName.toLowerCase() === 'input' || doc.activeElement.nodeName.toLowerCase() === 'textarea')) {\n        return undefined;\n      }\n      if (swiper.params.keyboard.onlyInViewport && (kc === 37 || kc === 39 || kc === 38 || kc === 40)) {\n        var inView = false;\n        // Check that swiper should be inside of visible area of window\n        if (swiper.$el.parents((\".\" + (swiper.params.slideClass))).length > 0 && swiper.$el.parents((\".\" + (swiper.params.slideActiveClass))).length === 0) {\n          return undefined;\n        }\n        var windowWidth = win.innerWidth;\n        var windowHeight = win.innerHeight;\n        var swiperOffset = swiper.$el.offset();\n        if (rtl) { swiperOffset.left -= swiper.$el[0].scrollLeft; }\n        var swiperCoord = [\n          [swiperOffset.left, swiperOffset.top],\n          [swiperOffset.left + swiper.width, swiperOffset.top],\n          [swiperOffset.left, swiperOffset.top + swiper.height],\n          [swiperOffset.left + swiper.width, swiperOffset.top + swiper.height] ];\n        for (var i = 0; i < swiperCoord.length; i += 1) {\n          var point = swiperCoord[i];\n          if (\n            point[0] >= 0 && point[0] <= windowWidth\n            && point[1] >= 0 && point[1] <= windowHeight\n          ) {\n            inView = true;\n          }\n        }\n        if (!inView) { return undefined; }\n      }\n      if (swiper.isHorizontal()) {\n        if (kc === 37 || kc === 39) {\n          if (e.preventDefault) { e.preventDefault(); }\n          else { e.returnValue = false; }\n        }\n        if ((kc === 39 && !rtl) || (kc === 37 && rtl)) { swiper.slideNext(); }\n        if ((kc === 37 && !rtl) || (kc === 39 && rtl)) { swiper.slidePrev(); }\n      } else {\n        if (kc === 38 || kc === 40) {\n          if (e.preventDefault) { e.preventDefault(); }\n          else { e.returnValue = false; }\n        }\n        if (kc === 40) { swiper.slideNext(); }\n        if (kc === 38) { swiper.slidePrev(); }\n      }\n      swiper.emit('keyPress', kc);\n      return undefined;\n    },\n    enable: function enable() {\n      var swiper = this;\n      if (swiper.keyboard.enabled) { return; }\n      $(doc).on('keydown', swiper.keyboard.handle);\n      swiper.keyboard.enabled = true;\n    },\n    disable: function disable() {\n      var swiper = this;\n      if (!swiper.keyboard.enabled) { return; }\n      $(doc).off('keydown', swiper.keyboard.handle);\n      swiper.keyboard.enabled = false;\n    },\n  };\n\n  var Keyboard$1 = {\n    name: 'keyboard',\n    params: {\n      keyboard: {\n        enabled: false,\n        onlyInViewport: true,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        keyboard: {\n          enabled: false,\n          enable: Keyboard.enable.bind(swiper),\n          disable: Keyboard.disable.bind(swiper),\n          handle: Keyboard.handle.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.keyboard.enabled) {\n          swiper.keyboard.enable();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.keyboard.enabled) {\n          swiper.keyboard.disable();\n        }\n      },\n    },\n  };\n\n  function isEventSupported() {\n    var eventName = 'onwheel';\n    var isSupported = eventName in doc;\n\n    if (!isSupported) {\n      var element = doc.createElement('div');\n      element.setAttribute(eventName, 'return;');\n      isSupported = typeof element[eventName] === 'function';\n    }\n\n    if (!isSupported\n      && doc.implementation\n      && doc.implementation.hasFeature\n      // always returns true in newer browsers as per the standard.\n      // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature\n      && doc.implementation.hasFeature('', '') !== true\n    ) {\n      // This is the only way to test support for the `wheel` event in IE9+.\n      isSupported = doc.implementation.hasFeature('Events.wheel', '3.0');\n    }\n\n    return isSupported;\n  }\n  var Mousewheel = {\n    lastScrollTime: Utils.now(),\n    event: (function getEvent() {\n      if (win.navigator.userAgent.indexOf('firefox') > -1) { return 'DOMMouseScroll'; }\n      return isEventSupported() ? 'wheel' : 'mousewheel';\n    }()),\n    normalize: function normalize(e) {\n      // Reasonable defaults\n      var PIXEL_STEP = 10;\n      var LINE_HEIGHT = 40;\n      var PAGE_HEIGHT = 800;\n\n      var sX = 0;\n      var sY = 0; // spinX, spinY\n      var pX = 0;\n      var pY = 0; // pixelX, pixelY\n\n      // Legacy\n      if ('detail' in e) {\n        sY = e.detail;\n      }\n      if ('wheelDelta' in e) {\n        sY = -e.wheelDelta / 120;\n      }\n      if ('wheelDeltaY' in e) {\n        sY = -e.wheelDeltaY / 120;\n      }\n      if ('wheelDeltaX' in e) {\n        sX = -e.wheelDeltaX / 120;\n      }\n\n      // side scrolling on FF with DOMMouseScroll\n      if ('axis' in e && e.axis === e.HORIZONTAL_AXIS) {\n        sX = sY;\n        sY = 0;\n      }\n\n      pX = sX * PIXEL_STEP;\n      pY = sY * PIXEL_STEP;\n\n      if ('deltaY' in e) {\n        pY = e.deltaY;\n      }\n      if ('deltaX' in e) {\n        pX = e.deltaX;\n      }\n\n      if ((pX || pY) && e.deltaMode) {\n        if (e.deltaMode === 1) { // delta in LINE units\n          pX *= LINE_HEIGHT;\n          pY *= LINE_HEIGHT;\n        } else { // delta in PAGE units\n          pX *= PAGE_HEIGHT;\n          pY *= PAGE_HEIGHT;\n        }\n      }\n\n      // Fall-back if spin cannot be determined\n      if (pX && !sX) {\n        sX = (pX < 1) ? -1 : 1;\n      }\n      if (pY && !sY) {\n        sY = (pY < 1) ? -1 : 1;\n      }\n\n      return {\n        spinX: sX,\n        spinY: sY,\n        pixelX: pX,\n        pixelY: pY,\n      };\n    },\n    handleMouseEnter: function handleMouseEnter() {\n      var swiper = this;\n      swiper.mouseEntered = true;\n    },\n    handleMouseLeave: function handleMouseLeave() {\n      var swiper = this;\n      swiper.mouseEntered = false;\n    },\n    handle: function handle(event) {\n      var e = event;\n      var swiper = this;\n      var params = swiper.params.mousewheel;\n\n      if (!swiper.mouseEntered && !params.releaseOnEdges) { return true; }\n\n      if (e.originalEvent) { e = e.originalEvent; } // jquery fix\n      var delta = 0;\n      var rtlFactor = swiper.rtlTranslate ? -1 : 1;\n\n      var data = Mousewheel.normalize(e);\n\n      if (params.forceToAxis) {\n        if (swiper.isHorizontal()) {\n          if (Math.abs(data.pixelX) > Math.abs(data.pixelY)) { delta = data.pixelX * rtlFactor; }\n          else { return true; }\n        } else if (Math.abs(data.pixelY) > Math.abs(data.pixelX)) { delta = data.pixelY; }\n        else { return true; }\n      } else {\n        delta = Math.abs(data.pixelX) > Math.abs(data.pixelY) ? -data.pixelX * rtlFactor : -data.pixelY;\n      }\n\n      if (delta === 0) { return true; }\n\n      if (params.invert) { delta = -delta; }\n\n      if (!swiper.params.freeMode) {\n        if (Utils.now() - swiper.mousewheel.lastScrollTime > 60) {\n          if (delta < 0) {\n            if ((!swiper.isEnd || swiper.params.loop) && !swiper.animating) {\n              swiper.slideNext();\n              swiper.emit('scroll', e);\n            } else if (params.releaseOnEdges) { return true; }\n          } else if ((!swiper.isBeginning || swiper.params.loop) && !swiper.animating) {\n            swiper.slidePrev();\n            swiper.emit('scroll', e);\n          } else if (params.releaseOnEdges) { return true; }\n        }\n        swiper.mousewheel.lastScrollTime = (new win.Date()).getTime();\n      } else {\n        // Freemode or scrollContainer:\n        if (swiper.params.loop) {\n          swiper.loopFix();\n        }\n        var position = swiper.getTranslate() + (delta * params.sensitivity);\n        var wasBeginning = swiper.isBeginning;\n        var wasEnd = swiper.isEnd;\n\n        if (position >= swiper.minTranslate()) { position = swiper.minTranslate(); }\n        if (position <= swiper.maxTranslate()) { position = swiper.maxTranslate(); }\n\n        swiper.setTransition(0);\n        swiper.setTranslate(position);\n        swiper.updateProgress();\n        swiper.updateActiveIndex();\n        swiper.updateSlidesClasses();\n\n        if ((!wasBeginning && swiper.isBeginning) || (!wasEnd && swiper.isEnd)) {\n          swiper.updateSlidesClasses();\n        }\n\n        if (swiper.params.freeModeSticky) {\n          clearTimeout(swiper.mousewheel.timeout);\n          swiper.mousewheel.timeout = Utils.nextTick(function () {\n            swiper.slideToClosest();\n          }, 300);\n        }\n        // Emit event\n        swiper.emit('scroll', e);\n\n        // Stop autoplay\n        if (swiper.params.autoplay && swiper.params.autoplayDisableOnInteraction) { swiper.autoplay.stop(); }\n        // Return page scroll on edge positions\n        if (position === swiper.minTranslate() || position === swiper.maxTranslate()) { return true; }\n      }\n\n      if (e.preventDefault) { e.preventDefault(); }\n      else { e.returnValue = false; }\n      return false;\n    },\n    enable: function enable() {\n      var swiper = this;\n      if (!Mousewheel.event) { return false; }\n      if (swiper.mousewheel.enabled) { return false; }\n      var target = swiper.$el;\n      if (swiper.params.mousewheel.eventsTarged !== 'container') {\n        target = $(swiper.params.mousewheel.eventsTarged);\n      }\n      target.on('mouseenter', swiper.mousewheel.handleMouseEnter);\n      target.on('mouseleave', swiper.mousewheel.handleMouseLeave);\n      target.on(Mousewheel.event, swiper.mousewheel.handle);\n      swiper.mousewheel.enabled = true;\n      return true;\n    },\n    disable: function disable() {\n      var swiper = this;\n      if (!Mousewheel.event) { return false; }\n      if (!swiper.mousewheel.enabled) { return false; }\n      var target = swiper.$el;\n      if (swiper.params.mousewheel.eventsTarged !== 'container') {\n        target = $(swiper.params.mousewheel.eventsTarged);\n      }\n      target.off(Mousewheel.event, swiper.mousewheel.handle);\n      swiper.mousewheel.enabled = false;\n      return true;\n    },\n  };\n\n  var Mousewheel$1 = {\n    name: 'mousewheel',\n    params: {\n      mousewheel: {\n        enabled: false,\n        releaseOnEdges: false,\n        invert: false,\n        forceToAxis: false,\n        sensitivity: 1,\n        eventsTarged: 'container',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        mousewheel: {\n          enabled: false,\n          enable: Mousewheel.enable.bind(swiper),\n          disable: Mousewheel.disable.bind(swiper),\n          handle: Mousewheel.handle.bind(swiper),\n          handleMouseEnter: Mousewheel.handleMouseEnter.bind(swiper),\n          handleMouseLeave: Mousewheel.handleMouseLeave.bind(swiper),\n          lastScrollTime: Utils.now(),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.mousewheel.enabled) { swiper.mousewheel.enable(); }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.mousewheel.enabled) { swiper.mousewheel.disable(); }\n      },\n    },\n  };\n\n  var Navigation = {\n    update: function update() {\n      // Update Navigation Buttons\n      var swiper = this;\n      var params = swiper.params.navigation;\n\n      if (swiper.params.loop) { return; }\n      var ref = swiper.navigation;\n      var $nextEl = ref.$nextEl;\n      var $prevEl = ref.$prevEl;\n\n      if ($prevEl && $prevEl.length > 0) {\n        if (swiper.isBeginning) {\n          $prevEl.addClass(params.disabledClass);\n        } else {\n          $prevEl.removeClass(params.disabledClass);\n        }\n        $prevEl[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n      }\n      if ($nextEl && $nextEl.length > 0) {\n        if (swiper.isEnd) {\n          $nextEl.addClass(params.disabledClass);\n        } else {\n          $nextEl.removeClass(params.disabledClass);\n        }\n        $nextEl[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n      }\n    },\n    onPrevClick: function onPrevClick(e) {\n      var swiper = this;\n      e.preventDefault();\n      if (swiper.isBeginning && !swiper.params.loop) { return; }\n      swiper.slidePrev();\n    },\n    onNextClick: function onNextClick(e) {\n      var swiper = this;\n      e.preventDefault();\n      if (swiper.isEnd && !swiper.params.loop) { return; }\n      swiper.slideNext();\n    },\n    init: function init() {\n      var swiper = this;\n      var params = swiper.params.navigation;\n      if (!(params.nextEl || params.prevEl)) { return; }\n\n      var $nextEl;\n      var $prevEl;\n      if (params.nextEl) {\n        $nextEl = $(params.nextEl);\n        if (\n          swiper.params.uniqueNavElements\n          && typeof params.nextEl === 'string'\n          && $nextEl.length > 1\n          && swiper.$el.find(params.nextEl).length === 1\n        ) {\n          $nextEl = swiper.$el.find(params.nextEl);\n        }\n      }\n      if (params.prevEl) {\n        $prevEl = $(params.prevEl);\n        if (\n          swiper.params.uniqueNavElements\n          && typeof params.prevEl === 'string'\n          && $prevEl.length > 1\n          && swiper.$el.find(params.prevEl).length === 1\n        ) {\n          $prevEl = swiper.$el.find(params.prevEl);\n        }\n      }\n\n      if ($nextEl && $nextEl.length > 0) {\n        $nextEl.on('click', swiper.navigation.onNextClick);\n      }\n      if ($prevEl && $prevEl.length > 0) {\n        $prevEl.on('click', swiper.navigation.onPrevClick);\n      }\n\n      Utils.extend(swiper.navigation, {\n        $nextEl: $nextEl,\n        nextEl: $nextEl && $nextEl[0],\n        $prevEl: $prevEl,\n        prevEl: $prevEl && $prevEl[0],\n      });\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      var ref = swiper.navigation;\n      var $nextEl = ref.$nextEl;\n      var $prevEl = ref.$prevEl;\n      if ($nextEl && $nextEl.length) {\n        $nextEl.off('click', swiper.navigation.onNextClick);\n        $nextEl.removeClass(swiper.params.navigation.disabledClass);\n      }\n      if ($prevEl && $prevEl.length) {\n        $prevEl.off('click', swiper.navigation.onPrevClick);\n        $prevEl.removeClass(swiper.params.navigation.disabledClass);\n      }\n    },\n  };\n\n  var Navigation$1 = {\n    name: 'navigation',\n    params: {\n      navigation: {\n        nextEl: null,\n        prevEl: null,\n\n        hideOnClick: false,\n        disabledClass: 'swiper-button-disabled',\n        hiddenClass: 'swiper-button-hidden',\n        lockClass: 'swiper-button-lock',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        navigation: {\n          init: Navigation.init.bind(swiper),\n          update: Navigation.update.bind(swiper),\n          destroy: Navigation.destroy.bind(swiper),\n          onNextClick: Navigation.onNextClick.bind(swiper),\n          onPrevClick: Navigation.onPrevClick.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.navigation.init();\n        swiper.navigation.update();\n      },\n      toEdge: function toEdge() {\n        var swiper = this;\n        swiper.navigation.update();\n      },\n      fromEdge: function fromEdge() {\n        var swiper = this;\n        swiper.navigation.update();\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.navigation.destroy();\n      },\n      click: function click(e) {\n        var swiper = this;\n        var ref = swiper.navigation;\n        var $nextEl = ref.$nextEl;\n        var $prevEl = ref.$prevEl;\n        if (\n          swiper.params.navigation.hideOnClick\n          && !$(e.target).is($prevEl)\n          && !$(e.target).is($nextEl)\n        ) {\n          var isHidden;\n          if ($nextEl) {\n            isHidden = $nextEl.hasClass(swiper.params.navigation.hiddenClass);\n          } else if ($prevEl) {\n            isHidden = $prevEl.hasClass(swiper.params.navigation.hiddenClass);\n          }\n          if (isHidden === true) {\n            swiper.emit('navigationShow', swiper);\n          } else {\n            swiper.emit('navigationHide', swiper);\n          }\n          if ($nextEl) {\n            $nextEl.toggleClass(swiper.params.navigation.hiddenClass);\n          }\n          if ($prevEl) {\n            $prevEl.toggleClass(swiper.params.navigation.hiddenClass);\n          }\n        }\n      },\n    },\n  };\n\n  var Pagination = {\n    update: function update() {\n      // Render || Update Pagination bullets/items\n      var swiper = this;\n      var rtl = swiper.rtl;\n      var params = swiper.params.pagination;\n      if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) { return; }\n      var slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n      var $el = swiper.pagination.$el;\n      // Current/Total\n      var current;\n      var total = swiper.params.loop ? Math.ceil((slidesLength - (swiper.loopedSlides * 2)) / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.loop) {\n        current = Math.ceil((swiper.activeIndex - swiper.loopedSlides) / swiper.params.slidesPerGroup);\n        if (current > slidesLength - 1 - (swiper.loopedSlides * 2)) {\n          current -= (slidesLength - (swiper.loopedSlides * 2));\n        }\n        if (current > total - 1) { current -= total; }\n        if (current < 0 && swiper.params.paginationType !== 'bullets') { current = total + current; }\n      } else if (typeof swiper.snapIndex !== 'undefined') {\n        current = swiper.snapIndex;\n      } else {\n        current = swiper.activeIndex || 0;\n      }\n      // Types\n      if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n        var bullets = swiper.pagination.bullets;\n        var firstIndex;\n        var lastIndex;\n        var midIndex;\n        if (params.dynamicBullets) {\n          swiper.pagination.bulletSize = bullets.eq(0)[swiper.isHorizontal() ? 'outerWidth' : 'outerHeight'](true);\n          $el.css(swiper.isHorizontal() ? 'width' : 'height', ((swiper.pagination.bulletSize * (params.dynamicMainBullets + 4)) + \"px\"));\n          if (params.dynamicMainBullets > 1 && swiper.previousIndex !== undefined) {\n            swiper.pagination.dynamicBulletIndex += (current - swiper.previousIndex);\n            if (swiper.pagination.dynamicBulletIndex > (params.dynamicMainBullets - 1)) {\n              swiper.pagination.dynamicBulletIndex = params.dynamicMainBullets - 1;\n            } else if (swiper.pagination.dynamicBulletIndex < 0) {\n              swiper.pagination.dynamicBulletIndex = 0;\n            }\n          }\n          firstIndex = current - swiper.pagination.dynamicBulletIndex;\n          lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n          midIndex = (lastIndex + firstIndex) / 2;\n        }\n        bullets.removeClass(((params.bulletActiveClass) + \" \" + (params.bulletActiveClass) + \"-next \" + (params.bulletActiveClass) + \"-next-next \" + (params.bulletActiveClass) + \"-prev \" + (params.bulletActiveClass) + \"-prev-prev \" + (params.bulletActiveClass) + \"-main\"));\n        if ($el.length > 1) {\n          bullets.each(function (index, bullet) {\n            var $bullet = $(bullet);\n            var bulletIndex = $bullet.index();\n            if (bulletIndex === current) {\n              $bullet.addClass(params.bulletActiveClass);\n            }\n            if (params.dynamicBullets) {\n              if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n                $bullet.addClass(((params.bulletActiveClass) + \"-main\"));\n              }\n              if (bulletIndex === firstIndex) {\n                $bullet\n                  .prev()\n                  .addClass(((params.bulletActiveClass) + \"-prev\"))\n                  .prev()\n                  .addClass(((params.bulletActiveClass) + \"-prev-prev\"));\n              }\n              if (bulletIndex === lastIndex) {\n                $bullet\n                  .next()\n                  .addClass(((params.bulletActiveClass) + \"-next\"))\n                  .next()\n                  .addClass(((params.bulletActiveClass) + \"-next-next\"));\n              }\n            }\n          });\n        } else {\n          var $bullet = bullets.eq(current);\n          $bullet.addClass(params.bulletActiveClass);\n          if (params.dynamicBullets) {\n            var $firstDisplayedBullet = bullets.eq(firstIndex);\n            var $lastDisplayedBullet = bullets.eq(lastIndex);\n            for (var i = firstIndex; i <= lastIndex; i += 1) {\n              bullets.eq(i).addClass(((params.bulletActiveClass) + \"-main\"));\n            }\n            $firstDisplayedBullet\n              .prev()\n              .addClass(((params.bulletActiveClass) + \"-prev\"))\n              .prev()\n              .addClass(((params.bulletActiveClass) + \"-prev-prev\"));\n            $lastDisplayedBullet\n              .next()\n              .addClass(((params.bulletActiveClass) + \"-next\"))\n              .next()\n              .addClass(((params.bulletActiveClass) + \"-next-next\"));\n          }\n        }\n        if (params.dynamicBullets) {\n          var dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n          var bulletsOffset = (((swiper.pagination.bulletSize * dynamicBulletsLength) - (swiper.pagination.bulletSize)) / 2) - (midIndex * swiper.pagination.bulletSize);\n          var offsetProp = rtl ? 'right' : 'left';\n          bullets.css(swiper.isHorizontal() ? offsetProp : 'top', (bulletsOffset + \"px\"));\n        }\n      }\n      if (params.type === 'fraction') {\n        $el.find((\".\" + (params.currentClass))).text(params.formatFractionCurrent(current + 1));\n        $el.find((\".\" + (params.totalClass))).text(params.formatFractionTotal(total));\n      }\n      if (params.type === 'progressbar') {\n        var progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        var scale = (current + 1) / total;\n        var scaleX = 1;\n        var scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        $el.find((\".\" + (params.progressbarFillClass))).transform((\"translate3d(0,0,0) scaleX(\" + scaleX + \") scaleY(\" + scaleY + \")\")).transition(swiper.params.speed);\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        $el.html(params.renderCustom(swiper, current + 1, total));\n        swiper.emit('paginationRender', swiper, $el[0]);\n      } else {\n        swiper.emit('paginationUpdate', swiper, $el[0]);\n      }\n      $el[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n    },\n    render: function render() {\n      // Render Container\n      var swiper = this;\n      var params = swiper.params.pagination;\n      if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) { return; }\n      var slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n\n      var $el = swiper.pagination.$el;\n      var paginationHTML = '';\n      if (params.type === 'bullets') {\n        var numberOfBullets = swiper.params.loop ? Math.ceil((slidesLength - (swiper.loopedSlides * 2)) / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n        for (var i = 0; i < numberOfBullets; i += 1) {\n          if (params.renderBullet) {\n            paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n          } else {\n            paginationHTML += \"<\" + (params.bulletElement) + \" class=\\\"\" + (params.bulletClass) + \"\\\"></\" + (params.bulletElement) + \">\";\n          }\n        }\n        $el.html(paginationHTML);\n        swiper.pagination.bullets = $el.find((\".\" + (params.bulletClass)));\n      }\n      if (params.type === 'fraction') {\n        if (params.renderFraction) {\n          paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n        } else {\n          paginationHTML = \"<span class=\\\"\" + (params.currentClass) + \"\\\"></span>\"\n          + ' / '\n          + \"<span class=\\\"\" + (params.totalClass) + \"\\\"></span>\";\n        }\n        $el.html(paginationHTML);\n      }\n      if (params.type === 'progressbar') {\n        if (params.renderProgressbar) {\n          paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n        } else {\n          paginationHTML = \"<span class=\\\"\" + (params.progressbarFillClass) + \"\\\"></span>\";\n        }\n        $el.html(paginationHTML);\n      }\n      if (params.type !== 'custom') {\n        swiper.emit('paginationRender', swiper.pagination.$el[0]);\n      }\n    },\n    init: function init() {\n      var swiper = this;\n      var params = swiper.params.pagination;\n      if (!params.el) { return; }\n\n      var $el = $(params.el);\n      if ($el.length === 0) { return; }\n\n      if (\n        swiper.params.uniqueNavElements\n        && typeof params.el === 'string'\n        && $el.length > 1\n        && swiper.$el.find(params.el).length === 1\n      ) {\n        $el = swiper.$el.find(params.el);\n      }\n\n      if (params.type === 'bullets' && params.clickable) {\n        $el.addClass(params.clickableClass);\n      }\n\n      $el.addClass(params.modifierClass + params.type);\n\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        $el.addClass((\"\" + (params.modifierClass) + (params.type) + \"-dynamic\"));\n        swiper.pagination.dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        $el.addClass(params.progressbarOppositeClass);\n      }\n\n      if (params.clickable) {\n        $el.on('click', (\".\" + (params.bulletClass)), function onClick(e) {\n          e.preventDefault();\n          var index = $(this).index() * swiper.params.slidesPerGroup;\n          if (swiper.params.loop) { index += swiper.loopedSlides; }\n          swiper.slideTo(index);\n        });\n      }\n\n      Utils.extend(swiper.pagination, {\n        $el: $el,\n        el: $el[0],\n      });\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      var params = swiper.params.pagination;\n      if (!params.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0) { return; }\n      var $el = swiper.pagination.$el;\n\n      $el.removeClass(params.hiddenClass);\n      $el.removeClass(params.modifierClass + params.type);\n      if (swiper.pagination.bullets) { swiper.pagination.bullets.removeClass(params.bulletActiveClass); }\n      if (params.clickable) {\n        $el.off('click', (\".\" + (params.bulletClass)));\n      }\n    },\n  };\n\n  var Pagination$1 = {\n    name: 'pagination',\n    params: {\n      pagination: {\n        el: null,\n        bulletElement: 'span',\n        clickable: false,\n        hideOnClick: false,\n        renderBullet: null,\n        renderProgressbar: null,\n        renderFraction: null,\n        renderCustom: null,\n        progressbarOpposite: false,\n        type: 'bullets', // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n        dynamicBullets: false,\n        dynamicMainBullets: 1,\n        formatFractionCurrent: function (number) { return number; },\n        formatFractionTotal: function (number) { return number; },\n        bulletClass: 'swiper-pagination-bullet',\n        bulletActiveClass: 'swiper-pagination-bullet-active',\n        modifierClass: 'swiper-pagination-', // NEW\n        currentClass: 'swiper-pagination-current',\n        totalClass: 'swiper-pagination-total',\n        hiddenClass: 'swiper-pagination-hidden',\n        progressbarFillClass: 'swiper-pagination-progressbar-fill',\n        progressbarOppositeClass: 'swiper-pagination-progressbar-opposite',\n        clickableClass: 'swiper-pagination-clickable', // NEW\n        lockClass: 'swiper-pagination-lock',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        pagination: {\n          init: Pagination.init.bind(swiper),\n          render: Pagination.render.bind(swiper),\n          update: Pagination.update.bind(swiper),\n          destroy: Pagination.destroy.bind(swiper),\n          dynamicBulletIndex: 0,\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.pagination.init();\n        swiper.pagination.render();\n        swiper.pagination.update();\n      },\n      activeIndexChange: function activeIndexChange() {\n        var swiper = this;\n        if (swiper.params.loop) {\n          swiper.pagination.update();\n        } else if (typeof swiper.snapIndex === 'undefined') {\n          swiper.pagination.update();\n        }\n      },\n      snapIndexChange: function snapIndexChange() {\n        var swiper = this;\n        if (!swiper.params.loop) {\n          swiper.pagination.update();\n        }\n      },\n      slidesLengthChange: function slidesLengthChange() {\n        var swiper = this;\n        if (swiper.params.loop) {\n          swiper.pagination.render();\n          swiper.pagination.update();\n        }\n      },\n      snapGridLengthChange: function snapGridLengthChange() {\n        var swiper = this;\n        if (!swiper.params.loop) {\n          swiper.pagination.render();\n          swiper.pagination.update();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.pagination.destroy();\n      },\n      click: function click(e) {\n        var swiper = this;\n        if (\n          swiper.params.pagination.el\n          && swiper.params.pagination.hideOnClick\n          && swiper.pagination.$el.length > 0\n          && !$(e.target).hasClass(swiper.params.pagination.bulletClass)\n        ) {\n          var isHidden = swiper.pagination.$el.hasClass(swiper.params.pagination.hiddenClass);\n          if (isHidden === true) {\n            swiper.emit('paginationShow', swiper);\n          } else {\n            swiper.emit('paginationHide', swiper);\n          }\n          swiper.pagination.$el.toggleClass(swiper.params.pagination.hiddenClass);\n        }\n      },\n    },\n  };\n\n  var Scrollbar = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var rtl = swiper.rtlTranslate;\n      var progress = swiper.progress;\n      var dragSize = scrollbar.dragSize;\n      var trackSize = scrollbar.trackSize;\n      var $dragEl = scrollbar.$dragEl;\n      var $el = scrollbar.$el;\n      var params = swiper.params.scrollbar;\n\n      var newSize = dragSize;\n      var newPos = (trackSize - dragSize) * progress;\n      if (rtl) {\n        newPos = -newPos;\n        if (newPos > 0) {\n          newSize = dragSize - newPos;\n          newPos = 0;\n        } else if (-newPos + dragSize > trackSize) {\n          newSize = trackSize + newPos;\n        }\n      } else if (newPos < 0) {\n        newSize = dragSize + newPos;\n        newPos = 0;\n      } else if (newPos + dragSize > trackSize) {\n        newSize = trackSize - newPos;\n      }\n      if (swiper.isHorizontal()) {\n        if (Support.transforms3d) {\n          $dragEl.transform((\"translate3d(\" + newPos + \"px, 0, 0)\"));\n        } else {\n          $dragEl.transform((\"translateX(\" + newPos + \"px)\"));\n        }\n        $dragEl[0].style.width = newSize + \"px\";\n      } else {\n        if (Support.transforms3d) {\n          $dragEl.transform((\"translate3d(0px, \" + newPos + \"px, 0)\"));\n        } else {\n          $dragEl.transform((\"translateY(\" + newPos + \"px)\"));\n        }\n        $dragEl[0].style.height = newSize + \"px\";\n      }\n      if (params.hide) {\n        clearTimeout(swiper.scrollbar.timeout);\n        $el[0].style.opacity = 1;\n        swiper.scrollbar.timeout = setTimeout(function () {\n          $el[0].style.opacity = 0;\n          $el.transition(400);\n        }, 1000);\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) { return; }\n      swiper.scrollbar.$dragEl.transition(duration);\n    },\n    updateSize: function updateSize() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) { return; }\n\n      var scrollbar = swiper.scrollbar;\n      var $dragEl = scrollbar.$dragEl;\n      var $el = scrollbar.$el;\n\n      $dragEl[0].style.width = '';\n      $dragEl[0].style.height = '';\n      var trackSize = swiper.isHorizontal() ? $el[0].offsetWidth : $el[0].offsetHeight;\n\n      var divider = swiper.size / swiper.virtualSize;\n      var moveDivider = divider * (trackSize / swiper.size);\n      var dragSize;\n      if (swiper.params.scrollbar.dragSize === 'auto') {\n        dragSize = trackSize * divider;\n      } else {\n        dragSize = parseInt(swiper.params.scrollbar.dragSize, 10);\n      }\n\n      if (swiper.isHorizontal()) {\n        $dragEl[0].style.width = dragSize + \"px\";\n      } else {\n        $dragEl[0].style.height = dragSize + \"px\";\n      }\n\n      if (divider >= 1) {\n        $el[0].style.display = 'none';\n      } else {\n        $el[0].style.display = '';\n      }\n      if (swiper.params.scrollbar.hide) {\n        $el[0].style.opacity = 0;\n      }\n      Utils.extend(scrollbar, {\n        trackSize: trackSize,\n        divider: divider,\n        moveDivider: moveDivider,\n        dragSize: dragSize,\n      });\n      scrollbar.$el[swiper.params.watchOverflow && swiper.isLocked ? 'addClass' : 'removeClass'](swiper.params.scrollbar.lockClass);\n    },\n    setDragPosition: function setDragPosition(e) {\n      var swiper = this;\n      var scrollbar = swiper.scrollbar;\n      var rtl = swiper.rtlTranslate;\n      var $el = scrollbar.$el;\n      var dragSize = scrollbar.dragSize;\n      var trackSize = scrollbar.trackSize;\n\n      var pointerPosition;\n      if (swiper.isHorizontal()) {\n        pointerPosition = ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].pageX : e.pageX || e.clientX);\n      } else {\n        pointerPosition = ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].pageY : e.pageY || e.clientY);\n      }\n      var positionRatio;\n      positionRatio = ((pointerPosition) - $el.offset()[swiper.isHorizontal() ? 'left' : 'top'] - (dragSize / 2)) / (trackSize - dragSize);\n      positionRatio = Math.max(Math.min(positionRatio, 1), 0);\n      if (rtl) {\n        positionRatio = 1 - positionRatio;\n      }\n\n      var position = swiper.minTranslate() + ((swiper.maxTranslate() - swiper.minTranslate()) * positionRatio);\n\n      swiper.updateProgress(position);\n      swiper.setTranslate(position);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    },\n    onDragStart: function onDragStart(e) {\n      var swiper = this;\n      var params = swiper.params.scrollbar;\n      var scrollbar = swiper.scrollbar;\n      var $wrapperEl = swiper.$wrapperEl;\n      var $el = scrollbar.$el;\n      var $dragEl = scrollbar.$dragEl;\n      swiper.scrollbar.isTouched = true;\n      e.preventDefault();\n      e.stopPropagation();\n\n      $wrapperEl.transition(100);\n      $dragEl.transition(100);\n      scrollbar.setDragPosition(e);\n\n      clearTimeout(swiper.scrollbar.dragTimeout);\n\n      $el.transition(0);\n      if (params.hide) {\n        $el.css('opacity', 1);\n      }\n      swiper.emit('scrollbarDragStart', e);\n    },\n    onDragMove: function onDragMove(e) {\n      var swiper = this;\n      var scrollbar = swiper.scrollbar;\n      var $wrapperEl = swiper.$wrapperEl;\n      var $el = scrollbar.$el;\n      var $dragEl = scrollbar.$dragEl;\n\n      if (!swiper.scrollbar.isTouched) { return; }\n      if (e.preventDefault) { e.preventDefault(); }\n      else { e.returnValue = false; }\n      scrollbar.setDragPosition(e);\n      $wrapperEl.transition(0);\n      $el.transition(0);\n      $dragEl.transition(0);\n      swiper.emit('scrollbarDragMove', e);\n    },\n    onDragEnd: function onDragEnd(e) {\n      var swiper = this;\n\n      var params = swiper.params.scrollbar;\n      var scrollbar = swiper.scrollbar;\n      var $el = scrollbar.$el;\n\n      if (!swiper.scrollbar.isTouched) { return; }\n      swiper.scrollbar.isTouched = false;\n      if (params.hide) {\n        clearTimeout(swiper.scrollbar.dragTimeout);\n        swiper.scrollbar.dragTimeout = Utils.nextTick(function () {\n          $el.css('opacity', 0);\n          $el.transition(400);\n        }, 1000);\n      }\n      swiper.emit('scrollbarDragEnd', e);\n      if (params.snapOnRelease) {\n        swiper.slideToClosest();\n      }\n    },\n    enableDraggable: function enableDraggable() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var touchEventsTouch = swiper.touchEventsTouch;\n      var touchEventsDesktop = swiper.touchEventsDesktop;\n      var params = swiper.params;\n      var $el = scrollbar.$el;\n      var target = $el[0];\n      var activeListener = Support.passiveListener && params.passiveListeners ? { passive: false, capture: false } : false;\n      var passiveListener = Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n      if (!Support.touch) {\n        target.addEventListener(touchEventsDesktop.start, swiper.scrollbar.onDragStart, activeListener);\n        doc.addEventListener(touchEventsDesktop.move, swiper.scrollbar.onDragMove, activeListener);\n        doc.addEventListener(touchEventsDesktop.end, swiper.scrollbar.onDragEnd, passiveListener);\n      } else {\n        target.addEventListener(touchEventsTouch.start, swiper.scrollbar.onDragStart, activeListener);\n        target.addEventListener(touchEventsTouch.move, swiper.scrollbar.onDragMove, activeListener);\n        target.addEventListener(touchEventsTouch.end, swiper.scrollbar.onDragEnd, passiveListener);\n      }\n    },\n    disableDraggable: function disableDraggable() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var touchEventsTouch = swiper.touchEventsTouch;\n      var touchEventsDesktop = swiper.touchEventsDesktop;\n      var params = swiper.params;\n      var $el = scrollbar.$el;\n      var target = $el[0];\n      var activeListener = Support.passiveListener && params.passiveListeners ? { passive: false, capture: false } : false;\n      var passiveListener = Support.passiveListener && params.passiveListeners ? { passive: true, capture: false } : false;\n      if (!Support.touch) {\n        target.removeEventListener(touchEventsDesktop.start, swiper.scrollbar.onDragStart, activeListener);\n        doc.removeEventListener(touchEventsDesktop.move, swiper.scrollbar.onDragMove, activeListener);\n        doc.removeEventListener(touchEventsDesktop.end, swiper.scrollbar.onDragEnd, passiveListener);\n      } else {\n        target.removeEventListener(touchEventsTouch.start, swiper.scrollbar.onDragStart, activeListener);\n        target.removeEventListener(touchEventsTouch.move, swiper.scrollbar.onDragMove, activeListener);\n        target.removeEventListener(touchEventsTouch.end, swiper.scrollbar.onDragEnd, passiveListener);\n      }\n    },\n    init: function init() {\n      var swiper = this;\n      if (!swiper.params.scrollbar.el) { return; }\n      var scrollbar = swiper.scrollbar;\n      var $swiperEl = swiper.$el;\n      var params = swiper.params.scrollbar;\n\n      var $el = $(params.el);\n      if (swiper.params.uniqueNavElements && typeof params.el === 'string' && $el.length > 1 && $swiperEl.find(params.el).length === 1) {\n        $el = $swiperEl.find(params.el);\n      }\n\n      var $dragEl = $el.find((\".\" + (swiper.params.scrollbar.dragClass)));\n      if ($dragEl.length === 0) {\n        $dragEl = $((\"<div class=\\\"\" + (swiper.params.scrollbar.dragClass) + \"\\\"></div>\"));\n        $el.append($dragEl);\n      }\n\n      Utils.extend(scrollbar, {\n        $el: $el,\n        el: $el[0],\n        $dragEl: $dragEl,\n        dragEl: $dragEl[0],\n      });\n\n      if (params.draggable) {\n        scrollbar.enableDraggable();\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      swiper.scrollbar.disableDraggable();\n    },\n  };\n\n  var Scrollbar$1 = {\n    name: 'scrollbar',\n    params: {\n      scrollbar: {\n        el: null,\n        dragSize: 'auto',\n        hide: false,\n        draggable: false,\n        snapOnRelease: true,\n        lockClass: 'swiper-scrollbar-lock',\n        dragClass: 'swiper-scrollbar-drag',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        scrollbar: {\n          init: Scrollbar.init.bind(swiper),\n          destroy: Scrollbar.destroy.bind(swiper),\n          updateSize: Scrollbar.updateSize.bind(swiper),\n          setTranslate: Scrollbar.setTranslate.bind(swiper),\n          setTransition: Scrollbar.setTransition.bind(swiper),\n          enableDraggable: Scrollbar.enableDraggable.bind(swiper),\n          disableDraggable: Scrollbar.disableDraggable.bind(swiper),\n          setDragPosition: Scrollbar.setDragPosition.bind(swiper),\n          onDragStart: Scrollbar.onDragStart.bind(swiper),\n          onDragMove: Scrollbar.onDragMove.bind(swiper),\n          onDragEnd: Scrollbar.onDragEnd.bind(swiper),\n          isTouched: false,\n          timeout: null,\n          dragTimeout: null,\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        swiper.scrollbar.init();\n        swiper.scrollbar.updateSize();\n        swiper.scrollbar.setTranslate();\n      },\n      update: function update() {\n        var swiper = this;\n        swiper.scrollbar.updateSize();\n      },\n      resize: function resize() {\n        var swiper = this;\n        swiper.scrollbar.updateSize();\n      },\n      observerUpdate: function observerUpdate() {\n        var swiper = this;\n        swiper.scrollbar.updateSize();\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        swiper.scrollbar.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        swiper.scrollbar.setTransition(duration);\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.scrollbar.destroy();\n      },\n    },\n  };\n\n  var Parallax = {\n    setTransform: function setTransform(el, progress) {\n      var swiper = this;\n      var rtl = swiper.rtl;\n\n      var $el = $(el);\n      var rtlFactor = rtl ? -1 : 1;\n\n      var p = $el.attr('data-swiper-parallax') || '0';\n      var x = $el.attr('data-swiper-parallax-x');\n      var y = $el.attr('data-swiper-parallax-y');\n      var scale = $el.attr('data-swiper-parallax-scale');\n      var opacity = $el.attr('data-swiper-parallax-opacity');\n\n      if (x || y) {\n        x = x || '0';\n        y = y || '0';\n      } else if (swiper.isHorizontal()) {\n        x = p;\n        y = '0';\n      } else {\n        y = p;\n        x = '0';\n      }\n\n      if ((x).indexOf('%') >= 0) {\n        x = (parseInt(x, 10) * progress * rtlFactor) + \"%\";\n      } else {\n        x = (x * progress * rtlFactor) + \"px\";\n      }\n      if ((y).indexOf('%') >= 0) {\n        y = (parseInt(y, 10) * progress) + \"%\";\n      } else {\n        y = (y * progress) + \"px\";\n      }\n\n      if (typeof opacity !== 'undefined' && opacity !== null) {\n        var currentOpacity = opacity - ((opacity - 1) * (1 - Math.abs(progress)));\n        $el[0].style.opacity = currentOpacity;\n      }\n      if (typeof scale === 'undefined' || scale === null) {\n        $el.transform((\"translate3d(\" + x + \", \" + y + \", 0px)\"));\n      } else {\n        var currentScale = scale - ((scale - 1) * (1 - Math.abs(progress)));\n        $el.transform((\"translate3d(\" + x + \", \" + y + \", 0px) scale(\" + currentScale + \")\"));\n      }\n    },\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var $el = swiper.$el;\n      var slides = swiper.slides;\n      var progress = swiper.progress;\n      var snapGrid = swiper.snapGrid;\n      $el.children('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]')\n        .each(function (index, el) {\n          swiper.parallax.setTransform(el, progress);\n        });\n      slides.each(function (slideIndex, slideEl) {\n        var slideProgress = slideEl.progress;\n        if (swiper.params.slidesPerGroup > 1 && swiper.params.slidesPerView !== 'auto') {\n          slideProgress += Math.ceil(slideIndex / 2) - (progress * (snapGrid.length - 1));\n        }\n        slideProgress = Math.min(Math.max(slideProgress, -1), 1);\n        $(slideEl).find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]')\n          .each(function (index, el) {\n            swiper.parallax.setTransform(el, slideProgress);\n          });\n      });\n    },\n    setTransition: function setTransition(duration) {\n      if ( duration === void 0 ) duration = this.params.speed;\n\n      var swiper = this;\n      var $el = swiper.$el;\n      $el.find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]')\n        .each(function (index, parallaxEl) {\n          var $parallaxEl = $(parallaxEl);\n          var parallaxDuration = parseInt($parallaxEl.attr('data-swiper-parallax-duration'), 10) || duration;\n          if (duration === 0) { parallaxDuration = 0; }\n          $parallaxEl.transition(parallaxDuration);\n        });\n    },\n  };\n\n  var Parallax$1 = {\n    name: 'parallax',\n    params: {\n      parallax: {\n        enabled: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        parallax: {\n          setTransform: Parallax.setTransform.bind(swiper),\n          setTranslate: Parallax.setTranslate.bind(swiper),\n          setTransition: Parallax.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (!swiper.params.parallax.enabled) { return; }\n        swiper.params.watchSlidesProgress = true;\n        swiper.originalParams.watchSlidesProgress = true;\n      },\n      init: function init() {\n        var swiper = this;\n        if (!swiper.params.parallax.enabled) { return; }\n        swiper.parallax.setTranslate();\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (!swiper.params.parallax.enabled) { return; }\n        swiper.parallax.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (!swiper.params.parallax.enabled) { return; }\n        swiper.parallax.setTransition(duration);\n      },\n    },\n  };\n\n  var Zoom = {\n    // Calc Scale From Multi-touches\n    getDistanceBetweenTouches: function getDistanceBetweenTouches(e) {\n      if (e.targetTouches.length < 2) { return 1; }\n      var x1 = e.targetTouches[0].pageX;\n      var y1 = e.targetTouches[0].pageY;\n      var x2 = e.targetTouches[1].pageX;\n      var y2 = e.targetTouches[1].pageY;\n      var distance = Math.sqrt((Math.pow( (x2 - x1), 2 )) + (Math.pow( (y2 - y1), 2 )));\n      return distance;\n    },\n    // Events\n    onGestureStart: function onGestureStart(e) {\n      var swiper = this;\n      var params = swiper.params.zoom;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      zoom.fakeGestureTouched = false;\n      zoom.fakeGestureMoved = false;\n      if (!Support.gestures) {\n        if (e.type !== 'touchstart' || (e.type === 'touchstart' && e.targetTouches.length < 2)) {\n          return;\n        }\n        zoom.fakeGestureTouched = true;\n        gesture.scaleStart = Zoom.getDistanceBetweenTouches(e);\n      }\n      if (!gesture.$slideEl || !gesture.$slideEl.length) {\n        gesture.$slideEl = $(e.target).closest('.swiper-slide');\n        if (gesture.$slideEl.length === 0) { gesture.$slideEl = swiper.slides.eq(swiper.activeIndex); }\n        gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas');\n        gesture.$imageWrapEl = gesture.$imageEl.parent((\".\" + (params.containerClass)));\n        gesture.maxRatio = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n        if (gesture.$imageWrapEl.length === 0) {\n          gesture.$imageEl = undefined;\n          return;\n        }\n      }\n      gesture.$imageEl.transition(0);\n      swiper.zoom.isScaling = true;\n    },\n    onGestureChange: function onGestureChange(e) {\n      var swiper = this;\n      var params = swiper.params.zoom;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      if (!Support.gestures) {\n        if (e.type !== 'touchmove' || (e.type === 'touchmove' && e.targetTouches.length < 2)) {\n          return;\n        }\n        zoom.fakeGestureMoved = true;\n        gesture.scaleMove = Zoom.getDistanceBetweenTouches(e);\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      if (Support.gestures) {\n        zoom.scale = e.scale * zoom.currentScale;\n      } else {\n        zoom.scale = (gesture.scaleMove / gesture.scaleStart) * zoom.currentScale;\n      }\n      if (zoom.scale > gesture.maxRatio) {\n        zoom.scale = (gesture.maxRatio - 1) + (Math.pow( ((zoom.scale - gesture.maxRatio) + 1), 0.5 ));\n      }\n      if (zoom.scale < params.minRatio) {\n        zoom.scale = (params.minRatio + 1) - (Math.pow( ((params.minRatio - zoom.scale) + 1), 0.5 ));\n      }\n      gesture.$imageEl.transform((\"translate3d(0,0,0) scale(\" + (zoom.scale) + \")\"));\n    },\n    onGestureEnd: function onGestureEnd(e) {\n      var swiper = this;\n      var params = swiper.params.zoom;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      if (!Support.gestures) {\n        if (!zoom.fakeGestureTouched || !zoom.fakeGestureMoved) {\n          return;\n        }\n        if (e.type !== 'touchend' || (e.type === 'touchend' && e.changedTouches.length < 2 && !Device.android)) {\n          return;\n        }\n        zoom.fakeGestureTouched = false;\n        zoom.fakeGestureMoved = false;\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      zoom.scale = Math.max(Math.min(zoom.scale, gesture.maxRatio), params.minRatio);\n      gesture.$imageEl.transition(swiper.params.speed).transform((\"translate3d(0,0,0) scale(\" + (zoom.scale) + \")\"));\n      zoom.currentScale = zoom.scale;\n      zoom.isScaling = false;\n      if (zoom.scale === 1) { gesture.$slideEl = undefined; }\n    },\n    onTouchStart: function onTouchStart(e) {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      if (image.isTouched) { return; }\n      if (Device.android) { e.preventDefault(); }\n      image.isTouched = true;\n      image.touchesStart.x = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n      image.touchesStart.y = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n    },\n    onTouchMove: function onTouchMove(e) {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n      var velocity = zoom.velocity;\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      swiper.allowClick = false;\n      if (!image.isTouched || !gesture.$slideEl) { return; }\n\n      if (!image.isMoved) {\n        image.width = gesture.$imageEl[0].offsetWidth;\n        image.height = gesture.$imageEl[0].offsetHeight;\n        image.startX = Utils.getTranslate(gesture.$imageWrapEl[0], 'x') || 0;\n        image.startY = Utils.getTranslate(gesture.$imageWrapEl[0], 'y') || 0;\n        gesture.slideWidth = gesture.$slideEl[0].offsetWidth;\n        gesture.slideHeight = gesture.$slideEl[0].offsetHeight;\n        gesture.$imageWrapEl.transition(0);\n        if (swiper.rtl) {\n          image.startX = -image.startX;\n          image.startY = -image.startY;\n        }\n      }\n      // Define if we need image drag\n      var scaledWidth = image.width * zoom.scale;\n      var scaledHeight = image.height * zoom.scale;\n\n      if (scaledWidth < gesture.slideWidth && scaledHeight < gesture.slideHeight) { return; }\n\n      image.minX = Math.min(((gesture.slideWidth / 2) - (scaledWidth / 2)), 0);\n      image.maxX = -image.minX;\n      image.minY = Math.min(((gesture.slideHeight / 2) - (scaledHeight / 2)), 0);\n      image.maxY = -image.minY;\n\n      image.touchesCurrent.x = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n      image.touchesCurrent.y = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n\n      if (!image.isMoved && !zoom.isScaling) {\n        if (\n          swiper.isHorizontal()\n          && (\n            (Math.floor(image.minX) === Math.floor(image.startX) && image.touchesCurrent.x < image.touchesStart.x)\n            || (Math.floor(image.maxX) === Math.floor(image.startX) && image.touchesCurrent.x > image.touchesStart.x)\n          )\n        ) {\n          image.isTouched = false;\n          return;\n        } if (\n          !swiper.isHorizontal()\n          && (\n            (Math.floor(image.minY) === Math.floor(image.startY) && image.touchesCurrent.y < image.touchesStart.y)\n            || (Math.floor(image.maxY) === Math.floor(image.startY) && image.touchesCurrent.y > image.touchesStart.y)\n          )\n        ) {\n          image.isTouched = false;\n          return;\n        }\n      }\n      e.preventDefault();\n      e.stopPropagation();\n\n      image.isMoved = true;\n      image.currentX = (image.touchesCurrent.x - image.touchesStart.x) + image.startX;\n      image.currentY = (image.touchesCurrent.y - image.touchesStart.y) + image.startY;\n\n      if (image.currentX < image.minX) {\n        image.currentX = (image.minX + 1) - (Math.pow( ((image.minX - image.currentX) + 1), 0.8 ));\n      }\n      if (image.currentX > image.maxX) {\n        image.currentX = (image.maxX - 1) + (Math.pow( ((image.currentX - image.maxX) + 1), 0.8 ));\n      }\n\n      if (image.currentY < image.minY) {\n        image.currentY = (image.minY + 1) - (Math.pow( ((image.minY - image.currentY) + 1), 0.8 ));\n      }\n      if (image.currentY > image.maxY) {\n        image.currentY = (image.maxY - 1) + (Math.pow( ((image.currentY - image.maxY) + 1), 0.8 ));\n      }\n\n      // Velocity\n      if (!velocity.prevPositionX) { velocity.prevPositionX = image.touchesCurrent.x; }\n      if (!velocity.prevPositionY) { velocity.prevPositionY = image.touchesCurrent.y; }\n      if (!velocity.prevTime) { velocity.prevTime = Date.now(); }\n      velocity.x = (image.touchesCurrent.x - velocity.prevPositionX) / (Date.now() - velocity.prevTime) / 2;\n      velocity.y = (image.touchesCurrent.y - velocity.prevPositionY) / (Date.now() - velocity.prevTime) / 2;\n      if (Math.abs(image.touchesCurrent.x - velocity.prevPositionX) < 2) { velocity.x = 0; }\n      if (Math.abs(image.touchesCurrent.y - velocity.prevPositionY) < 2) { velocity.y = 0; }\n      velocity.prevPositionX = image.touchesCurrent.x;\n      velocity.prevPositionY = image.touchesCurrent.y;\n      velocity.prevTime = Date.now();\n\n      gesture.$imageWrapEl.transform((\"translate3d(\" + (image.currentX) + \"px, \" + (image.currentY) + \"px,0)\"));\n    },\n    onTouchEnd: function onTouchEnd() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n      var velocity = zoom.velocity;\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n      if (!image.isTouched || !image.isMoved) {\n        image.isTouched = false;\n        image.isMoved = false;\n        return;\n      }\n      image.isTouched = false;\n      image.isMoved = false;\n      var momentumDurationX = 300;\n      var momentumDurationY = 300;\n      var momentumDistanceX = velocity.x * momentumDurationX;\n      var newPositionX = image.currentX + momentumDistanceX;\n      var momentumDistanceY = velocity.y * momentumDurationY;\n      var newPositionY = image.currentY + momentumDistanceY;\n\n      // Fix duration\n      if (velocity.x !== 0) { momentumDurationX = Math.abs((newPositionX - image.currentX) / velocity.x); }\n      if (velocity.y !== 0) { momentumDurationY = Math.abs((newPositionY - image.currentY) / velocity.y); }\n      var momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n\n      image.currentX = newPositionX;\n      image.currentY = newPositionY;\n\n      // Define if we need image drag\n      var scaledWidth = image.width * zoom.scale;\n      var scaledHeight = image.height * zoom.scale;\n      image.minX = Math.min(((gesture.slideWidth / 2) - (scaledWidth / 2)), 0);\n      image.maxX = -image.minX;\n      image.minY = Math.min(((gesture.slideHeight / 2) - (scaledHeight / 2)), 0);\n      image.maxY = -image.minY;\n      image.currentX = Math.max(Math.min(image.currentX, image.maxX), image.minX);\n      image.currentY = Math.max(Math.min(image.currentY, image.maxY), image.minY);\n\n      gesture.$imageWrapEl.transition(momentumDuration).transform((\"translate3d(\" + (image.currentX) + \"px, \" + (image.currentY) + \"px,0)\"));\n    },\n    onTransitionEnd: function onTransitionEnd() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      var gesture = zoom.gesture;\n      if (gesture.$slideEl && swiper.previousIndex !== swiper.activeIndex) {\n        gesture.$imageEl.transform('translate3d(0,0,0) scale(1)');\n        gesture.$imageWrapEl.transform('translate3d(0,0,0)');\n\n        zoom.scale = 1;\n        zoom.currentScale = 1;\n\n        gesture.$slideEl = undefined;\n        gesture.$imageEl = undefined;\n        gesture.$imageWrapEl = undefined;\n      }\n    },\n    // Toggle Zoom\n    toggle: function toggle(e) {\n      var swiper = this;\n      var zoom = swiper.zoom;\n\n      if (zoom.scale && zoom.scale !== 1) {\n        // Zoom Out\n        zoom.out();\n      } else {\n        // Zoom In\n        zoom.in(e);\n      }\n    },\n    in: function in$1(e) {\n      var swiper = this;\n\n      var zoom = swiper.zoom;\n      var params = swiper.params.zoom;\n      var gesture = zoom.gesture;\n      var image = zoom.image;\n\n      if (!gesture.$slideEl) {\n        gesture.$slideEl = swiper.clickedSlide ? $(swiper.clickedSlide) : swiper.slides.eq(swiper.activeIndex);\n        gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas');\n        gesture.$imageWrapEl = gesture.$imageEl.parent((\".\" + (params.containerClass)));\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n\n      gesture.$slideEl.addClass((\"\" + (params.zoomedSlideClass)));\n\n      var touchX;\n      var touchY;\n      var offsetX;\n      var offsetY;\n      var diffX;\n      var diffY;\n      var translateX;\n      var translateY;\n      var imageWidth;\n      var imageHeight;\n      var scaledWidth;\n      var scaledHeight;\n      var translateMinX;\n      var translateMinY;\n      var translateMaxX;\n      var translateMaxY;\n      var slideWidth;\n      var slideHeight;\n\n      if (typeof image.touchesStart.x === 'undefined' && e) {\n        touchX = e.type === 'touchend' ? e.changedTouches[0].pageX : e.pageX;\n        touchY = e.type === 'touchend' ? e.changedTouches[0].pageY : e.pageY;\n      } else {\n        touchX = image.touchesStart.x;\n        touchY = image.touchesStart.y;\n      }\n\n      zoom.scale = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n      zoom.currentScale = gesture.$imageWrapEl.attr('data-swiper-zoom') || params.maxRatio;\n      if (e) {\n        slideWidth = gesture.$slideEl[0].offsetWidth;\n        slideHeight = gesture.$slideEl[0].offsetHeight;\n        offsetX = gesture.$slideEl.offset().left;\n        offsetY = gesture.$slideEl.offset().top;\n        diffX = (offsetX + (slideWidth / 2)) - touchX;\n        diffY = (offsetY + (slideHeight / 2)) - touchY;\n\n        imageWidth = gesture.$imageEl[0].offsetWidth;\n        imageHeight = gesture.$imageEl[0].offsetHeight;\n        scaledWidth = imageWidth * zoom.scale;\n        scaledHeight = imageHeight * zoom.scale;\n\n        translateMinX = Math.min(((slideWidth / 2) - (scaledWidth / 2)), 0);\n        translateMinY = Math.min(((slideHeight / 2) - (scaledHeight / 2)), 0);\n        translateMaxX = -translateMinX;\n        translateMaxY = -translateMinY;\n\n        translateX = diffX * zoom.scale;\n        translateY = diffY * zoom.scale;\n\n        if (translateX < translateMinX) {\n          translateX = translateMinX;\n        }\n        if (translateX > translateMaxX) {\n          translateX = translateMaxX;\n        }\n\n        if (translateY < translateMinY) {\n          translateY = translateMinY;\n        }\n        if (translateY > translateMaxY) {\n          translateY = translateMaxY;\n        }\n      } else {\n        translateX = 0;\n        translateY = 0;\n      }\n      gesture.$imageWrapEl.transition(300).transform((\"translate3d(\" + translateX + \"px, \" + translateY + \"px,0)\"));\n      gesture.$imageEl.transition(300).transform((\"translate3d(0,0,0) scale(\" + (zoom.scale) + \")\"));\n    },\n    out: function out() {\n      var swiper = this;\n\n      var zoom = swiper.zoom;\n      var params = swiper.params.zoom;\n      var gesture = zoom.gesture;\n\n      if (!gesture.$slideEl) {\n        gesture.$slideEl = swiper.clickedSlide ? $(swiper.clickedSlide) : swiper.slides.eq(swiper.activeIndex);\n        gesture.$imageEl = gesture.$slideEl.find('img, svg, canvas');\n        gesture.$imageWrapEl = gesture.$imageEl.parent((\".\" + (params.containerClass)));\n      }\n      if (!gesture.$imageEl || gesture.$imageEl.length === 0) { return; }\n\n      zoom.scale = 1;\n      zoom.currentScale = 1;\n      gesture.$imageWrapEl.transition(300).transform('translate3d(0,0,0)');\n      gesture.$imageEl.transition(300).transform('translate3d(0,0,0) scale(1)');\n      gesture.$slideEl.removeClass((\"\" + (params.zoomedSlideClass)));\n      gesture.$slideEl = undefined;\n    },\n    // Attach/Detach Events\n    enable: function enable() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      if (zoom.enabled) { return; }\n      zoom.enabled = true;\n\n      var passiveListener = swiper.touchEvents.start === 'touchstart' && Support.passiveListener && swiper.params.passiveListeners ? { passive: true, capture: false } : false;\n\n      // Scale image\n      if (Support.gestures) {\n        swiper.$wrapperEl.on('gesturestart', '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.on('gesturechange', '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.on('gestureend', '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      } else if (swiper.touchEvents.start === 'touchstart') {\n        swiper.$wrapperEl.on(swiper.touchEvents.start, '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.on(swiper.touchEvents.move, '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.on(swiper.touchEvents.end, '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      }\n\n      // Move image\n      swiper.$wrapperEl.on(swiper.touchEvents.move, (\".\" + (swiper.params.zoom.containerClass)), zoom.onTouchMove);\n    },\n    disable: function disable() {\n      var swiper = this;\n      var zoom = swiper.zoom;\n      if (!zoom.enabled) { return; }\n\n      swiper.zoom.enabled = false;\n\n      var passiveListener = swiper.touchEvents.start === 'touchstart' && Support.passiveListener && swiper.params.passiveListeners ? { passive: true, capture: false } : false;\n\n      // Scale image\n      if (Support.gestures) {\n        swiper.$wrapperEl.off('gesturestart', '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.off('gesturechange', '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.off('gestureend', '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      } else if (swiper.touchEvents.start === 'touchstart') {\n        swiper.$wrapperEl.off(swiper.touchEvents.start, '.swiper-slide', zoom.onGestureStart, passiveListener);\n        swiper.$wrapperEl.off(swiper.touchEvents.move, '.swiper-slide', zoom.onGestureChange, passiveListener);\n        swiper.$wrapperEl.off(swiper.touchEvents.end, '.swiper-slide', zoom.onGestureEnd, passiveListener);\n      }\n\n      // Move image\n      swiper.$wrapperEl.off(swiper.touchEvents.move, (\".\" + (swiper.params.zoom.containerClass)), zoom.onTouchMove);\n    },\n  };\n\n  var Zoom$1 = {\n    name: 'zoom',\n    params: {\n      zoom: {\n        enabled: false,\n        maxRatio: 3,\n        minRatio: 1,\n        toggle: true,\n        containerClass: 'swiper-zoom-container',\n        zoomedSlideClass: 'swiper-slide-zoomed',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      var zoom = {\n        enabled: false,\n        scale: 1,\n        currentScale: 1,\n        isScaling: false,\n        gesture: {\n          $slideEl: undefined,\n          slideWidth: undefined,\n          slideHeight: undefined,\n          $imageEl: undefined,\n          $imageWrapEl: undefined,\n          maxRatio: 3,\n        },\n        image: {\n          isTouched: undefined,\n          isMoved: undefined,\n          currentX: undefined,\n          currentY: undefined,\n          minX: undefined,\n          minY: undefined,\n          maxX: undefined,\n          maxY: undefined,\n          width: undefined,\n          height: undefined,\n          startX: undefined,\n          startY: undefined,\n          touchesStart: {},\n          touchesCurrent: {},\n        },\n        velocity: {\n          x: undefined,\n          y: undefined,\n          prevPositionX: undefined,\n          prevPositionY: undefined,\n          prevTime: undefined,\n        },\n      };\n\n      ('onGestureStart onGestureChange onGestureEnd onTouchStart onTouchMove onTouchEnd onTransitionEnd toggle enable disable in out').split(' ').forEach(function (methodName) {\n        zoom[methodName] = Zoom[methodName].bind(swiper);\n      });\n      Utils.extend(swiper, {\n        zoom: zoom,\n      });\n\n      var scale = 1;\n      Object.defineProperty(swiper.zoom, 'scale', {\n        get: function get() {\n          return scale;\n        },\n        set: function set(value) {\n          if (scale !== value) {\n            var imageEl = swiper.zoom.gesture.$imageEl ? swiper.zoom.gesture.$imageEl[0] : undefined;\n            var slideEl = swiper.zoom.gesture.$slideEl ? swiper.zoom.gesture.$slideEl[0] : undefined;\n            swiper.emit('zoomChange', value, imageEl, slideEl);\n          }\n          scale = value;\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.zoom.enabled) {\n          swiper.zoom.enable();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        swiper.zoom.disable();\n      },\n      touchStart: function touchStart(e) {\n        var swiper = this;\n        if (!swiper.zoom.enabled) { return; }\n        swiper.zoom.onTouchStart(e);\n      },\n      touchEnd: function touchEnd(e) {\n        var swiper = this;\n        if (!swiper.zoom.enabled) { return; }\n        swiper.zoom.onTouchEnd(e);\n      },\n      doubleTap: function doubleTap(e) {\n        var swiper = this;\n        if (swiper.params.zoom.enabled && swiper.zoom.enabled && swiper.params.zoom.toggle) {\n          swiper.zoom.toggle(e);\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.zoom.enabled && swiper.params.zoom.enabled) {\n          swiper.zoom.onTransitionEnd();\n        }\n      },\n    },\n  };\n\n  var Lazy = {\n    loadInSlide: function loadInSlide(index, loadInDuplicate) {\n      if ( loadInDuplicate === void 0 ) loadInDuplicate = true;\n\n      var swiper = this;\n      var params = swiper.params.lazy;\n      if (typeof index === 'undefined') { return; }\n      if (swiper.slides.length === 0) { return; }\n      var isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n      var $slideEl = isVirtual\n        ? swiper.$wrapperEl.children((\".\" + (swiper.params.slideClass) + \"[data-swiper-slide-index=\\\"\" + index + \"\\\"]\"))\n        : swiper.slides.eq(index);\n\n      var $images = $slideEl.find((\".\" + (params.elementClass) + \":not(.\" + (params.loadedClass) + \"):not(.\" + (params.loadingClass) + \")\"));\n      if ($slideEl.hasClass(params.elementClass) && !$slideEl.hasClass(params.loadedClass) && !$slideEl.hasClass(params.loadingClass)) {\n        $images = $images.add($slideEl[0]);\n      }\n      if ($images.length === 0) { return; }\n\n      $images.each(function (imageIndex, imageEl) {\n        var $imageEl = $(imageEl);\n        $imageEl.addClass(params.loadingClass);\n\n        var background = $imageEl.attr('data-background');\n        var src = $imageEl.attr('data-src');\n        var srcset = $imageEl.attr('data-srcset');\n        var sizes = $imageEl.attr('data-sizes');\n\n        swiper.loadImage($imageEl[0], (src || background), srcset, sizes, false, function () {\n          if (typeof swiper === 'undefined' || swiper === null || !swiper || (swiper && !swiper.params) || swiper.destroyed) { return; }\n          if (background) {\n            $imageEl.css('background-image', (\"url(\\\"\" + background + \"\\\")\"));\n            $imageEl.removeAttr('data-background');\n          } else {\n            if (srcset) {\n              $imageEl.attr('srcset', srcset);\n              $imageEl.removeAttr('data-srcset');\n            }\n            if (sizes) {\n              $imageEl.attr('sizes', sizes);\n              $imageEl.removeAttr('data-sizes');\n            }\n            if (src) {\n              $imageEl.attr('src', src);\n              $imageEl.removeAttr('data-src');\n            }\n          }\n\n          $imageEl.addClass(params.loadedClass).removeClass(params.loadingClass);\n          $slideEl.find((\".\" + (params.preloaderClass))).remove();\n          if (swiper.params.loop && loadInDuplicate) {\n            var slideOriginalIndex = $slideEl.attr('data-swiper-slide-index');\n            if ($slideEl.hasClass(swiper.params.slideDuplicateClass)) {\n              var originalSlide = swiper.$wrapperEl.children((\"[data-swiper-slide-index=\\\"\" + slideOriginalIndex + \"\\\"]:not(.\" + (swiper.params.slideDuplicateClass) + \")\"));\n              swiper.lazy.loadInSlide(originalSlide.index(), false);\n            } else {\n              var duplicatedSlide = swiper.$wrapperEl.children((\".\" + (swiper.params.slideDuplicateClass) + \"[data-swiper-slide-index=\\\"\" + slideOriginalIndex + \"\\\"]\"));\n              swiper.lazy.loadInSlide(duplicatedSlide.index(), false);\n            }\n          }\n          swiper.emit('lazyImageReady', $slideEl[0], $imageEl[0]);\n        });\n\n        swiper.emit('lazyImageLoad', $slideEl[0], $imageEl[0]);\n      });\n    },\n    load: function load() {\n      var swiper = this;\n      var $wrapperEl = swiper.$wrapperEl;\n      var swiperParams = swiper.params;\n      var slides = swiper.slides;\n      var activeIndex = swiper.activeIndex;\n      var isVirtual = swiper.virtual && swiperParams.virtual.enabled;\n      var params = swiperParams.lazy;\n\n      var slidesPerView = swiperParams.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = 0;\n      }\n\n      function slideExist(index) {\n        if (isVirtual) {\n          if ($wrapperEl.children((\".\" + (swiperParams.slideClass) + \"[data-swiper-slide-index=\\\"\" + index + \"\\\"]\")).length) {\n            return true;\n          }\n        } else if (slides[index]) { return true; }\n        return false;\n      }\n      function slideIndex(slideEl) {\n        if (isVirtual) {\n          return $(slideEl).attr('data-swiper-slide-index');\n        }\n        return $(slideEl).index();\n      }\n\n      if (!swiper.lazy.initialImageLoaded) { swiper.lazy.initialImageLoaded = true; }\n      if (swiper.params.watchSlidesVisibility) {\n        $wrapperEl.children((\".\" + (swiperParams.slideVisibleClass))).each(function (elIndex, slideEl) {\n          var index = isVirtual ? $(slideEl).attr('data-swiper-slide-index') : $(slideEl).index();\n          swiper.lazy.loadInSlide(index);\n        });\n      } else if (slidesPerView > 1) {\n        for (var i = activeIndex; i < activeIndex + slidesPerView; i += 1) {\n          if (slideExist(i)) { swiper.lazy.loadInSlide(i); }\n        }\n      } else {\n        swiper.lazy.loadInSlide(activeIndex);\n      }\n      if (params.loadPrevNext) {\n        if (slidesPerView > 1 || (params.loadPrevNextAmount && params.loadPrevNextAmount > 1)) {\n          var amount = params.loadPrevNextAmount;\n          var spv = slidesPerView;\n          var maxIndex = Math.min(activeIndex + spv + Math.max(amount, spv), slides.length);\n          var minIndex = Math.max(activeIndex - Math.max(spv, amount), 0);\n          // Next Slides\n          for (var i$1 = activeIndex + slidesPerView; i$1 < maxIndex; i$1 += 1) {\n            if (slideExist(i$1)) { swiper.lazy.loadInSlide(i$1); }\n          }\n          // Prev Slides\n          for (var i$2 = minIndex; i$2 < activeIndex; i$2 += 1) {\n            if (slideExist(i$2)) { swiper.lazy.loadInSlide(i$2); }\n          }\n        } else {\n          var nextSlide = $wrapperEl.children((\".\" + (swiperParams.slideNextClass)));\n          if (nextSlide.length > 0) { swiper.lazy.loadInSlide(slideIndex(nextSlide)); }\n\n          var prevSlide = $wrapperEl.children((\".\" + (swiperParams.slidePrevClass)));\n          if (prevSlide.length > 0) { swiper.lazy.loadInSlide(slideIndex(prevSlide)); }\n        }\n      }\n    },\n  };\n\n  var Lazy$1 = {\n    name: 'lazy',\n    params: {\n      lazy: {\n        enabled: false,\n        loadPrevNext: false,\n        loadPrevNextAmount: 1,\n        loadOnTransitionStart: false,\n\n        elementClass: 'swiper-lazy',\n        loadingClass: 'swiper-lazy-loading',\n        loadedClass: 'swiper-lazy-loaded',\n        preloaderClass: 'swiper-lazy-preloader',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        lazy: {\n          initialImageLoaded: false,\n          load: Lazy.load.bind(swiper),\n          loadInSlide: Lazy.loadInSlide.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled && swiper.params.preloadImages) {\n          swiper.params.preloadImages = false;\n        }\n      },\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled && !swiper.params.loop && swiper.params.initialSlide === 0) {\n          swiper.lazy.load();\n        }\n      },\n      scroll: function scroll() {\n        var swiper = this;\n        if (swiper.params.freeMode && !swiper.params.freeModeSticky) {\n          swiper.lazy.load();\n        }\n      },\n      resize: function resize() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled) {\n          swiper.lazy.load();\n        }\n      },\n      scrollbarDragMove: function scrollbarDragMove() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled) {\n          swiper.lazy.load();\n        }\n      },\n      transitionStart: function transitionStart() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled) {\n          if (swiper.params.lazy.loadOnTransitionStart || (!swiper.params.lazy.loadOnTransitionStart && !swiper.lazy.initialImageLoaded)) {\n            swiper.lazy.load();\n          }\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.params.lazy.enabled && !swiper.params.lazy.loadOnTransitionStart) {\n          swiper.lazy.load();\n        }\n      },\n    },\n  };\n\n  /* eslint no-bitwise: [\"error\", { \"allow\": [\">>\"] }] */\n\n  var Controller = {\n    LinearSpline: function LinearSpline(x, y) {\n      var binarySearch = (function search() {\n        var maxIndex;\n        var minIndex;\n        var guess;\n        return function (array, val) {\n          minIndex = -1;\n          maxIndex = array.length;\n          while (maxIndex - minIndex > 1) {\n            guess = maxIndex + minIndex >> 1;\n            if (array[guess] <= val) {\n              minIndex = guess;\n            } else {\n              maxIndex = guess;\n            }\n          }\n          return maxIndex;\n        };\n      }());\n      this.x = x;\n      this.y = y;\n      this.lastIndex = x.length - 1;\n      // Given an x value (x2), return the expected y2 value:\n      // (x1,y1) is the known point before given value,\n      // (x3,y3) is the known point after given value.\n      var i1;\n      var i3;\n\n      this.interpolate = function interpolate(x2) {\n        if (!x2) { return 0; }\n\n        // Get the indexes of x1 and x3 (the array indexes before and after given x2):\n        i3 = binarySearch(this.x, x2);\n        i1 = i3 - 1;\n\n        // We have our indexes i1 & i3, so we can calculate already:\n        // y2 := ((x2−x1) × (y3−y1)) ÷ (x3−x1) + y1\n        return (((x2 - this.x[i1]) * (this.y[i3] - this.y[i1])) / (this.x[i3] - this.x[i1])) + this.y[i1];\n      };\n      return this;\n    },\n    // xxx: for now i will just save one spline function to to\n    getInterpolateFunction: function getInterpolateFunction(c) {\n      var swiper = this;\n      if (!swiper.controller.spline) {\n        swiper.controller.spline = swiper.params.loop\n          ? new Controller.LinearSpline(swiper.slidesGrid, c.slidesGrid)\n          : new Controller.LinearSpline(swiper.snapGrid, c.snapGrid);\n      }\n    },\n    setTranslate: function setTranslate(setTranslate$1, byController) {\n      var swiper = this;\n      var controlled = swiper.controller.control;\n      var multiplier;\n      var controlledTranslate;\n      function setControlledTranslate(c) {\n        // this will create an Interpolate function based on the snapGrids\n        // x is the Grid of the scrolled scroller and y will be the controlled scroller\n        // it makes sense to create this only once and recall it for the interpolation\n        // the function does a lot of value caching for performance\n        var translate = swiper.rtlTranslate ? -swiper.translate : swiper.translate;\n        if (swiper.params.controller.by === 'slide') {\n          swiper.controller.getInterpolateFunction(c);\n          // i am not sure why the values have to be multiplicated this way, tried to invert the snapGrid\n          // but it did not work out\n          controlledTranslate = -swiper.controller.spline.interpolate(-translate);\n        }\n\n        if (!controlledTranslate || swiper.params.controller.by === 'container') {\n          multiplier = (c.maxTranslate() - c.minTranslate()) / (swiper.maxTranslate() - swiper.minTranslate());\n          controlledTranslate = ((translate - swiper.minTranslate()) * multiplier) + c.minTranslate();\n        }\n\n        if (swiper.params.controller.inverse) {\n          controlledTranslate = c.maxTranslate() - controlledTranslate;\n        }\n        c.updateProgress(controlledTranslate);\n        c.setTranslate(controlledTranslate, swiper);\n        c.updateActiveIndex();\n        c.updateSlidesClasses();\n      }\n      if (Array.isArray(controlled)) {\n        for (var i = 0; i < controlled.length; i += 1) {\n          if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n            setControlledTranslate(controlled[i]);\n          }\n        }\n      } else if (controlled instanceof Swiper && byController !== controlled) {\n        setControlledTranslate(controlled);\n      }\n    },\n    setTransition: function setTransition(duration, byController) {\n      var swiper = this;\n      var controlled = swiper.controller.control;\n      var i;\n      function setControlledTransition(c) {\n        c.setTransition(duration, swiper);\n        if (duration !== 0) {\n          c.transitionStart();\n          if (c.params.autoHeight) {\n            Utils.nextTick(function () {\n              c.updateAutoHeight();\n            });\n          }\n          c.$wrapperEl.transitionEnd(function () {\n            if (!controlled) { return; }\n            if (c.params.loop && swiper.params.controller.by === 'slide') {\n              c.loopFix();\n            }\n            c.transitionEnd();\n          });\n        }\n      }\n      if (Array.isArray(controlled)) {\n        for (i = 0; i < controlled.length; i += 1) {\n          if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n            setControlledTransition(controlled[i]);\n          }\n        }\n      } else if (controlled instanceof Swiper && byController !== controlled) {\n        setControlledTransition(controlled);\n      }\n    },\n  };\n  var Controller$1 = {\n    name: 'controller',\n    params: {\n      controller: {\n        control: undefined,\n        inverse: false,\n        by: 'slide', // or 'container'\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        controller: {\n          control: swiper.params.controller.control,\n          getInterpolateFunction: Controller.getInterpolateFunction.bind(swiper),\n          setTranslate: Controller.setTranslate.bind(swiper),\n          setTransition: Controller.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      update: function update() {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        if (swiper.controller.spline) {\n          swiper.controller.spline = undefined;\n          delete swiper.controller.spline;\n        }\n      },\n      resize: function resize() {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        if (swiper.controller.spline) {\n          swiper.controller.spline = undefined;\n          delete swiper.controller.spline;\n        }\n      },\n      observerUpdate: function observerUpdate() {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        if (swiper.controller.spline) {\n          swiper.controller.spline = undefined;\n          delete swiper.controller.spline;\n        }\n      },\n      setTranslate: function setTranslate(translate, byController) {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        swiper.controller.setTranslate(translate, byController);\n      },\n      setTransition: function setTransition(duration, byController) {\n        var swiper = this;\n        if (!swiper.controller.control) { return; }\n        swiper.controller.setTransition(duration, byController);\n      },\n    },\n  };\n\n  var a11y = {\n    makeElFocusable: function makeElFocusable($el) {\n      $el.attr('tabIndex', '0');\n      return $el;\n    },\n    addElRole: function addElRole($el, role) {\n      $el.attr('role', role);\n      return $el;\n    },\n    addElLabel: function addElLabel($el, label) {\n      $el.attr('aria-label', label);\n      return $el;\n    },\n    disableEl: function disableEl($el) {\n      $el.attr('aria-disabled', true);\n      return $el;\n    },\n    enableEl: function enableEl($el) {\n      $el.attr('aria-disabled', false);\n      return $el;\n    },\n    onEnterKey: function onEnterKey(e) {\n      var swiper = this;\n      var params = swiper.params.a11y;\n      if (e.keyCode !== 13) { return; }\n      var $targetEl = $(e.target);\n      if (swiper.navigation && swiper.navigation.$nextEl && $targetEl.is(swiper.navigation.$nextEl)) {\n        if (!(swiper.isEnd && !swiper.params.loop)) {\n          swiper.slideNext();\n        }\n        if (swiper.isEnd) {\n          swiper.a11y.notify(params.lastSlideMessage);\n        } else {\n          swiper.a11y.notify(params.nextSlideMessage);\n        }\n      }\n      if (swiper.navigation && swiper.navigation.$prevEl && $targetEl.is(swiper.navigation.$prevEl)) {\n        if (!(swiper.isBeginning && !swiper.params.loop)) {\n          swiper.slidePrev();\n        }\n        if (swiper.isBeginning) {\n          swiper.a11y.notify(params.firstSlideMessage);\n        } else {\n          swiper.a11y.notify(params.prevSlideMessage);\n        }\n      }\n      if (swiper.pagination && $targetEl.is((\".\" + (swiper.params.pagination.bulletClass)))) {\n        $targetEl[0].click();\n      }\n    },\n    notify: function notify(message) {\n      var swiper = this;\n      var notification = swiper.a11y.liveRegion;\n      if (notification.length === 0) { return; }\n      notification.html('');\n      notification.html(message);\n    },\n    updateNavigation: function updateNavigation() {\n      var swiper = this;\n\n      if (swiper.params.loop) { return; }\n      var ref = swiper.navigation;\n      var $nextEl = ref.$nextEl;\n      var $prevEl = ref.$prevEl;\n\n      if ($prevEl && $prevEl.length > 0) {\n        if (swiper.isBeginning) {\n          swiper.a11y.disableEl($prevEl);\n        } else {\n          swiper.a11y.enableEl($prevEl);\n        }\n      }\n      if ($nextEl && $nextEl.length > 0) {\n        if (swiper.isEnd) {\n          swiper.a11y.disableEl($nextEl);\n        } else {\n          swiper.a11y.enableEl($nextEl);\n        }\n      }\n    },\n    updatePagination: function updatePagination() {\n      var swiper = this;\n      var params = swiper.params.a11y;\n      if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n        swiper.pagination.bullets.each(function (bulletIndex, bulletEl) {\n          var $bulletEl = $(bulletEl);\n          swiper.a11y.makeElFocusable($bulletEl);\n          swiper.a11y.addElRole($bulletEl, 'button');\n          swiper.a11y.addElLabel($bulletEl, params.paginationBulletMessage.replace(/{{index}}/, $bulletEl.index() + 1));\n        });\n      }\n    },\n    init: function init() {\n      var swiper = this;\n\n      swiper.$el.append(swiper.a11y.liveRegion);\n\n      // Navigation\n      var params = swiper.params.a11y;\n      var $nextEl;\n      var $prevEl;\n      if (swiper.navigation && swiper.navigation.$nextEl) {\n        $nextEl = swiper.navigation.$nextEl;\n      }\n      if (swiper.navigation && swiper.navigation.$prevEl) {\n        $prevEl = swiper.navigation.$prevEl;\n      }\n      if ($nextEl) {\n        swiper.a11y.makeElFocusable($nextEl);\n        swiper.a11y.addElRole($nextEl, 'button');\n        swiper.a11y.addElLabel($nextEl, params.nextSlideMessage);\n        $nextEl.on('keydown', swiper.a11y.onEnterKey);\n      }\n      if ($prevEl) {\n        swiper.a11y.makeElFocusable($prevEl);\n        swiper.a11y.addElRole($prevEl, 'button');\n        swiper.a11y.addElLabel($prevEl, params.prevSlideMessage);\n        $prevEl.on('keydown', swiper.a11y.onEnterKey);\n      }\n\n      // Pagination\n      if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n        swiper.pagination.$el.on('keydown', (\".\" + (swiper.params.pagination.bulletClass)), swiper.a11y.onEnterKey);\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      if (swiper.a11y.liveRegion && swiper.a11y.liveRegion.length > 0) { swiper.a11y.liveRegion.remove(); }\n\n      var $nextEl;\n      var $prevEl;\n      if (swiper.navigation && swiper.navigation.$nextEl) {\n        $nextEl = swiper.navigation.$nextEl;\n      }\n      if (swiper.navigation && swiper.navigation.$prevEl) {\n        $prevEl = swiper.navigation.$prevEl;\n      }\n      if ($nextEl) {\n        $nextEl.off('keydown', swiper.a11y.onEnterKey);\n      }\n      if ($prevEl) {\n        $prevEl.off('keydown', swiper.a11y.onEnterKey);\n      }\n\n      // Pagination\n      if (swiper.pagination && swiper.params.pagination.clickable && swiper.pagination.bullets && swiper.pagination.bullets.length) {\n        swiper.pagination.$el.off('keydown', (\".\" + (swiper.params.pagination.bulletClass)), swiper.a11y.onEnterKey);\n      }\n    },\n  };\n  var A11y = {\n    name: 'a11y',\n    params: {\n      a11y: {\n        enabled: true,\n        notificationClass: 'swiper-notification',\n        prevSlideMessage: 'Previous slide',\n        nextSlideMessage: 'Next slide',\n        firstSlideMessage: 'This is the first slide',\n        lastSlideMessage: 'This is the last slide',\n        paginationBulletMessage: 'Go to slide {{index}}',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        a11y: {\n          liveRegion: $((\"<span class=\\\"\" + (swiper.params.a11y.notificationClass) + \"\\\" aria-live=\\\"assertive\\\" aria-atomic=\\\"true\\\"></span>\")),\n        },\n      });\n      Object.keys(a11y).forEach(function (methodName) {\n        swiper.a11y[methodName] = a11y[methodName].bind(swiper);\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.init();\n        swiper.a11y.updateNavigation();\n      },\n      toEdge: function toEdge() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.updateNavigation();\n      },\n      fromEdge: function fromEdge() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.updateNavigation();\n      },\n      paginationUpdate: function paginationUpdate() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.updatePagination();\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (!swiper.params.a11y.enabled) { return; }\n        swiper.a11y.destroy();\n      },\n    },\n  };\n\n  var History = {\n    init: function init() {\n      var swiper = this;\n      if (!swiper.params.history) { return; }\n      if (!win.history || !win.history.pushState) {\n        swiper.params.history.enabled = false;\n        swiper.params.hashNavigation.enabled = true;\n        return;\n      }\n      var history = swiper.history;\n      history.initialized = true;\n      history.paths = History.getPathValues();\n      if (!history.paths.key && !history.paths.value) { return; }\n      history.scrollToSlide(0, history.paths.value, swiper.params.runCallbacksOnInit);\n      if (!swiper.params.history.replaceState) {\n        win.addEventListener('popstate', swiper.history.setHistoryPopState);\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      if (!swiper.params.history.replaceState) {\n        win.removeEventListener('popstate', swiper.history.setHistoryPopState);\n      }\n    },\n    setHistoryPopState: function setHistoryPopState() {\n      var swiper = this;\n      swiper.history.paths = History.getPathValues();\n      swiper.history.scrollToSlide(swiper.params.speed, swiper.history.paths.value, false);\n    },\n    getPathValues: function getPathValues() {\n      var pathArray = win.location.pathname.slice(1).split('/').filter(function (part) { return part !== ''; });\n      var total = pathArray.length;\n      var key = pathArray[total - 2];\n      var value = pathArray[total - 1];\n      return { key: key, value: value };\n    },\n    setHistory: function setHistory(key, index) {\n      var swiper = this;\n      if (!swiper.history.initialized || !swiper.params.history.enabled) { return; }\n      var slide = swiper.slides.eq(index);\n      var value = History.slugify(slide.attr('data-history'));\n      if (!win.location.pathname.includes(key)) {\n        value = key + \"/\" + value;\n      }\n      var currentState = win.history.state;\n      if (currentState && currentState.value === value) {\n        return;\n      }\n      if (swiper.params.history.replaceState) {\n        win.history.replaceState({ value: value }, null, value);\n      } else {\n        win.history.pushState({ value: value }, null, value);\n      }\n    },\n    slugify: function slugify(text) {\n      return text.toString()\n        .replace(/\\s+/g, '-')\n        .replace(/[^\\w-]+/g, '')\n        .replace(/--+/g, '-')\n        .replace(/^-+/, '')\n        .replace(/-+$/, '');\n    },\n    scrollToSlide: function scrollToSlide(speed, value, runCallbacks) {\n      var swiper = this;\n      if (value) {\n        for (var i = 0, length = swiper.slides.length; i < length; i += 1) {\n          var slide = swiper.slides.eq(i);\n          var slideHistory = History.slugify(slide.attr('data-history'));\n          if (slideHistory === value && !slide.hasClass(swiper.params.slideDuplicateClass)) {\n            var index = slide.index();\n            swiper.slideTo(index, speed, runCallbacks);\n          }\n        }\n      } else {\n        swiper.slideTo(0, speed, runCallbacks);\n      }\n    },\n  };\n\n  var History$1 = {\n    name: 'history',\n    params: {\n      history: {\n        enabled: false,\n        replaceState: false,\n        key: 'slides',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        history: {\n          init: History.init.bind(swiper),\n          setHistory: History.setHistory.bind(swiper),\n          setHistoryPopState: History.setHistoryPopState.bind(swiper),\n          scrollToSlide: History.scrollToSlide.bind(swiper),\n          destroy: History.destroy.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.history.enabled) {\n          swiper.history.init();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.params.history.enabled) {\n          swiper.history.destroy();\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.history.initialized) {\n          swiper.history.setHistory(swiper.params.history.key, swiper.activeIndex);\n        }\n      },\n    },\n  };\n\n  var HashNavigation = {\n    onHashCange: function onHashCange() {\n      var swiper = this;\n      var newHash = doc.location.hash.replace('#', '');\n      var activeSlideHash = swiper.slides.eq(swiper.activeIndex).attr('data-hash');\n      if (newHash !== activeSlideHash) {\n        var newIndex = swiper.$wrapperEl.children((\".\" + (swiper.params.slideClass) + \"[data-hash=\\\"\" + newHash + \"\\\"]\")).index();\n        if (typeof newIndex === 'undefined') { return; }\n        swiper.slideTo(newIndex);\n      }\n    },\n    setHash: function setHash() {\n      var swiper = this;\n      if (!swiper.hashNavigation.initialized || !swiper.params.hashNavigation.enabled) { return; }\n      if (swiper.params.hashNavigation.replaceState && win.history && win.history.replaceState) {\n        win.history.replaceState(null, null, ((\"#\" + (swiper.slides.eq(swiper.activeIndex).attr('data-hash'))) || ''));\n      } else {\n        var slide = swiper.slides.eq(swiper.activeIndex);\n        var hash = slide.attr('data-hash') || slide.attr('data-history');\n        doc.location.hash = hash || '';\n      }\n    },\n    init: function init() {\n      var swiper = this;\n      if (!swiper.params.hashNavigation.enabled || (swiper.params.history && swiper.params.history.enabled)) { return; }\n      swiper.hashNavigation.initialized = true;\n      var hash = doc.location.hash.replace('#', '');\n      if (hash) {\n        var speed = 0;\n        for (var i = 0, length = swiper.slides.length; i < length; i += 1) {\n          var slide = swiper.slides.eq(i);\n          var slideHash = slide.attr('data-hash') || slide.attr('data-history');\n          if (slideHash === hash && !slide.hasClass(swiper.params.slideDuplicateClass)) {\n            var index = slide.index();\n            swiper.slideTo(index, speed, swiper.params.runCallbacksOnInit, true);\n          }\n        }\n      }\n      if (swiper.params.hashNavigation.watchState) {\n        $(win).on('hashchange', swiper.hashNavigation.onHashCange);\n      }\n    },\n    destroy: function destroy() {\n      var swiper = this;\n      if (swiper.params.hashNavigation.watchState) {\n        $(win).off('hashchange', swiper.hashNavigation.onHashCange);\n      }\n    },\n  };\n  var HashNavigation$1 = {\n    name: 'hash-navigation',\n    params: {\n      hashNavigation: {\n        enabled: false,\n        replaceState: false,\n        watchState: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        hashNavigation: {\n          initialized: false,\n          init: HashNavigation.init.bind(swiper),\n          destroy: HashNavigation.destroy.bind(swiper),\n          setHash: HashNavigation.setHash.bind(swiper),\n          onHashCange: HashNavigation.onHashCange.bind(swiper),\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.hashNavigation.enabled) {\n          swiper.hashNavigation.init();\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.params.hashNavigation.enabled) {\n          swiper.hashNavigation.destroy();\n        }\n      },\n      transitionEnd: function transitionEnd() {\n        var swiper = this;\n        if (swiper.hashNavigation.initialized) {\n          swiper.hashNavigation.setHash();\n        }\n      },\n    },\n  };\n\n  /* eslint no-underscore-dangle: \"off\" */\n\n  var Autoplay = {\n    run: function run() {\n      var swiper = this;\n      var $activeSlideEl = swiper.slides.eq(swiper.activeIndex);\n      var delay = swiper.params.autoplay.delay;\n      if ($activeSlideEl.attr('data-swiper-autoplay')) {\n        delay = $activeSlideEl.attr('data-swiper-autoplay') || swiper.params.autoplay.delay;\n      }\n      swiper.autoplay.timeout = Utils.nextTick(function () {\n        if (swiper.params.autoplay.reverseDirection) {\n          if (swiper.params.loop) {\n            swiper.loopFix();\n            swiper.slidePrev(swiper.params.speed, true, true);\n            swiper.emit('autoplay');\n          } else if (!swiper.isBeginning) {\n            swiper.slidePrev(swiper.params.speed, true, true);\n            swiper.emit('autoplay');\n          } else if (!swiper.params.autoplay.stopOnLastSlide) {\n            swiper.slideTo(swiper.slides.length - 1, swiper.params.speed, true, true);\n            swiper.emit('autoplay');\n          } else {\n            swiper.autoplay.stop();\n          }\n        } else if (swiper.params.loop) {\n          swiper.loopFix();\n          swiper.slideNext(swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else if (!swiper.isEnd) {\n          swiper.slideNext(swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, swiper.params.speed, true, true);\n          swiper.emit('autoplay');\n        } else {\n          swiper.autoplay.stop();\n        }\n      }, delay);\n    },\n    start: function start() {\n      var swiper = this;\n      if (typeof swiper.autoplay.timeout !== 'undefined') { return false; }\n      if (swiper.autoplay.running) { return false; }\n      swiper.autoplay.running = true;\n      swiper.emit('autoplayStart');\n      swiper.autoplay.run();\n      return true;\n    },\n    stop: function stop() {\n      var swiper = this;\n      if (!swiper.autoplay.running) { return false; }\n      if (typeof swiper.autoplay.timeout === 'undefined') { return false; }\n\n      if (swiper.autoplay.timeout) {\n        clearTimeout(swiper.autoplay.timeout);\n        swiper.autoplay.timeout = undefined;\n      }\n      swiper.autoplay.running = false;\n      swiper.emit('autoplayStop');\n      return true;\n    },\n    pause: function pause(speed) {\n      var swiper = this;\n      if (!swiper.autoplay.running) { return; }\n      if (swiper.autoplay.paused) { return; }\n      if (swiper.autoplay.timeout) { clearTimeout(swiper.autoplay.timeout); }\n      swiper.autoplay.paused = true;\n      if (speed === 0 || !swiper.params.autoplay.waitForTransition) {\n        swiper.autoplay.paused = false;\n        swiper.autoplay.run();\n      } else {\n        swiper.$wrapperEl[0].addEventListener('transitionend', swiper.autoplay.onTransitionEnd);\n        swiper.$wrapperEl[0].addEventListener('webkitTransitionEnd', swiper.autoplay.onTransitionEnd);\n      }\n    },\n  };\n\n  var Autoplay$1 = {\n    name: 'autoplay',\n    params: {\n      autoplay: {\n        enabled: false,\n        delay: 3000,\n        waitForTransition: true,\n        disableOnInteraction: true,\n        stopOnLastSlide: false,\n        reverseDirection: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        autoplay: {\n          running: false,\n          paused: false,\n          run: Autoplay.run.bind(swiper),\n          start: Autoplay.start.bind(swiper),\n          stop: Autoplay.stop.bind(swiper),\n          pause: Autoplay.pause.bind(swiper),\n          onTransitionEnd: function onTransitionEnd(e) {\n            if (!swiper || swiper.destroyed || !swiper.$wrapperEl) { return; }\n            if (e.target !== this) { return; }\n            swiper.$wrapperEl[0].removeEventListener('transitionend', swiper.autoplay.onTransitionEnd);\n            swiper.$wrapperEl[0].removeEventListener('webkitTransitionEnd', swiper.autoplay.onTransitionEnd);\n            swiper.autoplay.paused = false;\n            if (!swiper.autoplay.running) {\n              swiper.autoplay.stop();\n            } else {\n              swiper.autoplay.run();\n            }\n          },\n        },\n      });\n    },\n    on: {\n      init: function init() {\n        var swiper = this;\n        if (swiper.params.autoplay.enabled) {\n          swiper.autoplay.start();\n        }\n      },\n      beforeTransitionStart: function beforeTransitionStart(speed, internal) {\n        var swiper = this;\n        if (swiper.autoplay.running) {\n          if (internal || !swiper.params.autoplay.disableOnInteraction) {\n            swiper.autoplay.pause(speed);\n          } else {\n            swiper.autoplay.stop();\n          }\n        }\n      },\n      sliderFirstMove: function sliderFirstMove() {\n        var swiper = this;\n        if (swiper.autoplay.running) {\n          if (swiper.params.autoplay.disableOnInteraction) {\n            swiper.autoplay.stop();\n          } else {\n            swiper.autoplay.pause();\n          }\n        }\n      },\n      destroy: function destroy() {\n        var swiper = this;\n        if (swiper.autoplay.running) {\n          swiper.autoplay.stop();\n        }\n      },\n    },\n  };\n\n  var Fade = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var slides = swiper.slides;\n      for (var i = 0; i < slides.length; i += 1) {\n        var $slideEl = swiper.slides.eq(i);\n        var offset = $slideEl[0].swiperSlideOffset;\n        var tx = -offset;\n        if (!swiper.params.virtualTranslate) { tx -= swiper.translate; }\n        var ty = 0;\n        if (!swiper.isHorizontal()) {\n          ty = tx;\n          tx = 0;\n        }\n        var slideOpacity = swiper.params.fadeEffect.crossFade\n          ? Math.max(1 - Math.abs($slideEl[0].progress), 0)\n          : 1 + Math.min(Math.max($slideEl[0].progress, -1), 0);\n        $slideEl\n          .css({\n            opacity: slideOpacity,\n          })\n          .transform((\"translate3d(\" + tx + \"px, \" + ty + \"px, 0px)\"));\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      var slides = swiper.slides;\n      var $wrapperEl = swiper.$wrapperEl;\n      slides.transition(duration);\n      if (swiper.params.virtualTranslate && duration !== 0) {\n        var eventTriggered = false;\n        slides.transitionEnd(function () {\n          if (eventTriggered) { return; }\n          if (!swiper || swiper.destroyed) { return; }\n          eventTriggered = true;\n          swiper.animating = false;\n          var triggerEvents = ['webkitTransitionEnd', 'transitionend'];\n          for (var i = 0; i < triggerEvents.length; i += 1) {\n            $wrapperEl.trigger(triggerEvents[i]);\n          }\n        });\n      }\n    },\n  };\n\n  var EffectFade = {\n    name: 'effect-fade',\n    params: {\n      fadeEffect: {\n        crossFade: false,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        fadeEffect: {\n          setTranslate: Fade.setTranslate.bind(swiper),\n          setTransition: Fade.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'fade') { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"fade\"));\n        var overwriteParams = {\n          slidesPerView: 1,\n          slidesPerColumn: 1,\n          slidesPerGroup: 1,\n          watchSlidesProgress: true,\n          spaceBetween: 0,\n          virtualTranslate: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'fade') { return; }\n        swiper.fadeEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'fade') { return; }\n        swiper.fadeEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Cube = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var $el = swiper.$el;\n      var $wrapperEl = swiper.$wrapperEl;\n      var slides = swiper.slides;\n      var swiperWidth = swiper.width;\n      var swiperHeight = swiper.height;\n      var rtl = swiper.rtlTranslate;\n      var swiperSize = swiper.size;\n      var params = swiper.params.cubeEffect;\n      var isHorizontal = swiper.isHorizontal();\n      var isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n      var wrapperRotate = 0;\n      var $cubeShadowEl;\n      if (params.shadow) {\n        if (isHorizontal) {\n          $cubeShadowEl = $wrapperEl.find('.swiper-cube-shadow');\n          if ($cubeShadowEl.length === 0) {\n            $cubeShadowEl = $('<div class=\"swiper-cube-shadow\"></div>');\n            $wrapperEl.append($cubeShadowEl);\n          }\n          $cubeShadowEl.css({ height: (swiperWidth + \"px\") });\n        } else {\n          $cubeShadowEl = $el.find('.swiper-cube-shadow');\n          if ($cubeShadowEl.length === 0) {\n            $cubeShadowEl = $('<div class=\"swiper-cube-shadow\"></div>');\n            $el.append($cubeShadowEl);\n          }\n        }\n      }\n      for (var i = 0; i < slides.length; i += 1) {\n        var $slideEl = slides.eq(i);\n        var slideIndex = i;\n        if (isVirtual) {\n          slideIndex = parseInt($slideEl.attr('data-swiper-slide-index'), 10);\n        }\n        var slideAngle = slideIndex * 90;\n        var round = Math.floor(slideAngle / 360);\n        if (rtl) {\n          slideAngle = -slideAngle;\n          round = Math.floor(-slideAngle / 360);\n        }\n        var progress = Math.max(Math.min($slideEl[0].progress, 1), -1);\n        var tx = 0;\n        var ty = 0;\n        var tz = 0;\n        if (slideIndex % 4 === 0) {\n          tx = -round * 4 * swiperSize;\n          tz = 0;\n        } else if ((slideIndex - 1) % 4 === 0) {\n          tx = 0;\n          tz = -round * 4 * swiperSize;\n        } else if ((slideIndex - 2) % 4 === 0) {\n          tx = swiperSize + (round * 4 * swiperSize);\n          tz = swiperSize;\n        } else if ((slideIndex - 3) % 4 === 0) {\n          tx = -swiperSize;\n          tz = (3 * swiperSize) + (swiperSize * 4 * round);\n        }\n        if (rtl) {\n          tx = -tx;\n        }\n\n        if (!isHorizontal) {\n          ty = tx;\n          tx = 0;\n        }\n\n        var transform = \"rotateX(\" + (isHorizontal ? 0 : -slideAngle) + \"deg) rotateY(\" + (isHorizontal ? slideAngle : 0) + \"deg) translate3d(\" + tx + \"px, \" + ty + \"px, \" + tz + \"px)\";\n        if (progress <= 1 && progress > -1) {\n          wrapperRotate = (slideIndex * 90) + (progress * 90);\n          if (rtl) { wrapperRotate = (-slideIndex * 90) - (progress * 90); }\n        }\n        $slideEl.transform(transform);\n        if (params.slideShadows) {\n          // Set shadows\n          var shadowBefore = isHorizontal ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n          var shadowAfter = isHorizontal ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n          if (shadowBefore.length === 0) {\n            shadowBefore = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'left' : 'top') + \"\\\"></div>\"));\n            $slideEl.append(shadowBefore);\n          }\n          if (shadowAfter.length === 0) {\n            shadowAfter = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'right' : 'bottom') + \"\\\"></div>\"));\n            $slideEl.append(shadowAfter);\n          }\n          if (shadowBefore.length) { shadowBefore[0].style.opacity = Math.max(-progress, 0); }\n          if (shadowAfter.length) { shadowAfter[0].style.opacity = Math.max(progress, 0); }\n        }\n      }\n      $wrapperEl.css({\n        '-webkit-transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n        '-moz-transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n        '-ms-transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n        'transform-origin': (\"50% 50% -\" + (swiperSize / 2) + \"px\"),\n      });\n\n      if (params.shadow) {\n        if (isHorizontal) {\n          $cubeShadowEl.transform((\"translate3d(0px, \" + ((swiperWidth / 2) + params.shadowOffset) + \"px, \" + (-swiperWidth / 2) + \"px) rotateX(90deg) rotateZ(0deg) scale(\" + (params.shadowScale) + \")\"));\n        } else {\n          var shadowAngle = Math.abs(wrapperRotate) - (Math.floor(Math.abs(wrapperRotate) / 90) * 90);\n          var multiplier = 1.5 - (\n            (Math.sin((shadowAngle * 2 * Math.PI) / 360) / 2)\n            + (Math.cos((shadowAngle * 2 * Math.PI) / 360) / 2)\n          );\n          var scale1 = params.shadowScale;\n          var scale2 = params.shadowScale / multiplier;\n          var offset = params.shadowOffset;\n          $cubeShadowEl.transform((\"scale3d(\" + scale1 + \", 1, \" + scale2 + \") translate3d(0px, \" + ((swiperHeight / 2) + offset) + \"px, \" + (-swiperHeight / 2 / scale2) + \"px) rotateX(-90deg)\"));\n        }\n      }\n      var zFactor = (Browser.isSafari || Browser.isUiWebView) ? (-swiperSize / 2) : 0;\n      $wrapperEl\n        .transform((\"translate3d(0px,0,\" + zFactor + \"px) rotateX(\" + (swiper.isHorizontal() ? 0 : wrapperRotate) + \"deg) rotateY(\" + (swiper.isHorizontal() ? -wrapperRotate : 0) + \"deg)\"));\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      var $el = swiper.$el;\n      var slides = swiper.slides;\n      slides\n        .transition(duration)\n        .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n        .transition(duration);\n      if (swiper.params.cubeEffect.shadow && !swiper.isHorizontal()) {\n        $el.find('.swiper-cube-shadow').transition(duration);\n      }\n    },\n  };\n\n  var EffectCube = {\n    name: 'effect-cube',\n    params: {\n      cubeEffect: {\n        slideShadows: true,\n        shadow: true,\n        shadowOffset: 20,\n        shadowScale: 0.94,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        cubeEffect: {\n          setTranslate: Cube.setTranslate.bind(swiper),\n          setTransition: Cube.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'cube') { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"cube\"));\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"3d\"));\n        var overwriteParams = {\n          slidesPerView: 1,\n          slidesPerColumn: 1,\n          slidesPerGroup: 1,\n          watchSlidesProgress: true,\n          resistanceRatio: 0,\n          spaceBetween: 0,\n          centeredSlides: false,\n          virtualTranslate: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'cube') { return; }\n        swiper.cubeEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'cube') { return; }\n        swiper.cubeEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Flip = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var slides = swiper.slides;\n      var rtl = swiper.rtlTranslate;\n      for (var i = 0; i < slides.length; i += 1) {\n        var $slideEl = slides.eq(i);\n        var progress = $slideEl[0].progress;\n        if (swiper.params.flipEffect.limitRotation) {\n          progress = Math.max(Math.min($slideEl[0].progress, 1), -1);\n        }\n        var offset = $slideEl[0].swiperSlideOffset;\n        var rotate = -180 * progress;\n        var rotateY = rotate;\n        var rotateX = 0;\n        var tx = -offset;\n        var ty = 0;\n        if (!swiper.isHorizontal()) {\n          ty = tx;\n          tx = 0;\n          rotateX = -rotateY;\n          rotateY = 0;\n        } else if (rtl) {\n          rotateY = -rotateY;\n        }\n\n        $slideEl[0].style.zIndex = -Math.abs(Math.round(progress)) + slides.length;\n\n        if (swiper.params.flipEffect.slideShadows) {\n          // Set shadows\n          var shadowBefore = swiper.isHorizontal() ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n          var shadowAfter = swiper.isHorizontal() ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n          if (shadowBefore.length === 0) {\n            shadowBefore = $((\"<div class=\\\"swiper-slide-shadow-\" + (swiper.isHorizontal() ? 'left' : 'top') + \"\\\"></div>\"));\n            $slideEl.append(shadowBefore);\n          }\n          if (shadowAfter.length === 0) {\n            shadowAfter = $((\"<div class=\\\"swiper-slide-shadow-\" + (swiper.isHorizontal() ? 'right' : 'bottom') + \"\\\"></div>\"));\n            $slideEl.append(shadowAfter);\n          }\n          if (shadowBefore.length) { shadowBefore[0].style.opacity = Math.max(-progress, 0); }\n          if (shadowAfter.length) { shadowAfter[0].style.opacity = Math.max(progress, 0); }\n        }\n        $slideEl\n          .transform((\"translate3d(\" + tx + \"px, \" + ty + \"px, 0px) rotateX(\" + rotateX + \"deg) rotateY(\" + rotateY + \"deg)\"));\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      var slides = swiper.slides;\n      var activeIndex = swiper.activeIndex;\n      var $wrapperEl = swiper.$wrapperEl;\n      slides\n        .transition(duration)\n        .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n        .transition(duration);\n      if (swiper.params.virtualTranslate && duration !== 0) {\n        var eventTriggered = false;\n        // eslint-disable-next-line\n        slides.eq(activeIndex).transitionEnd(function onTransitionEnd() {\n          if (eventTriggered) { return; }\n          if (!swiper || swiper.destroyed) { return; }\n          // if (!$(this).hasClass(swiper.params.slideActiveClass)) return;\n          eventTriggered = true;\n          swiper.animating = false;\n          var triggerEvents = ['webkitTransitionEnd', 'transitionend'];\n          for (var i = 0; i < triggerEvents.length; i += 1) {\n            $wrapperEl.trigger(triggerEvents[i]);\n          }\n        });\n      }\n    },\n  };\n\n  var EffectFlip = {\n    name: 'effect-flip',\n    params: {\n      flipEffect: {\n        slideShadows: true,\n        limitRotation: true,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        flipEffect: {\n          setTranslate: Flip.setTranslate.bind(swiper),\n          setTransition: Flip.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'flip') { return; }\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"flip\"));\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"3d\"));\n        var overwriteParams = {\n          slidesPerView: 1,\n          slidesPerColumn: 1,\n          slidesPerGroup: 1,\n          watchSlidesProgress: true,\n          spaceBetween: 0,\n          virtualTranslate: true,\n        };\n        Utils.extend(swiper.params, overwriteParams);\n        Utils.extend(swiper.originalParams, overwriteParams);\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'flip') { return; }\n        swiper.flipEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'flip') { return; }\n        swiper.flipEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Coverflow = {\n    setTranslate: function setTranslate() {\n      var swiper = this;\n      var swiperWidth = swiper.width;\n      var swiperHeight = swiper.height;\n      var slides = swiper.slides;\n      var $wrapperEl = swiper.$wrapperEl;\n      var slidesSizesGrid = swiper.slidesSizesGrid;\n      var params = swiper.params.coverflowEffect;\n      var isHorizontal = swiper.isHorizontal();\n      var transform = swiper.translate;\n      var center = isHorizontal ? -transform + (swiperWidth / 2) : -transform + (swiperHeight / 2);\n      var rotate = isHorizontal ? params.rotate : -params.rotate;\n      var translate = params.depth;\n      // Each slide offset from center\n      for (var i = 0, length = slides.length; i < length; i += 1) {\n        var $slideEl = slides.eq(i);\n        var slideSize = slidesSizesGrid[i];\n        var slideOffset = $slideEl[0].swiperSlideOffset;\n        var offsetMultiplier = ((center - slideOffset - (slideSize / 2)) / slideSize) * params.modifier;\n\n        var rotateY = isHorizontal ? rotate * offsetMultiplier : 0;\n        var rotateX = isHorizontal ? 0 : rotate * offsetMultiplier;\n        // var rotateZ = 0\n        var translateZ = -translate * Math.abs(offsetMultiplier);\n\n        var translateY = isHorizontal ? 0 : params.stretch * (offsetMultiplier);\n        var translateX = isHorizontal ? params.stretch * (offsetMultiplier) : 0;\n\n        // Fix for ultra small values\n        if (Math.abs(translateX) < 0.001) { translateX = 0; }\n        if (Math.abs(translateY) < 0.001) { translateY = 0; }\n        if (Math.abs(translateZ) < 0.001) { translateZ = 0; }\n        if (Math.abs(rotateY) < 0.001) { rotateY = 0; }\n        if (Math.abs(rotateX) < 0.001) { rotateX = 0; }\n\n        var slideTransform = \"translate3d(\" + translateX + \"px,\" + translateY + \"px,\" + translateZ + \"px)  rotateX(\" + rotateX + \"deg) rotateY(\" + rotateY + \"deg)\";\n\n        $slideEl.transform(slideTransform);\n        $slideEl[0].style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n        if (params.slideShadows) {\n          // Set shadows\n          var $shadowBeforeEl = isHorizontal ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n          var $shadowAfterEl = isHorizontal ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n          if ($shadowBeforeEl.length === 0) {\n            $shadowBeforeEl = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'left' : 'top') + \"\\\"></div>\"));\n            $slideEl.append($shadowBeforeEl);\n          }\n          if ($shadowAfterEl.length === 0) {\n            $shadowAfterEl = $((\"<div class=\\\"swiper-slide-shadow-\" + (isHorizontal ? 'right' : 'bottom') + \"\\\"></div>\"));\n            $slideEl.append($shadowAfterEl);\n          }\n          if ($shadowBeforeEl.length) { $shadowBeforeEl[0].style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0; }\n          if ($shadowAfterEl.length) { $shadowAfterEl[0].style.opacity = (-offsetMultiplier) > 0 ? -offsetMultiplier : 0; }\n        }\n      }\n\n      // Set correct perspective for IE10\n      if (Support.pointerEvents || Support.prefixedPointerEvents) {\n        var ws = $wrapperEl[0].style;\n        ws.perspectiveOrigin = center + \"px 50%\";\n      }\n    },\n    setTransition: function setTransition(duration) {\n      var swiper = this;\n      swiper.slides\n        .transition(duration)\n        .find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left')\n        .transition(duration);\n    },\n  };\n\n  var EffectCoverflow = {\n    name: 'effect-coverflow',\n    params: {\n      coverflowEffect: {\n        rotate: 50,\n        stretch: 0,\n        depth: 100,\n        modifier: 1,\n        slideShadows: true,\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        coverflowEffect: {\n          setTranslate: Coverflow.setTranslate.bind(swiper),\n          setTransition: Coverflow.setTransition.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        if (swiper.params.effect !== 'coverflow') { return; }\n\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"coverflow\"));\n        swiper.classNames.push(((swiper.params.containerModifierClass) + \"3d\"));\n\n        swiper.params.watchSlidesProgress = true;\n        swiper.originalParams.watchSlidesProgress = true;\n      },\n      setTranslate: function setTranslate() {\n        var swiper = this;\n        if (swiper.params.effect !== 'coverflow') { return; }\n        swiper.coverflowEffect.setTranslate();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        if (swiper.params.effect !== 'coverflow') { return; }\n        swiper.coverflowEffect.setTransition(duration);\n      },\n    },\n  };\n\n  var Thumbs = {\n    init: function init() {\n      var swiper = this;\n      var ref = swiper.params;\n      var thumbsParams = ref.thumbs;\n      var SwiperClass = swiper.constructor;\n      if (thumbsParams.swiper instanceof SwiperClass) {\n        swiper.thumbs.swiper = thumbsParams.swiper;\n        Utils.extend(swiper.thumbs.swiper.originalParams, {\n          watchSlidesProgress: true,\n          slideToClickedSlide: false,\n        });\n        Utils.extend(swiper.thumbs.swiper.params, {\n          watchSlidesProgress: true,\n          slideToClickedSlide: false,\n        });\n      } else if (Utils.isObject(thumbsParams.swiper)) {\n        swiper.thumbs.swiper = new SwiperClass(Utils.extend({}, thumbsParams.swiper, {\n          watchSlidesVisibility: true,\n          watchSlidesProgress: true,\n          slideToClickedSlide: false,\n        }));\n        swiper.thumbs.swiperCreated = true;\n      }\n      swiper.thumbs.swiper.$el.addClass(swiper.params.thumbs.thumbsContainerClass);\n      swiper.thumbs.swiper.on('tap', swiper.thumbs.onThumbClick);\n    },\n    onThumbClick: function onThumbClick() {\n      var swiper = this;\n      var thumbsSwiper = swiper.thumbs.swiper;\n      if (!thumbsSwiper) { return; }\n      var clickedIndex = thumbsSwiper.clickedIndex;\n      var clickedSlide = thumbsSwiper.clickedSlide;\n      if (clickedSlide && $(clickedSlide).hasClass(swiper.params.thumbs.slideThumbActiveClass)) { return; }\n      if (typeof clickedIndex === 'undefined' || clickedIndex === null) { return; }\n      var slideToIndex;\n      if (thumbsSwiper.params.loop) {\n        slideToIndex = parseInt($(thumbsSwiper.clickedSlide).attr('data-swiper-slide-index'), 10);\n      } else {\n        slideToIndex = clickedIndex;\n      }\n      if (swiper.params.loop) {\n        var currentIndex = swiper.activeIndex;\n        if (swiper.slides.eq(currentIndex).hasClass(swiper.params.slideDuplicateClass)) {\n          swiper.loopFix();\n          // eslint-disable-next-line\n          swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n          currentIndex = swiper.activeIndex;\n        }\n        var prevIndex = swiper.slides.eq(currentIndex).prevAll((\"[data-swiper-slide-index=\\\"\" + slideToIndex + \"\\\"]\")).eq(0).index();\n        var nextIndex = swiper.slides.eq(currentIndex).nextAll((\"[data-swiper-slide-index=\\\"\" + slideToIndex + \"\\\"]\")).eq(0).index();\n        if (typeof prevIndex === 'undefined') { slideToIndex = nextIndex; }\n        else if (typeof nextIndex === 'undefined') { slideToIndex = prevIndex; }\n        else if (nextIndex - currentIndex < currentIndex - prevIndex) { slideToIndex = nextIndex; }\n        else { slideToIndex = prevIndex; }\n      }\n      swiper.slideTo(slideToIndex);\n    },\n    update: function update(initial) {\n      var swiper = this;\n      var thumbsSwiper = swiper.thumbs.swiper;\n      if (!thumbsSwiper) { return; }\n\n      var slidesPerView = thumbsSwiper.params.slidesPerView === 'auto'\n        ? thumbsSwiper.slidesPerViewDynamic()\n        : thumbsSwiper.params.slidesPerView;\n\n      if (swiper.realIndex !== thumbsSwiper.realIndex) {\n        var currentThumbsIndex = thumbsSwiper.activeIndex;\n        var newThumbsIndex;\n        if (thumbsSwiper.params.loop) {\n          if (thumbsSwiper.slides.eq(currentThumbsIndex).hasClass(thumbsSwiper.params.slideDuplicateClass)) {\n            thumbsSwiper.loopFix();\n            // eslint-disable-next-line\n            thumbsSwiper._clientLeft = thumbsSwiper.$wrapperEl[0].clientLeft;\n            currentThumbsIndex = thumbsSwiper.activeIndex;\n          }\n          // Find actual thumbs index to slide to\n          var prevThumbsIndex = thumbsSwiper.slides.eq(currentThumbsIndex).prevAll((\"[data-swiper-slide-index=\\\"\" + (swiper.realIndex) + \"\\\"]\")).eq(0).index();\n          var nextThumbsIndex = thumbsSwiper.slides.eq(currentThumbsIndex).nextAll((\"[data-swiper-slide-index=\\\"\" + (swiper.realIndex) + \"\\\"]\")).eq(0).index();\n          if (typeof prevThumbsIndex === 'undefined') { newThumbsIndex = nextThumbsIndex; }\n          else if (typeof nextThumbsIndex === 'undefined') { newThumbsIndex = prevThumbsIndex; }\n          else if (nextThumbsIndex - currentThumbsIndex === currentThumbsIndex - prevThumbsIndex) { newThumbsIndex = currentThumbsIndex; }\n          else if (nextThumbsIndex - currentThumbsIndex < currentThumbsIndex - prevThumbsIndex) { newThumbsIndex = nextThumbsIndex; }\n          else { newThumbsIndex = prevThumbsIndex; }\n        } else {\n          newThumbsIndex = swiper.realIndex;\n        }\n        if (thumbsSwiper.visibleSlidesIndexes.indexOf(newThumbsIndex) < 0) {\n          if (thumbsSwiper.params.centeredSlides) {\n            if (newThumbsIndex > currentThumbsIndex) {\n              newThumbsIndex = newThumbsIndex - Math.floor(slidesPerView / 2) + 1;\n            } else {\n              newThumbsIndex = newThumbsIndex + Math.floor(slidesPerView / 2) - 1;\n            }\n          } else if (newThumbsIndex > currentThumbsIndex) {\n            newThumbsIndex = newThumbsIndex - slidesPerView + 1;\n          }\n          thumbsSwiper.slideTo(newThumbsIndex, initial ? 0 : undefined);\n        }\n      }\n\n      // Activate thumbs\n      var thumbsToActivate = 1;\n      var thumbActiveClass = swiper.params.thumbs.slideThumbActiveClass;\n\n      if (swiper.params.slidesPerView > 1 && !swiper.params.centeredSlides) {\n        thumbsToActivate = swiper.params.slidesPerView;\n      }\n\n      thumbsSwiper.slides.removeClass(thumbActiveClass);\n      if (thumbsSwiper.params.loop) {\n        for (var i = 0; i < thumbsToActivate; i += 1) {\n          thumbsSwiper.$wrapperEl.children((\"[data-swiper-slide-index=\\\"\" + (swiper.realIndex + i) + \"\\\"]\")).addClass(thumbActiveClass);\n        }\n      } else {\n        for (var i$1 = 0; i$1 < thumbsToActivate; i$1 += 1) {\n          thumbsSwiper.slides.eq(swiper.realIndex + i$1).addClass(thumbActiveClass);\n        }\n      }\n    },\n  };\n  var Thumbs$1 = {\n    name: 'thumbs',\n    params: {\n      thumbs: {\n        swiper: null,\n        slideThumbActiveClass: 'swiper-slide-thumb-active',\n        thumbsContainerClass: 'swiper-container-thumbs',\n      },\n    },\n    create: function create() {\n      var swiper = this;\n      Utils.extend(swiper, {\n        thumbs: {\n          swiper: null,\n          init: Thumbs.init.bind(swiper),\n          update: Thumbs.update.bind(swiper),\n          onThumbClick: Thumbs.onThumbClick.bind(swiper),\n        },\n      });\n    },\n    on: {\n      beforeInit: function beforeInit() {\n        var swiper = this;\n        var ref = swiper.params;\n        var thumbs = ref.thumbs;\n        if (!thumbs || !thumbs.swiper) { return; }\n        swiper.thumbs.init();\n        swiper.thumbs.update(true);\n      },\n      slideChange: function slideChange() {\n        var swiper = this;\n        if (!swiper.thumbs.swiper) { return; }\n        swiper.thumbs.update();\n      },\n      update: function update() {\n        var swiper = this;\n        if (!swiper.thumbs.swiper) { return; }\n        swiper.thumbs.update();\n      },\n      resize: function resize() {\n        var swiper = this;\n        if (!swiper.thumbs.swiper) { return; }\n        swiper.thumbs.update();\n      },\n      observerUpdate: function observerUpdate() {\n        var swiper = this;\n        if (!swiper.thumbs.swiper) { return; }\n        swiper.thumbs.update();\n      },\n      setTransition: function setTransition(duration) {\n        var swiper = this;\n        var thumbsSwiper = swiper.thumbs.swiper;\n        if (!thumbsSwiper) { return; }\n        thumbsSwiper.setTransition(duration);\n      },\n      beforeDestroy: function beforeDestroy() {\n        var swiper = this;\n        var thumbsSwiper = swiper.thumbs.swiper;\n        if (!thumbsSwiper) { return; }\n        if (swiper.thumbs.swiperCreated && thumbsSwiper) {\n          thumbsSwiper.destroy();\n        }\n      },\n    },\n  };\n\n  // Swiper Class\n\n  var components = [\n    Device$1,\n    Support$1,\n    Browser$1,\n    Resize,\n    Observer$1,\n    Virtual$1,\n    Keyboard$1,\n    Mousewheel$1,\n    Navigation$1,\n    Pagination$1,\n    Scrollbar$1,\n    Parallax$1,\n    Zoom$1,\n    Lazy$1,\n    Controller$1,\n    A11y,\n    History$1,\n    HashNavigation$1,\n    Autoplay$1,\n    EffectFade,\n    EffectCube,\n    EffectFlip,\n    EffectCoverflow,\n    Thumbs$1\n  ];\n\n  if (typeof Swiper.use === 'undefined') {\n    Swiper.use = Swiper.Class.use;\n    Swiper.installModule = Swiper.Class.installModule;\n  }\n\n  Swiper.use(components);\n\n  return Swiper;\n\n}));\n"]}