<?php
// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login');
}

// Display success/error messages
$success_message = '';
$error_message = '';

if (isset($_SESSION['donation_success'])) {
    $success_message = $_SESSION['donation_success'];
    unset($_SESSION['donation_success']);
}

if (isset($_SESSION['donation_error'])) {
    $error_message = $_SESSION['donation_error'];
    unset($_SESSION['donation_error']);
}

// PayPal configuration
$paypal_business = config('paypal_business_email');
$paypal_sandbox = config('paypal_sandbox');
$paypal_url = $paypal_sandbox ? 'https://www.sandbox.paypal.com/cgi-bin/webscr' : 'https://www.paypal.com/cgi-bin/webscr';
$return_url = config('paypal_return_url');
$cancel_url = config('paypal_cancel_url');
$notify_url = config('paypal_notify_url');
$currency = config('paypal_currency');
$tolls_per_euro = config('paypal_tolls_per_euro');

// Donation packages
$donation_packages = array(
    array('amount' => 2, 'tolls' => 100, 'popular' => false),
    array('amount' => 5, 'tolls' => 250, 'popular' => false),
    array('amount' => 10, 'tolls' => 500, 'popular' => true),
    array('amount' => 20, 'tolls' => 1000, 'popular' => false),
    array('amount' => 50, 'tolls' => 2500, 'popular' => false),
);
?>

<nav class="page-breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item active" aria-current="page"><?php echo lang('donation_page1') ?></li>
    </ol>
</nav>

<?php if ($success_message): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i data-feather="check-circle" class="me-2"></i>
    <?php echo $success_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>

<?php if ($error_message): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i data-feather="alert-circle" class="me-2"></i>
    <?php echo $error_message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<?php endif; ?>


