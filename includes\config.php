<?php

# MAINTENANCE MODE
$config['active'] = true;
$config['enable_maintenance_redirect'] = true;
$config['maintenance_page'] = 'http://oneaion.com/maintenance';
$config['maintenance_ip_access'] = array(
    '127.0.0.1',
);

# MAIL CONFIGURATION
$config['email_active'] = true;
$config['email_send_from'] = '<EMAIL>';
$config['email_send_name'] = 'OneAion';
# SMTP
$config['smtp_active'] = true;
$config['smtp_host'] = 'smtp.gmail.com';
$config['smtp_port'] = '465';
$config['smtp_user'] = '<EMAIL>';
$config['smtp_pass'] = 'Hckd@0987654321';

# WEBSITE CONFIGURATION
$config['website_title'] = 'Neo Aion | Is a perfect quality private European server of Aion!';
$config['server_name'] = 'Neo Aion';
$config['server_version'] = 'Neo Aion 4.6 (Dark Betrayal)';
$config['launcher_link'] = '#';
$config['client_link'] = '#';
$config['forum_news_rss'] = '';
$config['forum_events_rss'] = '';
$config['forum_url'] = '#';

# WEBSITE TEMPLATE
$config['template'] = 'default';
$config['language'] = 'en';
$config['language_default'] = 'en';
$config['language_switch_active'] = true;
$config['language_debug'] = false;
$config['debug'] = true;

# SERVER DATABASE CONFIGURATION
# GAMESERVER
$config['SQLSERVER_GS'] = '127.0.0.1';
$config['SQLDATABASE_GS'] = 'AionWorld_110';
$config['SQLUSER_GS'] = 'sa';
$config['SQLPASSWORD_GS'] = 'nMHcCAQEEIDojD@+';
$config['SQLPORT_GS'] = '1433';
#LOGINSERVER
$config['SQLSERVER_LS'] = '127.0.0.1';
$config['SQLDATABASE_LS'] = 'AionAccounts';
$config['SQLUSER_LS'] = 'sa';
$config['SQLPASSWORD_LS'] = 'nMHcCAQEEIDojD@+';
$config['SQLPORT_LS'] = '1433';
#WEBSERVER
$config['SQLSERVER_WEB'] = '127.0.0.1';
$config['SQLDATABASE_WEB'] = 'aioncms';
$config['SQLUSER_WEB'] = 'sa';
$config['SQLPASSWORD_WEB'] = 'nMHcCAQEEIDojD@+';
$config['SQLPORT_WEB'] = '1433';
#GMSERV
$config['SQLSERVER_GMSERV'] = '127.0.0.1';
$config['SQLDATABASE_GMSERV'] = 'Aion_log';
$config['SQLUSER_GMSERV'] = '';
$config['SQLPASSWORD_GMSERV'] = 'nMHcCAQEEIDojD@+';
$config['SQLPORT_GMSERV'] = '1433';

#RECAPTCHA
$config['captcha_sitekey'] = '6LeE8fwqAAAAAIBxYWSmUyUaAD5gkHhDUcxanG6U';
$config['captcha_screetkey'] = '6LeE8fwqAAAAAFCtfPnW75YhPDrFka5f-DMLRzo6';
$config['captcha_enabled_register'] = false; // Activer/désactiver le reCAPTCHA pour l'inscription
$config['captcha_enabled_login'] = false; // Activer/désactiver le reCAPTCHA pour la connexion

// StarPass Configuration
$config['starpass_idd'] = '446053'; // ID du document StarPass
$config['starpass_base_toll'] = 400; // Nombre de Toll de base pour 1 code (2€)
$config['starpass_success_url'] = __BASE_URL__ . 'donate/success';
$config['starpass_fail_url'] = __BASE_URL__ . 'donate/fail';

// PayPal Configuration
$config['paypal_business_email'] = '<EMAIL>'; // PayPal business email
$config['paypal_sandbox'] = false; // Set to true for testing, false for production
$config['paypal_currency'] = 'EUR'; // Currency code
$config['paypal_return_url'] = __BASE_URL__ . 'donate/success'; // Success return URL
$config['paypal_cancel_url'] = __BASE_URL__ . 'donate/fail'; // Cancel return URL
$config['paypal_notify_url'] = __BASE_URL__ . 'api/paypal.php'; // IPN notification URL
$config['paypal_tolls_per_euro'] = 50; // Number of tolls per 1 EUR

$config['vote_sites'] = array(
    1 => array(
        2,
        'https://topg.org/aion-private-servers/server-671692#vote',
        'topofg.gif',
        'XtremeTop100',
        array(
            'xtremetop100.com',
            'www.xtremetop100.com'
        )
    ),
    2 => array(
        2,
        'https://www.arena-top100.com/index.php?a=in&u=Neoaion',
        'topofg.gif',
        'TopG',
        array(
            'topg.org',
            'www.topg.org'
        )
    ),
    3 => array(
        2,
        'https://gtop100.com/Aion-Online/NeoAion-4-6-perfect-server-104634',
        'topofg.gif',
        'TopofGames',
        array(
            'topofgames.com',
            'www.topofgames.com'
        )
    ),
    4 => array(
        1,
        'https://serveur-prive.net/aion/httpswwwneoaionfr/vote',
        'topofg.gif',
        'TopofGames',
        array(
            'topofgames.com',
            'www.topofgames.com'
        )
    ),
    5 => array(
        1,
        'https://mmohub.com/site/1079/vote',
        'topofg.gif',
        'TopofGames',
        array(
            'topofgames.com',
            'www.topofgames.com'
        )
    ),
    6 => array(
        2,
        'https://mmtop200.com/vote/4699',
        'topofg.gif',
        'TopofGames',
        array(
            'topofgames.com',
            'www.topofgames.com'
        )
    ),
    7 => array(
        1,
        'http://www.gamingtop100.net/in-30170',
        'topofg.gif',
        'TopofGames',
        array(
            'topofgames.com',
            'www.topofgames.com'
        )
    ),
    8 => array(
        1,
        'https://www.xtremetop100.com/in.php?site=**********',
        'topofg.gif',
        'TopofGames',
        array(
            'topofgames.com',
            'www.topofgames.com'
        )
    ),

);

$config['vote_promo'] = array(
    array('2021-04-25', '2021-04-30', 5),
);

#CMS USER BAR
$config['usercp_sidebar_menu'] = array(
    array(
        'My Account',
        'usercp/account/',
        'acc_ico.png',
    ),
    array(
        'My Characters',
        'usercp/characters/',
        'char_ico.png',
    ),
    array(
        'Level Rewards',
        'levelrewards',
        'gift_ico.png',
    ),
    array(
        'Webshop',
        'shop/',
        'items_ico.png',
    ),
    array(
        'Vote for Credits',
        'usercp/vote/',
        'wsitems_ico.png',
    ),
    array(
        'Change Password',
        'usercp/password/',
        'changepass_ico.png',
    ),
);

#CMS ROUTES
$config['routes'] = array(
    // route => file_path, template, access (all, guest, user)
    '404' => array('404.php', '', 'all'),
    'login' => array('login.php', '', 'guest'),
    'logout' => array('logout.php', '', 'user'),
    'home' => array('home.php', 'home', 'all'),
    'register' => array('register.php', '', 'guest'),
    //'rankings' => array('rankings.php', '', 'all'),
    //'rankings/abyss' => array('rankings/abyss.php', '', 'all'),
    //'rankings/glory' => array('rankings/glory.php', '', 'all'),
    //'rankings/kills' => array('rankings/kills.php', '', 'all'),
    //'rankings/legions' => array('rankings/legions.php', '', 'all'),
    //'rankings/votes' => array('rankings/votes.php', '', 'all'),
    'donate' => array('donate.php', '', 'user'),
    'donate/success' => array('donate/success.php', '', 'user'),
    'donate/fail' => array('donate/fail.php', '', 'user'),
    'levelrewards' => array('levelrewards.php', '', 'user'),

    'usercp' => array('usercp.php', '', 'user'),
    'usercp/account' => array('usercp/account.php', '', 'user'),
    'usercp/password' => array('usercp/password.php', '', 'user'),
    //'usercp/email' => array('usercp/email.php', '', 'user'),
    //'usercp/verifyemail' => array('usercp/verifyemail.php', '', 'user'),
    //'usercp/securityquestions' => array('usercp/securityquestions.php', '', 'user'),
    //'usercp/securitypin' => array('usercp/securitypin.php', '', 'user'),
    //'usercp/accountlock' => array('usercp/accountlock.php', '', 'user'),
    //'usercp/validate' => array('usercp/validate.php', '', 'user'),
    'usercp/characters' => array('usercp/characters.php', '', 'user'),
    //'usercp/upgrade' => array('usercp/upgrade.php', '', 'user'),
    //'usercp/bansystem' => array('usercp/bansystem.php', '', 'user'),
    //'usercp/timexchange' => array('usercp/timexchange.php', '', 'user'),
    'usercp/vote' => array('usercp/vote.php', '', 'user'),
    //'usercp/reset' => array('usercp/reset.php', '', 'user'),
    //'usercp/inventory' => array('usercp/inventory.php', '', 'user'),
    //'usercp/unstuck' => array('usercp/unstuck.php', '', 'user'),
    //'usercp/transfer' => array('usercp/transfer.php', '', 'user'),
    //'usercp/enchantable' => array('usercp/enchantable.php', '', 'user'),
    //'usercp/enchant' => array('usercp/enchant.php', '', 'user'),
    //'usercp/changeskill' => array('usercp/changeskill.php', '', 'user'),
    //'usercp/boost' => array('usercp/boost.php', '', 'user'),
    //'usercp/voteranking' => array('usercp/voteranking.php', '', 'user'),
    //'usercp/returning' => array('usercp/returning.php', '', 'user'),
    //'usercp/redeem' => array('usercp/redeem.php', '', 'user'),
    //'usercp/redeemlogs' => array('usercp/redeemlogs.php', '', 'user'),
    //'usercp/referrals' => array('usercp/referrals.php', '', 'user'),
    //'usercp/legionprofile' => array('usercp/legionprofile.php', '', 'user'),
    //'forumevents' => array('forumevents.php', '', 'all'),
    //'lottery' => array('lottery.php', '', 'all'),
    //'lottery/buy' => array('lottery/buy.php', '', 'user'),
    //'lottery/tickets' => array('lottery/tickets.php', '', 'user'),
    //'lottery/stash' => array('lottery/stash.php', '', 'user'),
    //'lottery/results' => array('lottery/results.php', '', 'user'),
    'recovery' => array('recovery.php', '', 'guest'),
    'recovery/password' => array('recovery/password.php', '', 'guest'),
    'recovery/username' => array('recovery/username.php', '', 'guest'),
    //'rules' => array('rules.php', '', 'all'),
    'shop' => array('shop.php', '', 'user'),
    'shop/history' => array('shop/history.php', '', 'user'),
    'shop/item' => array('shop/item.php', '', 'user'),
    'shop/list' => array('shop/list.php', '', 'user'),
    'verification/email' => array('verification/email.php', '', 'all'),
    'verification/forgotpassword' => array('verification/forgotpassword.php', '', 'all'),
    'verification/password' => array('verification/password.php', '', 'all'),
    //'bansystem/view' => array('bansystem/view.php', '', 'user'),
    //'profile/legion' => array('profile/legion.php', '', 'all'),
    //'tickets/new' => array('tickets/new.php', '', 'user'),
    //'tickets/list' => array('tickets/list.php', '', 'user'),
    //'tickets/view' => array('tickets/view.php', '', 'user'),
    'lang' => array('language/switch.php', '', 'all'),
    'siege' => array('siege.php', 'home', 'all'),
    'event' => array('event.php', 'home', 'all'),
    'usercp/account/claim' => array('usercp/account/claim.php', '', 'user'),
);