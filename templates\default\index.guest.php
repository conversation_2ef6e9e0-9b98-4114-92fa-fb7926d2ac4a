<?php
/**
 * AionCMS
 * https://aioncms.com/
 * 
 * @version 4.0
 * <AUTHOR> <http://lautaroangelico.com/>
 * @copyright (c) 2012-2020 Lautaro Angelico, All Rights Reserved
 */

/**
 * 
 * For support join us on discord:
 * https://aioncms.com/discord
 * 
 */

if(!defined('access') or !access) die();
?>
<!DOCTYPE HTML>
<html>

<head>
<meta charset="utf-8" />
	<meta http-equiv="Content-type" content="text/html; charset=utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1"> 
	<!--[if lt IE 9]><script src="https://cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv.min.js"></script><![endif]-->
	<title><?php echo config('website_title'); ?></title>
	<meta name="description" content="✔100% working game platform fully matching the official game server of 4.6 version ➤ No bugs, errors and defects - absolutely everything works properly!">
    <meta name="keywords" content="aion 4.6, aion official private server, aion online, aion game, aion, aion private, neo aion, aionneo, Neoaion">
	<meta property="og:title" content="<?php echo config('website_title'); ?>"/>
    <meta property="og:description" content="✔100% working game platform fully matching the official game server of 4.6 version ➤ No bugs, errors and defects - absolutely everything works properly!">

	<link rel="icon" type="image/ico" href="<?php template_img(); ?>favicon/fav.ico">
	<meta name="msapplication-TileColor" content="#ffffff">
	<meta name="msapplication-TileImage" content="<?php template_img(); ?>favicon/ms-icon-144x144.png">
	<meta name="theme-color" content="#ffffff">
    <link rel="stylesheet" href="<?php template_css(); ?>core.css?version=<?php echo filemtime('templates/default/css/core.css') ?>">
    <link rel="stylesheet" href="<?php template_css(); ?>iconfont.css?version=<?php echo filemtime('templates/default/css/iconfont.css') ?>">
    <link rel="stylesheet" href="<?php template_css(); ?>flag-icon.min.css">
    <link rel="stylesheet" href="<?php template_css(); ?>style.css?version=<?php echo filemtime('templates/default/css/style.css') ?>">
    <link rel="stylesheet" href="<?php template_css(); ?>golden-theme.css?version=<?php echo filemtime('templates/default/css/golden-theme.css') ?>">
    <link rel="stylesheet" href="<?php template_css(); ?>added.css">
    <script>
    var baseUrl = '<?php echo __BASE_URL__; ?>';
    </script>
    <div id="fb-root"></div>
    <script async defer crossorigin="anonymous" src="https://connect.facebook.net/id_ID/sdk.js#xfbml=1&version=v10.0"
        nonce="ib9T2pWj"></script>
</head>
<body>
    <div class="main-wrapper">
        <div class="page-wrapper full-page">
                
            <div class="page-content d-flex align-items-center justify-content-center">
                <div class="row w-100 mx-0 auth-page">
                    <div class="col-md-8 col-xl-6 mx-auto">
                        <div class="card">
                            <div class="row">
                                <div class="col-md-4 pr-md-0">
                                    <div class="auth-left-wrapper-pic">
                                        
                                    </div>
                                </div>
                                <?php Handler::loadContent(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src='https://www.google.com/recaptcha/api.js'></script>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
    <script src="<?php template_js() ?>core.js"></script>
    <script src="<?php template_js() ?>feather.min.js"></script>
    <script src="<?php template_js() ?>template.js"></script>

    <!-- Simple Golden Theme Enforcement -->
    <script>
    $(document).ready(function() {
        // Simple one-time application of golden colors
        function applyGoldenTheme() {
            // Style labels only
            $('label[for="username"], label[for="password"]').css('color', '#f4e4a6');
            $('.auth-form-wrapper h5, .auth-form-wrapper .text-muted').css('color', '#f4e4a6');
            $('.auth-form-wrapper a').css('color', '#d4af37');
        }

        // Apply once on page load
        applyGoldenTheme();

        // Apply once more after a short delay
        setTimeout(applyGoldenTheme, 100);
    });
    </script>
</body>
</html>