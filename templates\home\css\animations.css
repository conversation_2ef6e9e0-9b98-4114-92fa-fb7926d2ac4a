/*------------------------------------------------------------------
[Animations Stylesheet]

Author: AtypicalThemes
Template: Strider - A Game Studio Template
Version:	1.0

------------------------------------------------------------------- */
/* KEYFRAMES */
@keyframes pop-in {
0% { opacity: 0; transform: scale(0.5); }
100% { opacity: 1; transform: scale(1); }
}

/* --- The first part of the stylesheet sets the initial position of the animation elements and sets their opacity and timing --- */
.animation-element {
    opacity: 0;
    position: relative;
    transition: all 850ms ease;
}
.animation-element.extend {
    width: 0;
}
.animation-element.fade-in {
    opacity: 0;
}
.animation-element.slide-in-left {
    -moz-transform: translate3d(-25px, 0px, 0px);
    -webkit-transform: translate3d(-25px, 0px, 0px);
    -o-transform: translate(-25px, 0px);
    -ms-transform: translate(-25px, 0px);
    transform: translate3d(-25px, 0px, 0px);
}
.animation-element.slide-in-right {
    -moz-transform: translate3d(25px, 0px, 0px);
    -webkit-transform: translate3d(25px, 0px, 0px);
    -o-transform: translate(25px, 0px);
    -ms-transform: translate(25px, 0px);
    transform: translate3d(25px, 0px, 0px);
}
.animation-element.slide-up {
    -moz-transform: translate3d(0px, 25px, 0px);
    -webkit-transform: translate3d(0px, 25px, 0px);
    -o-transform: translate(0px, 25px);
    -ms-transform: translate(0px, 25px);
    transform: translate3d(0px, 25px, 0px);
}
.animation-element.slide-down {
    -moz-transform: translate3d(0px, -25px, 0px);
    -webkit-transform: translate3d(0px, -25px, 0px);
    -o-transform: translate(0px, -25px);
    -ms-transform: translate(0px, -25px);
    transform: translate3d(0px, -25px, 0px);
}

/* --- The second part of the stylesheet sets the position and opacity of animation elements when they are "in-view"  --- */
.animation-element.in-view {
    opacity: 1;
    z-index: 5;
}
.animation-element.extend.in-view {
    width: 100%;
}
.animation-element.fade-in.in-view {
    opacity: 1;
}
.animation-element.fade-in.delayed.in-view {
    transition-delay: 200ms;
}
.animation-element.pop-in.in-view {
    -webkit-animation: pop-in 700ms;
    -moz-animation: pop-in 700ms;
    -ms-animation: pop-in 700ms;
}
.animation-element.pop-in-slow.in-view {
    -webkit-animation: pop-in  800ms;
    -moz-animation: pop-in 800ms;
    -ms-animation :pop-in 800ms;
}
.animation-element.slide-in-left.in-view {
    -moz-transform: translate3d(0px, 0px, 0px);
    -webkit-transform: translate3d(0px, 0px, 0px);
    -o-transform: translate(0px, 0px);
    -ms-transform: translate(0px, 0px);
    transform: translate3d(0px, 0px, 0px);
}
.animation-element.slide-in-left.delayed.in-view {
    -webkit-transition-delay: 200ms; 
    transition-delay: 200ms;
}
.animation-element.slide-in-left.delayed-1.in-view {
    -webkit-transition-delay: 400ms; 
    transition-delay: 400ms;
}
.animation-element.slide-in-left.delayed-2.in-view {
    -webkit-transition-delay: 600ms; 
    transition-delay: 600ms;
}
.animation-element.slide-in-right.in-view {
    -moz-transform: translate3d(0px, 0px, 0px);
    -webkit-transform: translate3d(0px, 0px, 0px);
    -o-transform: translate(0px, 0px);
    -ms-transform: translate(0px, 0px);
    transform: translate3d(0px, 0px, 0px);
}
.animation-element.slide-in-right.delayed.in-view {
    -webkit-transition-delay: 200ms; 
    transition-delay: 200ms;
}
.animation-element.slide-in-right.delayed-1.in-view {
    -webkit-transition-delay: 400ms; 
    transition-delay: 400ms;
}
.animation-element.slide-in-right.delayed-2.in-view {
    -webkit-transition-delay: 600ms; 
    transition-delay: 600ms;
}
.animation-element.slide-down.in-view {
    -moz-transform: translate3d(0px, 0px, 0px);
    -webkit-transform: translate3d(0px, 0px, 0px);
    -o-transform: translate(0px, 0px);
    -ms-transform: translate(0px, 0px);
    transform: translate3d(0px, 0px, 0px);
}
.animation-element.slide-down.delayed.in-view {
    -webkit-transition-delay: 200ms; 
    transition-delay: 200ms;
}
.animation-element.slide-down.delayed-1.in-view {
    -webkit-transition-delay: 400ms; 
    transition-delay: 400ms;
}
.animation-element.slide-up.in-view {
    -moz-transform: translate3d(0px, 0px, 0px);
    -webkit-transform: translate3d(0px, 0px, 0px);
    -o-transform: translate(0px, 0px);
    -ms-transform: translate(0px, 0px);
    transform: translate3d(0px, 0px, 0px);
}
.animation-element.slide-up.delayed.in-view {
    -webkit-transition-delay: 200ms; 
    transition-delay: 200ms;
}
.animation-element.slide-up.delayed-1.in-view {
    -webkit-transition-delay: 400ms; 
    transition-delay: 400ms;
}