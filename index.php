<?php


header_remove("X-Powered-By");
header_remove("Server");
ini_set('expose_php', 'off');


# Define Access
define('access', 'index');

// Add debug information
$debug_file = 'logs/index_debug_' . date('Y-m-d') . '.log';
$debug_entry = date('Y-m-d H:i:s') . ' - Index.php called - ' . json_encode(array(
    'cookies' => $_COOKIE,
    'session' => isset($_SESSION) ? $_SESSION : 'Not started',
    'server' => $_SERVER,
    'request_uri' => isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'Not available',
    'http_referer' => isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'Not available',
    'get' => $_GET,
    'post' => $_POST
), JSO<PERSON>_PRETTY_PRINT) . "\n\n";
file_put_contents($debug_file, $debug_entry, FILE_APPEND);

try {

    if (!@include_once('includes/system.php')) {
        throw new Exception('[FAILED] COULD NOT LOAD WEBENGINE.');
    }

    // Add debug information after system.php is loaded
    $debug_entry = date('Y-m-d H:i:s') . ' - After system.php - ' . json_encode(array(
        'cookies' => $_COOKIE,
        'session' => isset($_SESSION) ? $_SESSION : 'Not started',
        'server' => $_SERVER,
        'request_uri' => isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'Not available',
        'http_referer' => isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'Not available',
        'get' => $_GET,
        'post' => $_POST,
        'handler_requestedPath' => isset(handler::$requestedPath) ? handler::$requestedPath : 'Not available'
    ), JSON_PRETTY_PRINT) . "\n\n";
    file_put_contents($debug_file, $debug_entry, FILE_APPEND);

} catch (Exception $ex) {

    die($ex->getMessage());

}