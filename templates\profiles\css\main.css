/* MAIN CSS */

html {
	background: #000000;
}
body {
	background: url('../img/footer_background.png') no-repeat bottom center, url('../img/background4.jpg') no-repeat top center;
	color: #333333;
	margin: 0px;
	padding: 0px;
	min-width: 984px;
	font-family: 'Roboto', sans-serif;
	font-weight: 200;
	font-size: 16px;
}

h1,h2,h3,h4,h5,h6,p {
	margin: 0px;
	padding: 0px;
}

a.alt {
	color: #ffb400;
	text-decoration: underline;
}

a.alt:hover, a.alt:active {
	color: #ffb400;
}

*:focus {
    outline: none;
}

/* GN DESIGN CSS */

.main-container {
	width: 984px;
	margin: 0px auto;
	min-height: 600px;
}

	.main-container .main-header {
		width: 950px;
		height: 10px;
		margin: 0px auto;
	}

	.main-container .main-navbar {
		background: url('../img/navbar_background_x.png') no-repeat top center;
		width: 100%;
		height: 167px;
		position: relative;
		top: -55px;
		z-index: 1000;
	}
	.main-container .main-navbar:hover {
		background: url('../img/navbar_background_x.png') no-repeat bottom center !important;
	}
	
		.main-container .main-navbar .main-navbar-container {
			position: relative;
			top: 73px;
			padding: 0px 20px 0px 20px;
			text-align: center;
		}
	
		.main-container .main-navbar .main-navbar-container a {
			color: #fff;
			text-decoration: none;
			font-family: Arial, Helvetica, sans-serif;
			font-size: 14px;
			text-transform: uppercase;
			text-shadow: 0px 0px 6px #000;
			font-weight: bold;
		}