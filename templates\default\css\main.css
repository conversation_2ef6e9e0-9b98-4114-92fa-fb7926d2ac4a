html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block;
}

body {
  line-height: 1;
}

ol, ul {
  list-style: none;
}

blockquote, q {
  quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
  content: '';
  content: none;
}

/*
table {
  border-collapse: collapse;
  border-spacing: 0;
}
*/

article, aside, details, figcaption, figure, footer, header, hgroup, main, nav, section, summary {
  display: block;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

html, body {
  min-height: 100%;
}

/* Golden Theme Variables */
:root {
  --primary-gold: #d4af37;
  --dark-gold: #b8941f;
  --light-gold: #f4e4a6;
  --gold-gradient: linear-gradient(135deg, #2c1810 0%, #4a2c1a 25%, #6b3e2a 50%, #8b5a3c 75%, #a67c52 100%);
  --dark-bg: #1a1a1a;
  --darker-bg: #0d0d0d;
  --gold-text: #d4af37;
  --light-gold-text: #f4e4a6;
  --dark-gold-text: #b8941f;
  --gold-border: #8b5a3c;
  --gold-hover: #e6c547;
}

body {
  width: 100%;
  background: var(--gold-gradient);
  background-attachment: fixed;
  background-size: cover;
  font-family: 'Open Sans', sans-serif;
  font-size: 16px;
  color: var(--light-gold-text);
  font-weight: 400;
  min-height: 100vh;
}

a {
  transition: all 0.3s ease;
  color: var(--gold-text);
  text-decoration: none;
}

a:hover {
  color: var(--gold-hover);
}

p {
  margin-bottom: 20px;
  color: var(--light-gold-text);
}

h1, h2, h3 {
  color: var(--primary-gold);
  margin-bottom: 20px;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

h1 {
  font-size: 20px;
}

h2 {
  font-size: 18px;
}

h3 {
  font-size: 16px;
}

/*
table {
  width: 100%;
  margin-bottom: 50px;
}
table td {
  border-collapse: collapse;
  padding: 25px 50px;
  border: 1px solid #cfcfcf;
  vertical-align: middle;
}
table thead {
  background: #dddfe4;
  color: #1a242d;
  border-top: 2px solid #a0a3ab;
}
table tbody {
  background: #fff;
}
*/

caption {
  margin-bottom: 15px;
}

select {
  background: var(--darker-bg);
  min-width: 270px;
  border: 1px solid var(--gold-border);
  color: var(--light-gold-text);
  margin: 15px 0px;
  padding: 10px 40px 10px 20px;
  position: relative;
  -webkit-appearance: none;
  background-image: url(../img/arrow-v.png);
  background-position: right 20px center;
  background-repeat: no-repeat;
  -moz-appearance: none;
  text-indent: 0.01px;
  text-overflow: '';
  -ms-appearance: none;
  appearance: none !important;
  cursor: pointer;
  font-size: 14px;
}

select:focus {
  border-color: var(--primary-gold);
  box-shadow: 0 0 5px var(--primary-gold);
}

.timexchange-conditions {
	list-style-type: none;
	padding: 0px;
	margin: 0px;
	font-size: 12px;
	padding: 5px;
	margin: 20px;
}

textarea {
  background: rgba(13, 13, 13, 0.9);
  border: 1px solid var(--gold-border);
  color: var(--light-gold-text);
  padding: 15px 30px;
  position: relative;
  font-size: 14px;
  min-width: 220px;
}

textarea:focus {
  border-color: var(--primary-gold);
  box-shadow: 0 0 5px var(--primary-gold);
  background: rgba(13, 13, 13, 1);
}

button, .button {
  transition: all 0.3s ease;
  cursor: pointer;
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%);
  border: 1px solid var(--gold-border);
  color: var(--darker-bg);
  position: relative;
  z-index: 1;
  text-align: center;
  padding: 16px 40px;
  text-transform: uppercase;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}
button:hover {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--gold-hover) 100%);
  color: var(--darker-bg);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(212, 175, 55, 0.3);
}

.button {
  display: inline-block;
  text-decoration: none;
}

:focus {
  outline: none;
}

::-webkit-input-placeholder {
  color: #e0eaf3;
}

::-moz-placeholder {
  color: #e0eaf3;
}

:-moz-placeholder {
  color: #e0eaf3;
}

:-ms-input-placeholder {
  color: #e0eaf3;
}

.wrapper {
  width: 1200px;
  margin: 0 auto;
  position: relative;
}

.content {
  min-height: 200px;
  padding: 50px 0px;
  line-height: 1.4;
  background-color: rgba(26, 26, 26, 0.9);
  position: relative;
}

.bodyHomePage {
  background: var(--gold-gradient);
  background-attachment: fixed;
  background-size: cover;
  min-height: 100vh;
}

.fullscreen-bg {
  overflow: hidden;
  z-index: -100;
}

.fullscreen-bg_video {
  object-fit: cover;
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
}

.contentHomePage {
  padding: 50px 0px 10px 0px;
  background-color: rgba(26, 26, 26, 0.9);
}

.flex {
  display: flex;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-c {
  display: flex;
  align-items: center;
}

.flex-s {
  display: flex;
  justify-content: space-between;
}

.flex-s-c {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-c-c {
  display: flex;
  justify-content: center;
  align-items: center;
}

.bright:hover {
  filter: brightness(120%);
}

/* Header
-----------------------------------------------------------------------------*/
.header {
  height: 735px;
  position: relative;
}

.headerHomePage {
  height: 1182px;
}

.topPanel {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  transition: 0.3s;
  z-index: 99;
}
.topPanel.topPanel-top {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%);
  border-bottom: 2px solid var(--gold-border);
}
.topPanel-wrapper {
  width: 1200px;
  margin: 0 auto;
  padding: 0px 20px;
  position: relative;
}
.topPanel-wrapper:after {
  content: "";
  position: absolute;
  left: 0;
  top: 105px;
  height: 1px;
  width: 100%;
  background-color: var(--gold-border);
  transition: 0.3s;
  opacity: 0;
}
.topPanel:hover {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.98) 0%, rgba(44, 24, 16, 0.98) 100%);
}
.topPanel:hover .topPanel-wrapper:after {
  opacity: 1;
}

.logo {
  margin-top: 32px;
}

.logo-fixed {
  position: relative;
  left: 0;
  top: 225px;
  margin-left: 350px;
}

.logo-fixed img {
  filter: brightness(150%);
}

.menu ul {
  display: flex;
}
.menu ul li {
  padding: 45px 40px;
  transition: 0.3s;
}
.menu ul li a {
  color: var(--light-gold-text);
  display: block;
  min-width: 100px;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}
.menu ul li ul {
  display: none;
}
.menu ul li:hover {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2) 0%, rgba(184, 148, 31, 0.3) 100%);
  border-radius: 5px;
}
.menu ul li:hover a {
  color: var(--primary-gold) !important;
}
.menu ul li:hover ul {
  display: block;
  margin-top: 60px;
}
.menu ul li:hover ul li {
  padding: 15px 0px;
}
.menu ul li:hover ul li:hover {
  background: none;
}
.menu ul li:hover ul li a {
  font-weight: 400;
  color: #fff;
}
.menu ul li:hover ul li a:hover {
  color: #ffdb75;
}

.acc-panel {
  background: url(../img/acc-icon.png) left no-repeat;
  height: 55px;
  padding-left: 80px;
  margin-top: 25px;
}
.acc-panel a {
  color: #f2db9e;
  font-size: 12px;
}
.acc-panel a:hover {
  color: #f2db9e !important;
}
.acc-panel span {
  margin-bottom: 0px;
  font-size: 18px;
  font-weight: bold;
  margin-top: 3px;
  color: #fff;
  display: block;
}

.topPanel.topPanel-top .menu ul li {
  padding: 25px 40px;
}
.topPanel.topPanel-top .menu ul li ul li {
  padding: 15px 0px;
}
.topPanel.topPanel-top .menu ul li:hover ul {
  margin-top: 40px;
}
.topPanel.topPanel-top .logo {
  margin-top: 15px;
}
.topPanel.topPanel-top .acc-panel {
  margin-top: 5px;
}
.topPanel.topPanel-top .topPanel-wrapper:after {
  top: 65px;
}

.headerButtons {
  position: absolute;
  left: 0;
  bottom: 425px;
}

.downloadButton {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: url(../img/download-button.jpg) no-repeat;
  width: 321px;
  height: 132px;
  color: #fbdb89;
  font-size: 12px;
  letter-spacing: 1px;
  transition: 0.3s;
}
.downloadButton:hover {
  filter: brightness(120%);
  box-shadow: 0px 0px 14px 0px rgba(77, 224, 248, 0.8);
}
.downloadButton span {
  font-size: 26px;
  color: #fff;
  font-weight: 700;
  margin-bottom: 3px;
}

.registerButton {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: url(../img/register-button.png) no-repeat;
  width: 321px;
  height: 132px;
  color: #fbdb89;
  font-size: 12px;
  letter-spacing: 1px;
  transition: 0.3s;
}
.registerButton:hover {
  filter: brightness(120%);
  box-shadow: 0px 0px 14px 0px rgba(77, 224, 248, 0.8);
}
.registerButton span {
  font-size: 26px;
  color: #fff;
  font-weight: 700;
  margin-bottom: 3px;
}

.headerButtons-other {
  background: url(../img/header-buttons-other.png) no-repeat;
  width: 272px;
  height: 132px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.headerButtons-other a {
  display: flex;
  flex-direction: column;
  width: 130px;
  height: 110px;
  text-align: center;
  color: #c1d8ea !important;
  font-size: 12px;
  padding-top: 10px;
}
.headerButtons-other a span {
  font-size: 14px;
  font-weight: 600;
  margin-top: 3px;
}
.headerButtons-other a:hover {
  color: #fff !important;
}
.headerButtons-other a:hover .b-icons {
  filter: drop-shadow(0px 0px 10px #57c5d5);
}
.headerButtons-other .b-icons {
  width: 50px;
  height: 50px;
  margin: 0 auto;
  margin-bottom: 10px;
  transition: 0.3s;
}
.headerButtons-other .pack {
  background: url(../img/pack-icon.png) center no-repeat;
}
.headerButtons-other .fb {
  background: url(../img/fb-icon.png) center no-repeat;
}
.headerButtons-other .wp {
  background: url(../img/wp-icon.png) center no-repeat;
}
.headerButtons-other .discord {
  background: url(../img/dis-icon.png) center no-repeat;
}
.headerButtons-other .ig {
  background: url(../img/ig-icon.png) center no-repeat;
}
.headerButtons-other .forum {
  background: url(../img/forum-icon.png) center no-repeat;
}
.headerButtons-other .mobile {
  background: url(../img/mobile-icon.png) center no-repeat;
}

.newsBlock {
  position: absolute;
  left: 0;
  bottom: 0;
}

.swiper-news {
  width: 774px;
  height: 394px;
}

.newsPagination {
  position: absolute;
  background: #0a1218;
  left: 30px;
  bottom: 30px;
  width: 120px;
  height: 35px;
  z-index: 9;
}

.swiper-news .swiper-button-next, .swiper-news .swiper-button-prev {
  width: 7px;
  height: 13px;
  background-size: 7px 13px;
  margin-top: -7px;
}

.swiper-news .swiper-pagination {
  color: #d0dce0;
  font-size: 15px;
}

.swiper-news .swiper-pagination-current {
  color: #ffc66d;
}

.news-block {
  background: url(../img/news-bg.jpg) no-repeat;
  width: 429px;
  height: 394px;
  border-right: 1px solid rgba(63, 90, 123, 0.5);
  border-top: 1px solid rgba(63, 90, 123, 0.5);
  padding: 30px;
  position: relative;
}

.news-block-tab-buttons {
  position: relative;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #364059;
}
.news-block-tab-buttons span {
  display: block;
  height: 44px;
  padding: 5px 10px;
  color: #828990;
  font-size: 15px;
  cursor: pointer;
  position: relative;
  transition: 0.3s;
}
.news-block-tab-buttons span:hover {
  color: #ffe49b;
}
.news-block-tab-buttons span.active {
  color: #ffe49b;
  font-weight: 700;
  border-bottom: 1px solid #ffe49b;
}
.news-block-tab-buttons span.active:after {
  content: "";
  background: #ffe49b;
  height: 1px;
  width: 100%;
  position: absolute;
  left: 0;
  bottom: -1px;
}

.news-message {
  height: 77px;
  text-align: center;
  flex-direction: column;
  justify-content: center;
  border-bottom: 1px solid rgba(54, 64, 89, 0.4);
}
.news-message h3 {
  color: #fff;
  font-size: 16px;
  margin-bottom: 8px;
}
.news-message span {
  color: #828990;
  font-size: 12px;
}

.news-block-tab {
  padding: 25px 15px;
  border-bottom: 1px solid rgba(54, 64, 89, 0.4);
  height: 180px;
  overflow: hidden;
  display: none;
  animation: tab 0.4s linear;
}
.news-block-tab.active {
  display: block;
}

@keyframes tab {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.news {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #eee4cb;
  margin-bottom: 17px;
}
.news:last-child {
  margin-bottom: 0px;
}
.news a {
  color: #eee4cb;
  display: block;
  width: 260px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  padding: 3px 0px;
}
.news a:hover {
  text-decoration: underline;
}
.news .date {
  color: #828990;
  font-size: 12px;
}

.color-red {
  color: #fe3d29;
}

.color-yellow {
  color: #c7ae71;
}

.load-more {
  text-align: center;
  padding-top: 20px;
}
.load-more a {
  color: #8f97a6;
  font-size: 14px;
  position: relative;
}
.load-more a:hover {
  color: #fff;
}
.load-more a:before {
  content: "+";
  margin-right: 5px;
}

.gemFamilyBlock {
  width: 770px;
}

.eventsBlock {
  width: 428px;
}

.fr-title {
  color: #53453d;
  font-size: 18px;
  font-weight: bold;
  position: relative;
  margin-bottom: 30px;
}
.fr-title span {
  background-color: #f8f7f5;
  position: relative;
  z-index: 1;
  display: inline-block;
  padding: 5px 25px 5px 55px;
  background-position: left 20px center;
  background-repeat: no-repeat;
}
.fr-title:after {
  content: "";
  height: 1px;
  width: 98%;
  position: absolute;
  top: 50%;
  margin-top: 1px;
  left: 0;
  background: #dfdad5;
}

.fr-title-gem span {
  background-image: url(../img/gem-title-icon.png);
}

.fr-title-events span {
  background-image: url(../img/event-title-icon.png);
}

.gemBlock {
  width: 386px;
  height: 172px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 30px;
  font-weight: bold;
}
.gemBlock.vanertBlock {
  background: url(../img/vanert-bg.jpg) no-repeat;
}
.gemBlock.duprianBlock {
  background: url(../img/duprian-bg.jpg) no-repeat;
}
.gemBlock p {
  margin-bottom: -5px;
  color: #fdedc7;
  font-size: 12px;
  text-transform: uppercase;
}
.gemBlock img {
  margin-right: 50px;
}

.event {
  display: block;
  width: 50%;
  border: 1px solid #d6beb4;
  height: 86px;
  display: flex;
  align-items: center;
  background: #ffffff;
  padding-left: 15px;
  line-height: 1.1;
  color: #61564e;
  font-size: 14px;
  font-weight: 600;
}
.event:nth-child(1) {
  border-right: none;
  border-bottom: none;
}
.event:nth-child(2) {
  border-bottom: none;
}
.event:nth-child(3) {
  border-right: none;
}
.event-icon {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-right: 15px;
}
.event-icon:after {
  content: "";
  background: url(../img/icon-bg.png) no-repeat;
  width: 56px;
  height: 56px;
  position: absolute;
  left: 0;
  top: 0;
}
.event:hover {
  background: #3f332b;
  color: #ccbfb4;
}
.event:hover .event-icon img {
  filter: brightness(150%);
}
.event:hover .event-icon:after {
  opacity: 0.2;
}

.infoBlockHome {
  height: 1188px;
}

.slider {
  width: 1200px;
  height: 730px;
  position: relative;
}
.slider:before {
  content: "CLASS";
  font-size: 170px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.1);
  position: absolute;
  left: -90px;
  top: 140px;
  letter-spacing: 50px;
}
.slider .swiper-slide {
  background-position: top right;
  background-repeat: no-repeat;
}

.gallery-top {
  height: 730px;
  width: 100%;
}

.gallery-thumbs {
  height: 167px;
  width: 996px;
  margin: 0 auto;
  margin-top: -167px;
}
.gallery-thumbs span {
  width: 96px;
  height: 96px;
  border-radius: 50%;
  background-color: #c9cacc;
  border: 1px solid #def0fd;
  position: relative;
  z-index: 2;
}
.gallery-thumbs span img {
  width: 96px;
  height: 96px;
  filter: grayscale(100%) brightness(120%);
}
.gallery-thumbs .swiper-slide {
  width: 166px;
  height: 163px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: 0.4s;
  position: relative;
}
.gallery-thumbs .swiper-slide:after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 166px;
  height: 163px;
  transition: 0.3s;
  opacity: 0;
}
.gallery-thumbs .swiper-slide.sm:after {
  background: url(../img/slider-nav.png) 0px top no-repeat;
}
.gallery-thumbs .swiper-slide.gl:after {
  background: url(../img/slider-nav.png) -166px top no-repeat;
}
.gallery-thumbs .swiper-slide.elf:after {
  background: url(../img/slider-nav.png) -332px top no-repeat;
}
.gallery-thumbs .swiper-slide.mg:after {
  background: url(../img/slider-nav.png) -498px top no-repeat;
}
.gallery-thumbs .swiper-slide.dm:after {
  background: url(../img/slider-nav.png) -664px top no-repeat;
}
.gallery-thumbs .swiper-slide.rf:after {
  background: url(../img/slider-nav.png) -830px top no-repeat;
}
.gallery-thumbs .swiper-slide.dl:after {
  background: url(../img/slider-nav.png) -996px top no-repeat;
}
.gallery-thumbs .swiper-slide.bk:after {
  background: url(../img/slider-nav.png) -1162px top no-repeat;
}
.gallery-thumbs .swiper-slide:hover span {
  border: 1px solid rgba(255, 255, 255, 0);
}
.gallery-thumbs .swiper-slide:hover span img {
  filter: none;
}
.gallery-thumbs .swiper-slide:hover:after {
  opacity: 1;
}
.gallery-thumbs .swiper-slide.swiper-slide-thumb-active img {
  filter: none;
}
.gallery-thumbs .swiper-slide.swiper-slide-thumb-active:after {
  opacity: 1;
}

.gallery-thumbs .swiper-slide-thumb-active {
  opacity: 1;
}

.slider-arrow > div {
  top: auto;
  bottom: 55px;
  width: 126px;
  height: 61px;
}
.slider-arrow .swiper-button-prev {
  background-image: url(../img/slider-arrow-left.png);
  background-size: 126px 61px;
}
.slider-arrow .swiper-button-next {
  background-image: url(../img/slider-arrow-right.png);
  background-size: 126px 61px;
}

.classNameBlock {
  position: relative;
  margin-left: -20px;
  margin-bottom: 20px;
  margin-top: 157px;
  opacity: 0;
}

.class-img {
  width: 150px;
  height: 150px;
}
.class-img.magic {
  background: url(../img/magic-icon.png) no-repeat;
}
.class-img.archer {
  background: url(../img/archer-icon.png) no-repeat;
}
.class-img.warrior {
  background: url(../img/warrior-icon.png) no-repeat;
}

.className h2 {
  color: #fff;
  font-size: 60px;
  margin-bottom: 5px;
  text-transform: uppercase;
  line-height: 1;
}
.className span {
  font-size: 18px;
  color: #cbd3d6;
  font-weight: 600;
  text-transform: uppercase;
}

.skillBlock {
  color: #ecf6fa;
  font-size: 18px;
  padding-left: 130px;
  margin-bottom: 35px;
  opacity: 0;
}

.stars {
  background: url(../img/stars.png) no-repeat;
  width: 139px;
  height: 19px;
  display: block;
  position: relative;
  margin-left: 10px;
}
.stars span {
  background: url(../img/stars-active.png) no-repeat;
  height: 19px;
  display: block;
}

.sliderVideo {
  opacity: 0;
}

.sliderVideo a {
  margin-left: 130px;
  width: 165px;
  height: 90px;
  background: #000;
  position: relative;
  display: block;
}
.sliderVideo a:after {
  content: "";
  background: url(../img/video-icon.png) center no-repeat;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.modal_div {
  width: 800px;
  min-height: 300px;
  background: url(../img/modal-img.png) center top no-repeat;
  background-color: #251e1a;
  position: fixed;
  top: 15%;
  left: 50%;
  margin-top: -150px;
  margin-left: -400px;
  display: none;
  opacity: 0;
  z-index: 999;
  padding: 30px 20px;
  border: 1px solid #352925;
  box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.4);
}

.modal_div .modal_close {
  width: 40px;
  height: 40px;
  background: #fff;
  position: absolute;
  color: #000;
  top: 0px;
  right: -40px;
  cursor: pointer;
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Arial;
  font-size: 20px;
}

#overlay {
  z-index: 998;
  position: fixed;
  background-color: #000;
  opacity: 0.8;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  cursor: pointer;
  display: none;
}

.modal-title {
  text-align: center;
  border-bottom: 1px solid #392e29;
  margin: 0px -20px;
}
.modal-title h2 {
  font-weight: 600;
  color: #fff;
  font-size: 30px;
  text-transform: uppercase;
  margin-bottom: 0px;
  padding-bottom: 30px;
  line-height: 1;
}

.modal-content {
  padding-top: 30px;
}

.modal_video .modal-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal_video iframe {
  width: 100%;
}

.swiper-slide-active .classNameBlock {
  animation: fade 1s linear;
  animation-delay: 0.5s;
  animation-fill-mode: forwards;
}
.swiper-slide-active .skillBlock {
  animation: fade 1s linear;
  animation-delay: 1s;
  animation-fill-mode: forwards;
}
.swiper-slide-active .sliderVideo {
  animation: fade 1s linear;
  animation-delay: 1.5s;
  animation-fill-mode: forwards;
}

@keyframes fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.gameCenterBlock {
  padding-top: 110px;
}
.gameCenterBlock h1 {
  text-align: center;
  margin-bottom: 50px;
  font-size: 36px;
}
.gameCenterBlock h1 span {
  font-weight: 400;
}

.gameBlock {
  width: 215px;
  height: 149px;
  border-top: 1px solid #d1c0b7;
  border-bottom: 1px solid #d1c0b7;
  border-left: 1px solid #d1c0b7;
  background: linear-gradient(to right, #fefefd, #f9f6f1);
  padding: 34px 0px 0px 60px;
  position: relative;
  overflow: hidden;
  transition: 0.3s;
}
.gameBlock:last-child {
  border-right: 1px solid #d1c0b7;
}
.gameBlock p {
  font-weight: 600;
  margin-bottom: 10px;
  font-size: 18px;
  text-transform: uppercase;
}
.gameBlock .game-title_1 {
  color: #6b5a42;
}
.gameBlock .game-title_2 {
  color: #a39178;
}
.gameBlock a {
  color: #fff;
  text-transform: uppercase;
  padding: 6px 9px;
  background: #a39f99;
  position: relative;
  font-size: 10px;
  display: inline-block;
}
.gameBlock a:after {
  content: ">";
  margin-left: 5px;
}
.gameBlock:after {
  content: "";
  height: 1px;
  width: 100px;
  background: #eae6da;
  position: absolute;
  left: -20px;
  bottom: 20px;
  transform: rotate(-133deg);
}
.gameBlock:before {
  content: "";
  height: 1px;
  width: 100px;
  background: #ddd6c7;
  position: absolute;
  left: -40px;
  bottom: 20px;
  transform: rotate(-133deg);
}
.gameBlock:hover {
  width: 340px;
  box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.1);
}
.gameBlock:hover:after {
  display: none;
}
.gameBlock:hover:before {
  display: none;
}
.gameBlock:hover .game-title_1 {
  color: #fff;
}
.gameBlock:hover .game-title_2 {
  color: #fdf9cc;
}
.gameBlock:hover a {
  background: #867458;
}
.gameBlock.strategy:hover {
  background: url(../img/game-center-strategy.png) right no-repeat;
  background-color: #bea57d;
}
.gameBlock.system:hover {
  background: url(../img/game-center-system.png) right no-repeat;
  background-color: #bea57d;
}
.gameBlock.events:hover {
  background: url(../img/game-center-events.png) right no-repeat;
  background-color: #bea57d;
}
.gameBlock.pets:hover {
  background: url(../img/game-center-pets.png) right no-repeat;
  background-color: #bea57d;
}
.gameBlock.guides:hover {
  background: url(../img/game-center-guides.png) right no-repeat;
  background-color: #bea57d;
}

/* Footer
-----------------------------------------------------------------------------*/
.footer {
  height: 301px;
  text-align: center;
  padding-top: 60px;
}

.f-logo {
  margin-bottom: 40px;
}
.f-logo img {
  filter: brightness(150%);
}

.copyright {
  color: #e8eff8;
  font-size: 14px;
  margin-bottom: 10px;
}

.copy-text {
  color: #849095;
}

.pageTitle {
  display: flex;
  align-items: center;
  color: #ffffff;
  font-size: 24px;
  margin-bottom: 0px;
  height: 110px;
  width: 100%;
  padding-left: 40px;
  position: absolute;
  left: 0;
  top: -112px;
  border-top: 1px solid #536e9b;
  border-left: 1px solid #536e9b;
  border-right: 1px solid #536e9b;
}
.pageTitle:after {
  content: "";
  position: absolute;
  background: linear-gradient(to right, #351c3c 30%, rgba(52, 75, 114, 0.7));
  left: 0;
  top: 0;
  width: 100%;
  height: 110px;
}
.pageTitle h1 {
  color: #fff;
  position: relative;
  z-index: 2;
  margin-bottom: 0px;
  font-weight: bold;
}
.pageTitle select {
  position: absolute;
  right: 45px;
  top: 25px;
  z-index: 10;
}

.blockTitle {
  margin-bottom: 50px;
  background: url(../img/title-icon.png) left no-repeat;
  padding-left: 25px;
  margin-left: 15px;
}
.blockTitle h2 {
  margin-bottom: 0px;
  font-size: 24px;
  font-weight: 400;
}
.blockTitle h2 span {
  font-weight: 600;
  color: #1a242d;
  font-size: 30px;
}

.downloadBlock {
  text-align: center;
}
.downloadBlock > div {
  height: 270px;
  padding: 0px 60px;
}
.downloadBlock-left {
  width: 700px;
  background: #e0e3ed;
}
.downloadBlock-right {
  width: 500px;
  background: #4b577e;
  color: #d5e7e8;
}
.downloadBlock-right h3 {
  color: #fff;
  margin-bottom: 15px;
  font-size: 24px;
}

.from-block {
  padding: 0px 60px;
}

.file-size {
  font-size: 24px;
  color: #27353d;
  margin-bottom: 30px;
}

.filesBlock {
  display: flex;
  border: 1px solid #c8ccd6;
}
.filesBlock a {
  display: block;
  width: 120px;
  height: 90px;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  border-right: 1px solid #c8ccd6;
}
.filesBlock a:last-child {
  border-right: none;
}
.filesBlock a:hover {
  background-color: #eef2ff;
}
.filesBlock .mega {
  background-image: url(../img/mega-icon.png);
}
.filesBlock .googledrive {
  background-image: url(../img/googledrive-icon.png);
}
.filesBlock .torrent {
  background-image: url(../img/torrent-icon.png);
}
.filesBlock .mediafire {
  background-image: url(../img/mediafire-icon.png);
}

.download-button {
  color: #dbe6fe;
  display: inline-block;
  padding: 14px 35px;
  border: 1px solid #a5b3db;
}
.download-button:hover {
  background: #a5b3db;
  color: #4b577e;
}

.block {
  margin-bottom: 50px;
}

.drivers {
  justify-content: space-between;
}
.drivers .driver {
  width: 380px;
  height: 185px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #bdbec1;
}

.rank-title {
  padding: 0px 0px 0px 50px;
  border-bottom: 1px solid #d1ccc9;
  margin-bottom: 40px;
}
.rank-title h2 {
  color: #1a242d;
  font-size: 30px;
  font-weight: 400;
  margin-bottom: 35px;
}

.topRanksBlock {
  margin-bottom: 30px;
}
.topRanksBlock > div {
  width: 582px;
  height: 125px;
  display: flex;
}

.topRank_1 {
  border: 1px solid #d6cb93;
  background: #f2ebc7;
}
.topRank_1 .topRank-number {
  background: #eadb8a;
  border-right: 1px solid #d6cb93;
}

.topRank_2 {
  border: 1px solid #bab9b1;
  background: #eaeae8;
}
.topRank_2 .topRank-number {
  background: #bab9b1;
  border-right: 1px solid #bab9b1;
}

.topRank-number {
  width: 123px;
}
.topRank-number span {
  width: 60px;
  height: 60px;
  background: #fff;
  border-radius: 50%;
  color: #1a242d;
  font-size: 18px;
}

.topRank-icon {
  width: 134px;
  border-right: 1px solid #eadb8a;
}
.topRank-icon img {
  width: 70px;
}
.topRank-icon .topRank-icon-sm {
  background-image: url(../img/slider-nav.png);
}

.topRank-info {
  width: 325px;
  padding-left: 40px;
}
.topRank-info_name img {
  height: 20px;
  margin-right: 15px;
}
.topRank-info_name div {
  color: #3e3e3c;
  font-size: 24px;
  font-weight: 600;
  margin-right: 15px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 160px;
}
.topRank-info_resets {
  color: #3e3e3c;
  font-size: 14px;
}

.p-online {
  color: #63c342;
  font-size: 14px;
}

.p-offline {
  color: #ff2d03;
  font-size: 14px;
}

.table-rank thead {
  border: 1px solid #d8d7d0;
  background: #e7e6df;
  color: #717171;
  font-size: 14px;
}
.table-rank td {
  border: none;
  border-bottom: 1px solid #d8d7d0;
}
.table-rank tbody td {
  color: #3b3f43;
  font-size: 18px;
  padding: 20px 50px;
}
.table-rank tbody td:nth-child(6n+1) {
  font-weight: 600;
}
.table-rank tbody td:nth-child(6n+2) img {
  width: 50px;
}
.table-rank tbody td:nth-child(6n+4) img {
  height: 16px;
  margin-right: 5px;
  margin-bottom: -2px;
}
.table-rank tbody td:nth-child(6n+5) {
  font-weight: 600;
}

.pagination {
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.pagination li .number {
  display: block;
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  margin: 0px 7px;
  font-size: 14px;
  color: #404243;
  border-radius: 50%;
  position: relative;
}
.pagination li .active {
  color: #f1f8ff;
  background: #5f5d55;
}
.pagination li .prev {
  width: 40px;
  height: 40px;
  line-height: 40px;
  background: #918f84;
}
.pagination li .prev:before {
  content: "<";
  font-stretch: ultra-condensed;
  color: #f1f8ff;
  font-size: 18px;
}
.pagination li .next {
  width: 40px;
  height: 40px;
  line-height: 40px;
  background: #918f84;
}
.pagination li .next:before {
  content: ">";
  font-stretch: ultra-condensed;
  color: #f1f8ff;
  font-size: 18px;
}

/*# sourceMappingURL=style.css.map */
.rankings-selection {
  margin-bottom: 15px;
	text-align: center;
}
.rankings-selection a {
	text-decoration: none;
	color: #666666;
	font-weight: bold;
	padding: 0px 10px;
}

.rankings-selection a:hover, .rankings-selection a:active {
	text-decoration: none;
	color: #ff0000;
}
.rankings-selection a.active {
	font-weight: bold;
	color: #ff0000;
}
/* USER CP: MY ACCOUNT */
.account-safety {
	width: 500px;
	margin: 30px auto;
	font-size: 12px;
}
.account-safety ul {
		margin-top: 10px;
}
.account-safety ul li {
		margin-bottom: 10px;
}