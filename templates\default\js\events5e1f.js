function tConvert(time) {
	time = time.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/) || [time];

	if (time.length > 1) {
		time = time.slice(1);
		time[5] = +time[0] < 12 ? ' AM' : ' PM';
		time[0] = +time[0] % 12 || 12;
		if(time[0] < 10) time[0] = "0" + time[0];
	}

	return time.join('');
}

var MuEvents = {};

MuEvents.sked = [
	['Blood Castle', 0, '00:00','02:00','04:00','06:00','08:00','10:00','12:00','14:00','16:00','18:00','20:00','22:00'],
	['Devil Square', 0, '00:30','02:30','04:30','06:30','08:30','10:30','12:30','14:30','16:30','18:30','20:30','22:30'],
	['Chaos Castle', 0, '01:00','03:00','05:00','07:00','09:00','11:00','13:00','15:00','17:00','19:00','21:00','23:00'],
	['Red Dragon Invasion', 1, '01:15','03:15','05:15','07:15','09:15','11:15','13:15','15:15','17:15','19:15','21:15','23:15'],
	['Golden Invasion', 1, '00:15','02:15','04:15','06:15','08:15','10:15','12:15','14:15','16:15','18:15','20:15','22:15'],
	['White Wizard', 1, '00:10','03:10','06:10','09:10','12:10','15:10','18:10','21:10'],
	['Skeleton King', 1, '00:15','04:15','08:15','12:15','16:15','20:15'],
/*Castle Siege*/
	['Crywolf', 0, '21:00'],
	['Illusion Temple', 0, '00:45','04:45','08:45','12:45','16:45','20:45'],
	['Medusa', 1, '01:45','04:45','07:45','10:45','13:45','16:45','19:45','22:45'],
	['Doppelganger', 0, '00:30','02:30','04:30','06:30','08:30','10:30','12:30','14:30','16:30','18:30','20:30','22:30'],
	['Acheron Guardian', 0, '23:00'],
	['Arca Battle', 0, '22:00'],
	['Last Man Standing', 0, '06:00','12:00','18:00'],
	['Scramble Words', 0, '10:00','12:00','15:00','18:00','23:00']
];

MuEvents.init = function (e) {
	if (typeof e == "string") {
		var g = new Date(new Date().toString().replace(/\d+:\d+:\d+/g, e));
	}

	var f = (typeof e == "number" ? e : (g.getHours() * 60 + g.getMinutes()) * 60 + g.getSeconds()), q = MuEvents.sked, j = [];

	for (var a = 0; a < q.length; a++) {
		var n = q[a];

		for (var k = 2; k < q[a].length; k++) {
			var b = 0,
			p = q[a][k].split(":"),
			o = (p[0] * 60 + p[1] * 1) * 60,
			c = q[a][2].split(":");

			if (q[a].length - 1 == k && (o - f) < 0)
				b = 1;

			var r = b ? (1440 * 60 - f) + ((c[0] * 60 + c[1] * 1) * 60) : o - f;

			if (f <= o || b) {
				var l = Math.floor((r / 60) / 60),
				l = l,
				d = Math.floor((r / 60) % 60),
				d = d,
				u = r % 60,
				u = u;
				j.push('<li class="discussions-content-block"><div style="width: 100%;"><span>' + n[0] + '</span><span style="float:right; display:block;">' + tConvert(q[a][b ? 2 : k]) + '</span><br /><span style="color: #8b6757;">Starts In</span><span style="float:right; display:block; color:#0b8a88;">' + (l + " hrs " + d + " min " + u + " sec") + '</span></div></li>');
				break;
			};
		};
	};

	document.getElementById("eventtimers").innerHTML = j.join("");

	setTimeout(function () {
		MuEvents.init(f == 86400 ? 1 : ++f);
	}, 1000);
};