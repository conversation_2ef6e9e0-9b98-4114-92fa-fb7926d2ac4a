<?php
/**
 * AionCMS
 * https://aioncms.com
 * 
 * <AUTHOR> <https://lautaroangelico.com/>
 * @copyright (c) 2012-2019 Lautaro Angelico, All Rights Reserved
 */

if(!defined('access') or !access) die();

if(isLoggedIn()) {
	# usercp 
	echo '<div class="usercp-block">';
		echo '<div class="usercp-container">';
			$usercpSidebarMenu = config('usercp_sidebar_menu', true);
			if(is_array($usercpSidebarMenu)) {
				foreach($usercpSidebarMenu as $usercpSidebarElement) {
					echo '<div class="item">';
						echo '<div class="itemicon"><img src="'.template_img(true).'usercp_icons/'.$usercpSidebarElement[2].'" /></div>';
						echo '<div class="itemlink"><a href="'.module_url($usercpSidebarElement[1], true).'">'.$usercpSidebarElement[0].'</a></div>';
					echo '</div>';
					echo '<div class="separator"></div>';
				}
			}
		echo '</div>';
	echo '</div>';
	
	echo '<div class="usercp-loggedin-block">';
		echo 'Welcome back <strong>'.$_SESSION['username'].'</strong>!';
		echo '<br />';
		if($_SESSION['is_staff'] == true) echo '<a href="'.__BASE_URL__.'aioncp/" target="_blank" style="background:#000;color:#ff0000;font-weight:bold;">aioncp</a> ';
		echo '<a href="'.module_url('usercp/', true).'">usercp</a> ';
		echo '<a href="'.module_url('logout/', true).'">logout</a>';
	echo '</div>';
} else {
	/*
	# login box
	echo '<div class="login-box">';
		echo '<form action="'.module_url('', true).'login/" method="post">';
		echo '<input type="text" class="login-username" maxlength="25" name="login_username" autofocus/>';
		echo '<input type="password" class="login-password" name="login_password"/><br />';
		echo '<button type="submit" class="login-submit" name="login_submit" value="ok"></button>';
	echo '</form>';
	echo '</div>';
	*/
}

echo '<div class="sidebar-block">';
	echo '<a href="'.config('launcher_link').'" class="sidebar-download"></a>';
echo '</div>';

/*
echo '<div class="sidebar-block">';
	echo '<a href="'.config('forum_url').'" target="_blank" class="sidebar-enchant"></a>';
echo '</div>';
*/

echo '<div class="sidebar-block rankings">';
	echo '<h5>TOP RANKING</h5>';
    try {
        $rankingData = loadCacheFile('rankings.abyss.cache');
        if(!$rankingData) throw new Exception();
        $result = rankingCacheToArray($rankingData);
        echo '<table class="sidebar-ranking-table">';
        echo '<tr>';
        echo '<th>'.lang('home_ranking_title_name').'</th>';
        echo '<th>'.lang('home_ranking_title_abyss').'</th>';
        echo '<th>'.lang('ranking_table_race').'</th>';
        echo '</tr>';

        $i = 1;
        foreach($result as $row) {
            if($i >= 10) continue;
            echo '<tr>';
            echo '<td>'.$row[1].'</td>';
            echo '<td>'.number_format($row[6]).'</td>';
            echo '<td>'.numRaceImg($row[3]).'</td>';
            echo '</tr>';

            $i++;
        }
        echo '</table>';
    } catch(Exception $exception) {

    }
echo '</div>';