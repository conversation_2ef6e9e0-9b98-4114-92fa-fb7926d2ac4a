.shop-container {
    width: 100%;
    margin: 0 auto;
    min-height: 600px;
}

.shop-container .shop-header {
    background: #000 url(../img/webshop/honeycomb_mesh.jpg) repeat;
    width: 100%;
    border: 1px solid #333;
    color: #fff;
    padding-bottom: 20px;
    font-size: 14px;
}

.shop-container .shop-header a {
    color: #ff9600;
}

.shop-container .shop-header a:hover,
.shop-container .shop-header a:active {
    color: #fc0;
}

.shop-container .shop-header .shop-title {
    font-size: 30px;
    text-transform: uppercase;
    font-weight: bold;
    padding: 20px 0;
}

    .shop-container .shop-header .shop-title a {
        color: #ccc;
    }

    .shop-container .shop-header .shop-title a:hover,
    .shop-container .shop-header .shop-title a:active {
        color: #fff;
        text-decoration: none;
    }

.shop-container .shop-content {
    width: 100%;
    background: #fff;
    border-left: 1px solid #333;
    border-right: 1px solid #333;
    border-bottom: 1px solid #333
}

.shop-container>.shop-content>.row {
    margin: 0;
    display: table;
    width: 100%
}

.shop-container>.shop-content>.row>.sidebar {
    background: #000;
    padding: 0;
    height: 100%;
    display: table-cell;
    float: none;
}

.shop-container>.shop-content>.row>.items-content {
    display: table-cell;
    float: none;
    padding-top: 15px;
    overflow: hidden;
}

.nav-sidebar>li {
    border-bottom: 1px dotted #121212;
}

.nav-sidebar>li>a {
    padding-right: 15px;
    padding-left: 15px;
    font-size: 12px;
    color: #fff;
    cursor: pointer;
}

.nav-sidebar>li>a:hover,
.nav-sidebar>li>a:focus {
    background-color: #111;
    color: #fc0;
}

.nav-sidebar>li>ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

.nav-sidebar>li>ul>.sub-category>a {
    color: #aaa;
    padding-left: 30px;
    font-size: 11px;
    background-color: #000;
}

.nav-sidebar>li>ul>.sub-category>a:hover,
.nav-sidebar>li>ul>.sub-category>a:focus {
    color: #fc0;
}

.shop-item-text {
    font-weight: 700;
    color: #000;
}

.shop-quality-0 { color: #000000 !important; }
.shop-quality-1 { color: #000000 !important; }
.shop-quality-2 { color: #69e15e !important; }
.shop-quality-3 { color: #4ccfff !important; }
.shop-quality-4 { color: #f0b71c !important; }
.shop-quality-5 { color: #f08033 !important; }
.shop-quality-6 { color: #8f39ce !important; }
.shop-quality-7 { color: #d9a839 !important; }
.shop-quality-8 { color: #d944ec !important; }
.shop-quality-9 { color: #fe4b4b !important; }

.shop-items-table tr td {
    padding: 20px 0!important;
}

.shop-item-detail {
    background: #0d161c;
    overflow: auto;
    padding: 20px;
    border: 5px solid #1b252e;
    margin-bottom: 2px;
    color: #fff;
}

.shop-purchase-box {
    background: #f1f1f1;
    padding: 20px;
    border-top: 4px solid red;
}

/* Styles modernes pour le shop */

/* Style pour les cartes d'items */
.card-colored {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
}

/* Assurer que toutes les cartes ont la même hauteur dans une rangée */
.grid-margin {
    margin-bottom: 20px;
    height: 100%;
    display: flex;
}

.grid-margin > .card-colored {
    width: 100%;
}

.card-colored:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* En-tête de carte */
.card-header-shop {
    background: linear-gradient(135deg, #2c3e50, #1a2530);
    color: white;
    border-bottom: none;
    padding: 12px 15px;
}

.card-header-shop p {
    margin-bottom: 0;
    font-weight: 600;
    font-size: 16px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Corps de carte */
.card-body {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Empêche tout débordement */
}

/* Assurer que les contenus des cartes sont bien alignés */
.card-body > * {
    max-width: 100%;
}

/* Pied de carte */
.card-footer {
    background: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px;
}

/* Style pour les images */
.shop-item-image {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    transition: all 0.3s ease;
}

.shop-image-container:hover .shop-item-image {
    transform: scale(1.05);
}

/* Style pour le conteneur des images pour assurer une hauteur uniforme */
.shop-image-container {
    height: 200px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(248, 249, 250, 0.5);
    border-radius: 8px;
    overflow: hidden; /* Empêche les images de déborder */
}

/* Style pour les badges */
.badge-item-count {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    display: inline-block;
    margin-top: 5px;
}

.badge-item-price {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
}

/* Style pour la description */
.item-desc {
    font-size: 13px;
    color: #6c757d;
    margin-top: 10px;
    flex-grow: 1;
    max-height: 80px;
    overflow-y: auto;
}

/* Style pour le bouton d'achat */
.btn-buy {
    background: #e74c3c; /* Rouge vif */
    border: none;
    border-radius: 20px;
    padding: 8px 20px;
    color: white !important; /* Texte blanc forcé */
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(231, 76, 60, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-buy:hover {
    background: #c0392b; /* Rouge plus foncé au survol */
    box-shadow: 0 6px 12px rgba(231, 76, 60, 0.4);
    transform: translateY(-2px);
}
.shops {
    background: url('../img/webshop/header.png') no-repeat top center;
    width: 90%;
    height: 164px;
    margin: auto;
    /*    margin-left: auto;
        margin-right: auto;
        margin-top: -10px;
        margin-bottom: 10px;*/
    -webkit-box-shadow: 0 0 10px 3px rgba(0, 0, 0, 0.35);
    -moz-box-shadow: 0 0 10px 3px rgba(0, 0, 0, 0.35);
    box-shadow: 0 0 10px 3px rgba(0, 0, 0, 0.35);
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    /*border: 0.001em solid #fff511;*/
}
.shops:hover {
    -webkit-filter: brightness(120%);
    filter: brightness(120%);
}