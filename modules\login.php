<?php 

    if (isLoggedIn()) redirect('usercp/');

?>
<div class="col-md-8 pl-md-0">
    <div class="auth-form-wrapper px-4 py-5">
        <a href="/" class="noble-ui-logo d-block mb-2"><img class="img-fluid" src="<?php template_img() ?>neo_logo.png" style="max-width: 155px; height: auto; width: auto;"></a>
        <h5 class="text-muted font-weight-normal mb-4" style="color: #f4e4a6 !important;">Welcome back! Log in to your account.</h5>
    
        <?php
            if (check($_POST['login_submit'])) {
                $captcha_enabled = config('captcha_enabled_login');
                $captcha_verified = true;
                
                if ($captcha_enabled) {
                    $screetkey = config('captcha_screetkey');
                    $response = null;
                    $recaptcha = new ReCaptcha($screetkey);
                    $captcha_verified = false;

                    if ($_POST['g-recaptcha-response']) {
                        $response = $recaptcha->verifyResponse($_SERVER["REMOTE_ADDR"], $_POST["g-recaptcha-response"]);
                        $captcha_verified = ($response != null && $response->success);
                    }
                }

                try {
                    if ($captcha_verified) {
                        $accountLogin = new login();
                        $accountLogin->setUsername(strtolower($_POST['login_username']));
                        $accountLogin->setpassword($_POST['login_password']);
                        $accountLogin->accountLogin();
                    } else {
                        throw new Exception('ReCAPTCHA failed, try again.');
                    }
                } catch (Exception $exception) {
                    message($exception->getMessage(), 'error');
                }
            }
        ?>
        <form class="forms-sample" action="<?php module_url('login/'); ?>" method="post">
            <div class="form-group">
                <label for="username" style="color: #f4e4a6 !important; font-weight: 500;">Username </label>
                <input class="form-control" id="username" type="text" name="login_username" maxlength="25" required style="color: #f4e4a6 !important; background: rgba(13, 13, 13, 0.9) !important; border: 2px solid #8b5a3c !important; border-radius: 10px; padding: 15px 20px; font-size: 16px;" />
            </div>
            <div class="form-group">
                <label for="password" style="color: #f4e4a6 !important; font-weight: 500;">Password </label>
                <input class="form-control" id="password" type="password" name="login_password" required style="color: #f4e4a6 !important; background: rgba(13, 13, 13, 0.9) !important; border: 2px solid #8b5a3c !important; border-radius: 10px; padding: 15px 20px; font-size: 16px;" />
            </div>
            <?php if (config('captcha_enabled_login')): ?>
            <div class="mt-3">
                <div class="g-recaptcha" data-sitekey="<?php echo config('captcha_sitekey') ?>"></div>
            </div>
            <?php endif; ?>
            <div class="mt-3">
                <button type="submit" class="btn btn-primary mr-2 mb-2 mb-md-0 text-white" name="login_submit" value="ok">Log in</button>
            </div>
            <a href="https://reset.neoaion.fr/request-reset" class="d-block mt-3 text-muted" target="_blank" style="color: #d4af37 !important; text-decoration: none;">Forgot password?</a>
            <a href="<?php module_url('register/'); ?>" class="d-block mt-3 text-muted" style="color: #d4af37 !important; text-decoration: none;"> Not a member? Sign up</a>
        </form>
    </div>
</div>