<?php
/**
 * AionCMS
 * https://aioncms.com
 * 
 * <AUTHOR> <https://lautaroangelico.com/>
 * @copyright (c) 2012-2019 Lautaro Angelico, All Rights Reserved
 */

if(!defined('access') or !access) die();

if(isLoggedIn()) {
	# usercp 
	echo '<div class="panel panel-sidebar panel-usercp">';
		echo '<div class="panel-heading">';
			echo '<h3 class="panel-title">User Menu <a href="'.module_url('logout/', true).'" class="btn btn-primary btn-xs pull-right">logout</a></h3>';
		echo '</div>';
		echo '<div class="panel-body">';
			echo '<ul>';
				if($_SESSION['is_staff'] == true) echo '<li><img src="'.template_img(true).'usercp_icons/forumevents_ico.png" /><a href="'.__BASE_URL__.'aioncp/" target="_blank">Aioncp</a> </li>';
			echo '</ul>';
			$usercpSidebarMenu = config('usercp_sidebar_menu', true);
			if(is_array($usercpSidebarMenu)) {
				foreach($usercpSidebarMenu as $usercpSidebarElement) {
					echo '<ul>';
						echo '<li><img src="'.template_img(true).'usercp_icons/'.$usercpSidebarElement[2].'" /><a href="'.module_url($usercpSidebarElement[1], true).'">'.$usercpSidebarElement[0].'</a></li>';
					echo '</ul>';
				}
			}
		echo '</div>';
	echo '</div>';
} else {
	echo '<div class="panel panel-sidebar">';
		echo '<div class="panel-heading">';
			echo '<h3 class="panel-title">LOGIN<a href="'.module_url('recovery/', true).'" class="btn btn-primary btn-xs pull-right">Forgot Password ?</a></h3>';
		echo '</div>';
		echo '<div class="panel-body">';
			echo '<form action="'.module_url('', true).'login/" method="post">';
				echo '<div class="form-group">';
					echo '<input type="text" class="form-control" id="loginBox1" name="login_username" placeholder="Username..." required>';
				echo '</div>';
				echo '<div class="form-group">';
					echo '<input type="password" class="form-control" id="loginBox2" name="login_password" placeholder="Password..." required>';
				echo '</div>';
				echo '<button type="submit" name="login_submit" value="ok" class="btn btn-primary">Login</button>';
			echo '</form>';
		echo '</div>';
	echo '</div>';
}

echo '<div class="sidebar-block">';
	echo '<a href="'.config('launcher_link').'" class="sidebar-download"></a>';
echo '</div>';

/*
echo '<div class="sidebar-block">';
	echo '<a href="'.config('forum_url').'" target="_blank" class="sidebar-enchant"></a>';
echo '</div>';
*/

echo '<div class="panel panel-sidebar">';
	echo '<div class="panel-heading">';
		echo '<h4 class="panel-title" style="font-size:12px">TOP RANKINGS <a href="'.__BASE_URL__.'rankings/abyss" class="btn btn-primary btn-xs pull-right" style="text-align:center;width:22px;">+</a></h4>';
	echo '</div>';
	try {

		// ABYSS RANKING
		$rankingData = loadCacheFile('rankings.abyss.cache');
		if(!$rankingData) throw new Exception();
		
		$result = rankingCacheToArray($rankingData);
		
		echo '<div class="panel-body" style="font-size:12px">';
			echo '<table class="table table-condensed">';
				echo '<tr>';
					echo '<th>'.lang('home_ranking_title_name').'</th>';
					echo '<th>'.lang('home_ranking_title_abyss').'</th>';
				echo '</tr>';
				$i = 1;
				foreach($result as $row) {
					if($i >= 6) continue;
					echo '<tr>';
						echo '<td>'.$row[1].'</td>';
						echo '<td>'.number_format($row[6]).'</td>';
					echo '</tr>';
								
					$i++;
				}
			echo '</table>';
		echo '</div>';
					
		} catch(Exception $ex) {}
		try {
						
			// KILLS RANKING
			$rankingData = loadCacheFile('rankings.kills.cache');
			if(!$rankingData) throw new Exception();
						
			$result = rankingCacheToArray($rankingData);
						
			echo '<div class="panel-body" style="font-size:12px">';
				echo '<table class="table table-condensed">';
					echo '<tr>';
						echo '<th>'.lang('home_ranking_title_name').'</th>';
						echo '<th>'.lang('home_ranking_title_kills').'</th>';
					echo '</tr>';
								
					$i = 1;
					foreach($result as $row) {
						if($i >= 6) continue;
						echo '<tr>';
							echo '<td>'.$row[1].'</td>';
							echo '<td>'.number_format($row[6]).'</td>';
						echo '</tr>';
									
						$i++;
					}
				echo '</table>';
			echo '</div>';
		} catch(Exception $ex) {}
echo '</div>';