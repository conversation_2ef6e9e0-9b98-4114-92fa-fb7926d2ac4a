# ✅ Enable Rewrite Engine
RewriteEngine on

# ✅ Force HTTPS
RewriteCond %{HTTPS} !=on
RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# ✅ Hide server and PHP info
<IfModule mod_headers.c>
    Header unset Server
    Header unset X-Powered-By
    Header always unset X-Powered-By
</IfModule>

# ✅ Block common scanner user-agents
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_USER_AGENT} (nmap|nikto|dirbuster|sqlmap|acunetix|netsparker) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# ✅ Block access to sensitive files
<FilesMatch "^(\.htaccess|\.htpasswd|\.git|\.env|\.svn|config\.php|configuration\.php)">
    Order deny,allow
    Deny from all
</FilesMatch>

# ✅ Disable directory listing
Options -Indexes

# ✅ Your rewrite rules
RewriteRule ^page/([a-zA-Z0-9\_/]+)/?$ index.php?request=$1
RewriteRule ^legion/(.*)/?$ legion.php?request=profile/legion/$1
RewriteRule ^legionemblem/([0-9]+).dds$ api/legionEmblem.php?key=WJaddps23DZM&legionid=$1

# ✅ Block XSS and suspicious query strings
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2})
    RewriteRule .* index.php [F,L]
</IfModule>

# ✅ Global CORS support for launcher/API access
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, X-Requested-With"
</IfModule>

# ✅ Preflight request support for CORS
<IfModule mod_rewrite.c>
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
</IfModule>

# ✅ Explicitly allow CORS for images and fonts
<IfModule mod_headers.c>
    <FilesMatch "\.(jpg|jpeg|png|gif|webp|svg|ttf|woff|woff2|otf|eot|css)$">
        Header always set Access-Control-Allow-Origin "*"
    </FilesMatch>
</IfModule>
