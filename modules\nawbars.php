<header id="main-header">
    <nav class="navbar fixed-top navbar-expand-lg navbar-light">
        <div class="container">
            <!-- Site Logo -->
            <a id="logo" class="navbar-brand" href="https://elyon-aion.com" target="_blank"><img src="<?php template_img() ?>neo_logo.png" style="max-width: 120px; height: auto; width: auto; filter: brightness(1.2) sepia(1) hue-rotate(35deg) saturate(1.5);"></a>
            <!-- Dropdown Button -->
            <button id="hamburger" class="navbar-toggler" type="button" data-toggle="collapse"
                data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false"
                aria-label="Toggle navigation">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav ml-auto">
                    <?php if($_REQUEST['request'] != 'siege/' && $_REQUEST['request'] != 'siege' && $_REQUEST['request'] != 'event/' && $_REQUEST['request'] != 'event') { ?>
                        <li class="nav-item active">
                            <a class="nav-link" href="#about"><?php echo lang('navbar_about') ?></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#games"><?php echo lang('navbar_connect') ?></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#comunity"><?php echo lang('navbar_community') ?></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#ranking"><?php echo lang('navbar_ranking') ?></a>
                        </li>
                    <?php } else { ?>
                        <li class="nav-item active">
                            <a class="nav-link" href="<?php base_url() ?>">Home</a>
                        </li>
                    <?php } ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php module_url() ?>siege/">Siege Time</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/saison/index.html">Saison Time</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php module_url() ?>register/"><?php echo lang('navbar_register') ?></a>
                    </li>
                    <li class="nav-item dropdown smaller">
                        <a class="nav-link dropdown-toggle" href="" id="dropdown09" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <span class="flag-icon flag-icon-us"></span><?php echo lang('navbar_lang') ?>
                        </a>
                        <div class="dropdown-menu" aria-labelledby="dropdown09">
                            <a class="dropdown-item" href="<?php module_url() ?>lang/to/en"><span class="flag-icon flag-icon-en"></span>English</a>
                            <a class="dropdown-item" href="<?php module_url() ?>lang/to/fr"><span class="flag-icon flag-icon-cn"> </span>France</a>
                            <a class="dropdown-item" href="<?php module_url() ?>lang/to/ru"><span class="flag-icon flag-icon-ru"> </span>Русский</a>
                        </div>
                    </li>
                    <li class="nav-item">
                        <?php if (isLoggedIn()) { ?>
                            <a class="nav-link" href="<?php module_url() ?>usercp/"><?php echo $_SESSION['email'] ?></a>
                        <?php } else { ?>
                            <a class="nav-link" href="<?php module_url() ?>login/"><?php echo lang('navbar_login') ?></a>
                        <?php } ?>
                        
                    </li>
                </ul>
            </div>
        </div>
    </nav>
</header>