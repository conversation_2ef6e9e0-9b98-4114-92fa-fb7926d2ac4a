.usercp-block .container {
    -webkit-box-sizing: content-box !important;
    -moz-box-sizing: content-box !important;
    box-sizing: content-box !important;
}

.btn-primary {
    color: #fff;
    background-color: #000000;
    border-color: #111111;
}

.btn-primary:focus,
.btn-primary.focus {
    color: #fff;
    background-color: #333333;
    border-color: #111111;
}

.btn-primary:hover {
    color: #fff;
    background-color: #333333;
    border-color: #111111;
}

.btn-primary:active,
.btn-primary.active,
.open>.dropdown-toggle.btn-primary {
    color: #fff;
    background-color: #333333;
    border-color: #111111;
}


/* vertical align on td */

.table tbody>tr>td.vert-align {
    vertical-align: middle;
}


/* SEPTEMBER UPDATE */

.container {
    width: 1060px !important;
}

.navbar {
    margin-bottom: 0 !important;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    border-radius: 0 !important;
    border-color: #222;
}

.navbar-nav>li>a {
    padding: 25px 35px;
}

.dropdown-menu {
    background-color: #222 !important;
    /*border: 1px solid #ccc;*/
    border: 1px solid rgba(204, 204, 204, .15);
    border-radius: 2px;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
}

.dropdown-menu>li>a {
    color: #9d9d9d;
}

.dropdown-menu>li>a:focus,
.dropdown-menu>li>a:hover {
    color: #fff;
    text-decoration: none;
    background-color: #444;
}

.dropdown-menu .divider {
    background-color: #555;
}

@media screen and (min-width: 768px) {
    .dropdown:hover .dropdown-menu,
    .btn-group:hover .dropdown-menu {
        display: block;
    }
    .dropdown-menu {
        margin-top: 0;
    }
    .dropdown-toggle {
        margin-bottom: 2px;
    }
    .navbar .dropdown-toggle,
    .nav-tabs .dropdown-toggle {
        margin-bottom: 0;
    }
}