@import "https://fonts.googleapis.com/css?family=Special+Elite";
.shop-container {
    width: 940px;
    margin: 0 auto;
    position: relative;
    top: -100px;
    min-height: 600px
}

.shop-container .shop-header {
    background: #000 url(../img/webshop/honeycomb_mesh.jpg) repeat;
    width: 100%;
    border: 1px solid #333;
    color: #fff;
    padding-bottom: 20px;
    font-size: 14px;
    font-family: special elite, cursive
}

.shop-container .shop-header .row {
    padding: 40px 0 0
}

.shop-container .shop-header a {
    color: #ff9600
}

.shop-container .shop-header a:hover,
.shop-container .shop-header a:active {
    color: #fc0
}

.shop-container .shop-content {
    width: 100%;
    background: #fff;
    border-left: 1px solid #333;
    border-right: 1px solid #333;
    border-bottom: 1px solid #333
}

.shop-container>.shop-content>.row {
    margin: 0;
    display: table;
    width: 100%
}

.shop-container>.shop-content>.row>.sidebar {
    background: #000;
    padding: 0;
    height: 100%;
    display: table-cell;
    float: none
}

.shop-container>.shop-content>.row>.items-content {
    display: table-cell;
    float: none;
    padding-top: 15px;
    overflow: hidden
}

.nav-sidebar>li {
    border-bottom: 1px dotted #121212
}

.nav-sidebar>li>a {
    padding-right: 15px;
    padding-left: 15px;
    font-size: 12px;
    color: #fff;
    cursor: pointer
}

.nav-sidebar>li>a:hover,
.nav-sidebar>li>a:focus {
    background-color: #111;
    color: #fc0
}

.nav-sidebar>li>ul {
    list-style-type: none;
    margin: 0;
    padding: 0
}

.nav-sidebar>li>ul>.sub-category>a {
    color: #aaa;
    padding-left: 30px;
    font-size: 11px;
    background-color: #000
}

.nav-sidebar>li>ul>.sub-category>a:hover,
.nav-sidebar>li>ul>.sub-category>a:focus {
    color: #fc0
}

.shop-item-text {
    font-weight: 700;
    color: #000
}

.shop-quality-0 { color: #000000 !important; }
.shop-quality-1 { color: #000000 !important; }
.shop-quality-2 { color: #69e15e !important; }
.shop-quality-3 { color: #4ccfff !important; }
.shop-quality-4 { color: #f0b71c !important; }
.shop-quality-5 { color: #f08033 !important; }
.shop-quality-6 { color: #8f39ce !important; }
.shop-quality-7 { color: #d9a839 !important; }
.shop-quality-8 { color: #d944ec !important; }
.shop-quality-9 { color: #fe4b4b !important; }

.shop-items-table tr td {
    padding: 20px 0!important
}

.shop-item-detail {
    background: #0d161c;
    overflow: auto;
    padding: 20px;
    border: 5px solid #1b252e;
    margin-bottom: 2px;
    color: #fff;
}

.shop-purchase-box {
    background: #f1f1f1;
    padding: 20px;
    border-top: 4px solid red
}

/* Styles modernes pour le shop */

/* Style pour les cartes d'items */
.card-colored {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
}

/* Assurer que toutes les cartes ont la même hauteur dans une rangée */
.grid-margin {
    margin-bottom: 20px;
    height: 100%;
    display: flex;
}

.grid-margin > .card-colored {
    width: 100%;
}

.card-colored:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* En-tête de carte */
.card-header-shop {
    background: linear-gradient(135deg, #2c3e50, #1a2530);
    color: white;
    border-bottom: none;
    padding: 12px 15px;
}

.card-header-shop p {
    margin-bottom: 0;
    font-weight: 600;
    font-size: 16px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Corps de carte */
.card-body {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Empêche tout débordement */
}

/* Assurer que les contenus des cartes sont bien alignés */
.card-body > * {
    max-width: 100%;
}

/* Pied de carte */
.card-footer {
    background: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 12px 20px;
}

/* Style pour les images des items dans le shop */
.card-body .shop-item-image {
    max-width: 100%;  /* Largeur maximale de 100% du conteneur */
    max-height: 100%; /* Hauteur maximale de 100% du conteneur */
    width: auto;      /* Largeur automatique pour maintenir les proportions */
    height: auto;     /* Hauteur automatique pour maintenir les proportions */
    object-fit: contain; /* Garde les proportions de l'image sans la déformer */
    margin: 0 auto; /* Centre l'image horizontalement */
    display: block; /* Permet le centrage avec margin auto */
    transition: transform 0.3s ease;
    filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

.shop-image-container:hover .shop-item-image {
    transform: scale(1.05);
}

/* Style pour le conteneur des images pour assurer une hauteur uniforme */
.shop-image-container {
    height: 200px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(248, 249, 250, 0.5);
    border-radius: 8px;
    overflow: hidden; /* Empêche les images de déborder */
}

/* Style pour les badges */
.badge-item-count {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    display: inline-block;
    margin-top: 5px;
}

.badge-item-price {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
}

/* Style pour la description */
.item-desc {
    font-size: 13px;
    color: #6c757d;
    margin-top: 10px;
    flex-grow: 1;
    max-height: 80px;
    overflow-y: auto;
}

/* Style pour le bouton d'achat */
.btn-buy {
    background: #e74c3c; /* Rouge vif */
    border: none;
    border-radius: 20px;
    padding: 8px 20px;
    color: white !important; /* Texte blanc forcé */
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(231, 76, 60, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-buy:hover {
    background: #c0392b; /* Rouge plus foncé au survol */
    box-shadow: 0 6px 12px rgba(231, 76, 60, 0.4);
    transform: translateY(-2px);
}

/* Style pour les catégories */
.nav-pills .nav-link {
    border-radius: 20px;
    padding: 8px 15px;
    transition: all 0.3s ease;
    margin-bottom: 5px;
    color: #495057;
}

.nav-pills .nav-link:hover {
    background-color: #f8f9fa;
    color: #ff7200;
}

.nav-sub-categories {
    margin-left: 20px !important;
    margin-top: 5px !important;
}

/* Style pour la pagination */
.pagination .page-item .page-link {
    border-radius: 20px;
    margin: 0 3px;
    color: #495057;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #ff7200, #ff5500);
    color: white;
    border: none;
    box-shadow: 0 4px 6px rgba(255, 114, 0, 0.2);
}

.pagination .page-item .page-link:hover {
    background-color: #f8f9fa;
    color: #ff7200;
}

/* Style pour les icônes de bonus */
.bonus-item-icon {
    display: inline-block;
    margin: 5px;
    padding: 5px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.bonus-item-icon:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.2);
}

/* Style pour la liste des items */
.list-group-item {
    border-left: none;
    border-right: none;
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

/* Style pour les détails d'item */
.item-description {
    line-height: 1.6;
    color: #6c757d;
}