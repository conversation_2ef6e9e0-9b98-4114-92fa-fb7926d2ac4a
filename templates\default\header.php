<nav class="navbar top-navbar">
    <div class="container">
        <div class="navbar-content">
            <a href="https://elyon-aion.com" class="navbar-brand" target="_blank"><img src="<?php template_img() ?>neo_logo.png" style="max-width: 155px; height: auto; width: auto; filter: brightness(1.2) sepia(1) hue-rotate(35deg) saturate(1.5);"></a>
            <ul class="navbar-nav">
                <?php if($_SESSION['is_staff'] == true) { ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo __BASE_URL__ ?>aioncp/" target="_blank">
                            <i class="link-icon" data-feather="user"></i>
                            <span class="menu-title">
                                <?php echo lang('header_page1') ?> <i class="link-icon" data-feather="chevron-right"></i>
                            </span>
                        </a>
                    </li>
                <?php } ?>
                <li class="nav-item">
                    <a class="nav-link" href="#" target="_blank">
                        <i class="link-icon" data-feather="users"></i>
                        <span class="menu-title">
                            <?php echo lang('header_page2') ?> <i class="link-icon" data-feather="chevron-right"></i>
                        </span>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button"
                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="flag-icon flag-icon-us mt-1" title="us"></i>
                        <span class="font-weight-medium ml-1 mr-1">
                            English
                        </span>
                    </a>
                    <div class="dropdown-menu" aria-labelledby="languageDropdown">
                        <a href="<?php module_url() ?>lang/to/en" class="dropdown-item py-2"><i class="flag-icon flag-icon-us" title="us"
                                id="us"></i> <span class="ml-1">English </span></a>
                        <a href="<?php module_url() ?>lang/to/fr" class="dropdown-item py-2"><i class="flag-icon flag-icon-fr" title="fr"
                                id="fr"></i> <span class="ml-1">France </span></a>
                        <a href="<?php module_url() ?>lang/to/ru" class="dropdown-item py-2"><i class="flag-icon flag-icon-ru" title="ru"
                                id="ru"></i> <span class="ml-1"><?php echo lang('navbar_about') ?> </span></a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="<?php module_url() ?>logout/" class="nav-link"> <i data-feather="log-out"></i>
                        <span>
                            <?php echo lang('header_page3') ?>
                        </span>
                    </a>
                </li>
            </ul>
            <button class="navbar-toggler navbar-toggler-right d-lg-none align-self-center" type="button"
                data-toggle="horizontal-menu-toggle"> <i data-feather="menu"></i> </button>
        </div>
    </div>
</nav>
<nav class="bottom-navbar">
    <div class="container">
        <ul class="nav page-navigation">
        <?php
                if ($_REQUEST['request'] == 'usercp/account/' || $_REQUEST['request'] == 'usercp/account') {
                    echo '<li class="nav-item active">';
                        echo '<a class="nav-link" href="'.module_url('usercp/account/', true).'"> <i class="link-icon" data-feather="box"></i>';
                            echo '<span class="menu-title">'.lang('header_page4').'</span>';
                        echo '</a>';
                    echo '</li>';
                } else {
                    echo '<li class="nav-item">';
                        echo '<a class="nav-link" href="'.module_url('usercp/account/', true).'"> <i class="link-icon" data-feather="box"></i>';
                            echo '<span class="menu-title">'.lang('header_page4').'</span>';
                        echo '</a>';
                    echo '</li>';
                }
                if ($_REQUEST['request'] == 'usercp/password/' || $_REQUEST['request'] == 'usercp/password') {
                    echo '<li class="nav-item active">';
                        echo '<a class="nav-link" href="'.module_url('usercp/password/', true).'"> <i class="link-icon" data-feather="key"></i>';
                            echo '<span class="menu-title">'.lang('header_page5').'</span>';
                        echo '</a>';
                    echo '</li>';
                } else {
                    echo '<li class="nav-item">';
                        echo '<a class="nav-link" href="'.module_url('usercp/password/', true).'"> <i class="link-icon" data-feather="key"></i>';
                            echo '<span class="menu-title">'.lang('header_page5').'</span>';
                        echo '</a>';
                    echo '</li>';
                }
                if ($_REQUEST['request'] == 'usercp/characters/' || $_REQUEST['request'] == 'usercp/characters') {
                    echo '<li class="nav-item active">';
                        echo '<a class="nav-link" href="'.module_url('usercp/characters/', true).'"> <i class="link-icon" data-feather="user-check"></i>';
                            echo '<span class="menu-title">'.lang('header_page6').'</span>';
                        echo '</a>';
                    echo '</li>';
                } else {
                    echo '<li class="nav-item">';
                        echo '<a class="nav-link" href="'.module_url('usercp/characters/', true).'"> <i class="link-icon" data-feather="user-check"></i>';
                            echo '<span class="menu-title">'.lang('header_page6').'</span>';
                        echo '</a>';
                    echo '</li>';
                }
                if ($_REQUEST['request'] == 'donate/' || $_REQUEST['request'] == 'donate' || $_REQUEST['request'] == 'donate/paypal/' || $_REQUEST['request'] == 'donate/paypal') {
                    echo '<li class="nav-item active">';
                        echo '<a class="nav-link" href="'.module_url('donate/', true).'"> <i class="link-icon" data-feather="dollar-sign"></i>';
                            echo '<span class="menu-title">'.lang('header_page9').'</span>';
                        echo '</a>';
                    echo '</li>';
                } else {
                    echo '<li class="nav-item">';
                        echo '<a class="nav-link" href="'.module_url('donate/', true).'"> <i class="link-icon" data-feather="dollar-sign"></i>';
                            echo '<span class="menu-title">'.lang('header_page9').'</span>';
                        echo '</a>';
                    echo '</li>';
                }             
                if ($_REQUEST['request'] == 'usercp/vote/' || $_REQUEST['request'] == 'usercp/vote') {
                    echo '<li class="nav-item active">';
                        echo '<a class="nav-link" href="'.module_url('usercp/vote/', true).'"> <i class="link-icon" data-feather="bell"></i>';
                            echo '<span class="menu-title">'.lang('header_page11').'</span>';
                        echo '</a>';
                    echo '</li>';
                } else {
                    echo '<li class="nav-item">';
                        echo '<a class="nav-link" href="'.module_url('usercp/vote/', true).'"> <i class="link-icon" data-feather="bell"></i>';
                            echo '<span class="menu-title">'.lang('header_page11').'</span>';
                        echo '</a>';
                    echo '</li>';
                }
                if ($_REQUEST['request'] == 'shop' || $_REQUEST['request'] == 'shop/' || $_REQUEST['request'] == 'shop/item/id/'.$_GET['id'].'/ws/'.$_GET['ws'].'/' || $_REQUEST['request'] == 'shop/list/c/'.$_GET['c'].'/' || $_REQUEST['request'] == 'shop/list/c/'.$_GET['c'].'/p/'.$_GET['p'].'/') {
                    echo '<li class="nav-item active">';
                        echo '<a class="nav-link" href="'.module_url('shop/', true).'"> <i class="link-icon" data-feather="shopping-cart"></i>';
                            echo '<span class="menu-title">'.lang('header_page12').'</span>';
                        echo '</a>';
                    echo '</li>';
                } else {
                    echo '<li class="nav-item">';
                        echo '<a class="nav-link" href="'.module_url('shop/', true).'"> <i class="link-icon" data-feather="shopping-cart"></i>';
                            echo '<span class="menu-title">'.lang('header_page12').'</span>';
                        echo '</a>';
                    echo '</li>';
                }
            ?>
        </ul>
    </div>
</nav>