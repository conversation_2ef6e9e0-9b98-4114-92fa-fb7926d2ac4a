<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELYON AION - Seasons Pass</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@300;400;500;600;700;800;900&family=Cinzel:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" type="image/ico" href="img/favicon/fav.ico">
    <style>
        :root {
            --primary-gold: #FFD700;
            --secondary-gold: #FFA500;
            --dark-gold: #B8860B;
            --light-gold: #FFEF94;
            --accent-gold: #F4E4BC;
            --bg-dark: #0A0A0A;
            --bg-gradient: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            --card-bg: linear-gradient(145deg, #1e1e1e 0%, #2a2a2a 50%, #1e1e1e 100%);
            --text-gold: #FFD700;
            --text-light: #CCCCCC;
            --text-muted: #999999;
            --shadow-gold: rgba(255, 215, 0, 0.3);
            --shadow-dark: rgba(0, 0, 0, 0.8);
            --glow-gold: 0 0 20px rgba(255, 215, 0, 0.5);
            --border-gold: 2px solid #FFD700;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Disable text selection highlighting */
        *::selection {
            background: transparent;
        }

        *::-moz-selection {
            background: transparent;
        }

        /* Prevent text selection on interactive elements */
        .season-card, .status-badge, .timer, .season-level {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Allow text selection only on content text */
        .season-content, .subtitle {
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        .season-content::selection {
            background: rgba(255, 215, 0, 0.2);
            color: inherit;
        }

        .season-content::-moz-selection {
            background: rgba(255, 215, 0, 0.2);
            color: inherit;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-dark);
            background-image:
                radial-gradient(circle at 20% 50%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 165, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(184, 134, 11, 0.1) 0%, transparent 50%);
            color: var(--text-light);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                linear-gradient(45deg, transparent 49%, rgba(255, 215, 0, 0.02) 50%, transparent 51%),
                linear-gradient(-45deg, transparent 49%, rgba(255, 215, 0, 0.02) 50%, transparent 51%);
            background-size: 60px 60px;
            pointer-events: none;
            z-index: 0;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 3rem 2rem;
            position: relative;
            z-index: 1;
        }

        header {
            text-align: center;
            margin-bottom: 4rem;
            position: relative;
        }

        h1 {
            font-family: 'Cinzel', serif;
            font-size: clamp(2.5rem, 5vw, 4.5rem);
            background: linear-gradient(135deg, var(--primary-gold) 0%, var(--secondary-gold) 50%, var(--primary-gold) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            letter-spacing: 3px;
            font-weight: 700;
            text-shadow: 0 0 30px var(--shadow-gold);
            position: relative;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
            box-shadow: var(--glow-gold);
        }

        .subtitle {
            font-size: 1.3rem;
            color: var(--text-muted);
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
            font-weight: 300;
        }

        .seasons-container {
            display: flex;
            flex-direction: column;
            gap: 2.5rem;
            margin-top: 3rem;
            width: 100%;
        }

        .season-card {
            background: var(--card-bg);
            border-radius: 24px;
            padding: 2.5rem 3rem;
            box-shadow:
                0 8px 32px var(--shadow-dark),
                0 0 0 1px rgba(255, 215, 0, 0.2),
                inset 0 1px 0 rgba(255, 215, 0, 0.1);
            border: var(--border-gold);
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            width: 100%;
            max-width: none;
            margin: 0 auto;
            min-height: unset;
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: space-between;
            gap: 3rem;
            animation: fadeInUp 0.9s cubic-bezier(.4,2,.6,1) both;
            backdrop-filter: blur(10px);
        }

        .season-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 215, 0, 0.05) 0%,
                transparent 30%,
                transparent 70%,
                rgba(255, 165, 0, 0.05) 100%);
            pointer-events: none;
        }

        .season-card::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--primary-gold), var(--secondary-gold), var(--dark-gold), var(--primary-gold));
            border-radius: 26px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(60px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .season-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 20px 60px var(--shadow-dark),
                0 0 0 1px var(--primary-gold),
                var(--glow-gold),
                inset 0 1px 0 rgba(255, 215, 0, 0.2);
        }

        .season-card:hover::after {
            opacity: 1;
        }

        @keyframes pulseGold {
            0%, 100% {
                box-shadow:
                    0 8px 32px var(--shadow-dark),
                    0 0 0 1px rgba(255, 215, 0, 0.2),
                    0 0 20px rgba(255, 215, 0, 0.3);
            }
            50% {
                box-shadow:
                    0 12px 48px var(--shadow-dark),
                    0 0 0 1px var(--primary-gold),
                    0 0 40px rgba(255, 215, 0, 0.6);
            }
        }

        .season-info {
            flex: 2 1 0;
            min-width: 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            position: relative;
            z-index: 2;
        }

        .season-title {
            font-family: 'Cinzel', serif;
            font-size: 1.8rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-gold) 0%, var(--secondary-gold) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            line-height: 1.3;
            text-shadow: 0 0 20px var(--shadow-gold);
        }

        .season-icon {
            font-size: 2rem;
            filter: drop-shadow(0 0 10px var(--shadow-gold));
        }

        .season-levels {
            margin-bottom: 1.2rem;
            display: flex;
            flex-wrap: wrap;
            gap: 0.8rem;
        }

        .season-level {
            display: inline-block;
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
            color: var(--primary-gold);
            padding: 0.5rem 1.2rem;
            border-radius: 20px;
            font-size: 0.95rem;
            font-weight: 500;
            border: 1px solid rgba(255, 215, 0, 0.3);
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        .season-level:hover {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 165, 0, 0.2) 100%);
            border-color: var(--primary-gold);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
        }

        .season-content {
            margin-bottom: 1.5rem;
            line-height: 1.7;
            color: var(--text-light);
            font-size: 1.1rem;
            max-width: 800px;
        }

        .season-content p {
            margin-bottom: 1.2rem;
            text-align: justify;
        }

        .season-content ul {
            padding-left: 1.5rem;
            margin: 1rem 0;
        }

        .season-content li {
            margin-bottom: 0.6rem;
            position: relative;
        }

        .season-content li::marker {
            color: var(--primary-gold);
        }

        .season-content h3 {
            font-size: 1.2rem;
            margin: 1.2rem 0 0.8rem 0;
            background: linear-gradient(135deg, var(--primary-gold) 0%, var(--secondary-gold) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
            font-family: 'Cinzel', serif;
        }

        .timer-side {
            flex: 1 1 280px;
            min-width: 280px;
            max-width: 380px;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: flex-start;
            gap: 1rem;
            position: relative;
            z-index: 2;
        }

        .timer-topbar {
            position: absolute;
            top: 15px;
            right: 40px;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.8rem;
            z-index: 3;
            background: none;
            border-radius: 0;
            padding: 0;
            box-shadow: none;
            animation: none;
        }

        .timer-status-row {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.3rem;
        }

        .status-badge {
            position: static;
            margin-bottom: 0;
            margin-right: 0;
            padding: 0.6rem 1.5rem;
            border-radius: 25px;
            font-size: 0.95rem;
            font-weight: 600;
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
            border: 1px solid var(--primary-gold);
            color: var(--primary-gold);
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
            letter-spacing: 1px;
            z-index: 2;
            animation: badgePop 0.7s cubic-bezier(.4,2,.6,1);
            backdrop-filter: blur(10px);
            text-transform: uppercase;
            transition: all 0.3s ease;
        }

        .status-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
        }

        .timer-row {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.05) 0%, rgba(255, 165, 0, 0.05) 100%);
            border-radius: 20px;
            padding: 0.8rem 1.8rem;
            box-shadow:
                0 4px 15px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 215, 0, 0.2);
            animation: slideInTimer 0.8s cubic-bezier(.4,2,.6,1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .timer-label {
            font-size: 1rem;
            color: var(--primary-gold);
            font-weight: 600;
            min-width: 90px;
            text-align: left;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .timer {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.4rem;
            letter-spacing: 2px;
            background: linear-gradient(135deg, var(--primary-gold) 0%, var(--secondary-gold) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            min-width: 120px;
            text-align: right;
            font-weight: 700;
            animation: timerPulse 2s infinite alternate;
            text-shadow: 0 0 20px var(--shadow-gold);
        }

        @keyframes timerPulse {
            0% {
                filter: brightness(1);
                text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            }
            100% {
                filter: brightness(1.2);
                text-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
            }
        }

        .progress-container {
            width: 100%;
            height: 12px;
            background: linear-gradient(90deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
            border-radius: 8px;
            margin-top: 1.5rem;
            overflow: hidden;
            animation: progressBarGrow 1.2s cubic-bezier(.4,2,.6,1);
            border: 1px solid rgba(255, 215, 0, 0.2);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        @keyframes progressBarGrow {
            from { width: 0; opacity: 0; }
            to { width: 100%; opacity: 1; }
        }

        .progress-bar {
            height: 100%;
            border-radius: 7px;
            background: linear-gradient(90deg, var(--primary-gold) 0%, var(--secondary-gold) 50%, var(--primary-gold) 100%);
            transition: width 0.8s cubic-bezier(.4,2,.6,1);
            position: relative;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .active-badge {
            background: linear-gradient(135deg, var(--primary-gold) 0%, var(--secondary-gold) 100%) !important;
            color: #000 !important;
            border: 1px solid var(--primary-gold) !important;
            animation: badgePulse 2s infinite alternate;
            font-weight: 700;
        }

        @keyframes badgePulse {
            0% {
                box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
                transform: scale(1);
            }
            100% {
                box-shadow: 0 6px 25px rgba(255, 215, 0, 0.6);
                transform: scale(1.05);
            }
        }

        .upcoming-badge {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%) !important;
            color: var(--text-muted) !important;
            border: 1px solid rgba(255, 215, 0, 0.3) !important;
        }

        .completed-badge {
            background: linear-gradient(135deg, rgba(100, 100, 100, 0.1) 0%, rgba(80, 80, 80, 0.1) 100%) !important;
            color: #666 !important;
            border: 1px solid rgba(100, 100, 100, 0.3) !important;
        }

        /* Additional animations */
        @keyframes slideInTimer {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes badgePop {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .container {
                padding: 2rem 1.5rem;
            }

            .season-card {
                flex-direction: column;
                align-items: stretch;
                padding: 2rem;
                gap: 2rem;
            }

            .timer-side {
                align-items: stretch;
                max-width: none;
                min-width: 0;
            }

            .timer-topbar {
                position: static;
                align-items: stretch;
                margin-bottom: 1rem;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1.5rem 1rem;
            }

            h1 {
                font-size: 2.5rem;
                letter-spacing: 2px;
            }

            .subtitle {
                font-size: 1.1rem;
            }

            .season-card {
                padding: 1.5rem;
                border-radius: 20px;
            }

            .season-title {
                font-size: 1.5rem;
            }

            .timer {
                font-size: 1.2rem;
            }

            .season-levels {
                gap: 0.5rem;
            }

            .season-level {
                padding: 0.4rem 1rem;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 1rem 0.5rem;
            }

            h1 {
                font-size: 2rem;
            }

            .season-card {
                padding: 1rem;
                margin: 0 0.5rem;
            }

            .timer-row {
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }

            .timer-label {
                min-width: auto;
            }

            .timer {
                min-width: auto;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>ELYON AION Seasons Pass</h1>
            <p class="subtitle">Embark on an epic journey through the evolving seasons of Atreia. Experience progressive content, unlock new challenges, and witness the world transform with each passing season.</p>
        </header>
        <div class="seasons-container">
            <!-- Season 1 -->
            <div class="season-card" id="season1">
                <div class="timer-topbar">
                    <div class="timer-status-row">
                        <span class="status-badge"></span>
                    </div>
                    <div class="timer-row">
                        <span class="timer-label" id="timer-label1"></span>
                        <span class="timer" id="timer1"></span>
                    </div>
                </div>
                <div class="season-info">
                    <h2 class="season-title"><span class="season-icon">🌱</span> Season 1 - The Beginning </h2>
                    <div class="season-levels">
                        <span class="season-level">📢 Level Cap 50</span>
                        <span class="season-level">⏳ Duration: 90 Days</span>
                    </div>
                    <div class="season-content">
                        <p>The gates to Atreia open, and your adventure begins! In Season 1, all players start fresh with a level cap of 50, offering a structured and balanced progression for everyone. This is your chance to master your class, explore the vast world of Aion 4.6, and prepare yourself for greater challenges ahead.</p>
                        <ul>
                            <h3>🔹 Important Information:</h3>
                            <li>❌ The new classes (Bard, Gunner, Aethertech) are NOT available yet! Only the six original classes (Warrior, Scout, Mage, Priest, Ranger, and Gladiator) can be played.</li>
                            <h3>🔹 What to expect in Season 1?</h3>
                            <li>✔️ Iconic low-level dungeons like Draupnir Cave, Tiak’s Eye, and Dark Poeta</li>
                            <li>✔️ A competitive and balanced PvP experience in Abyss Battles.</li>
                            <li>✔️ The opportunity to farm and optimize your gear to its full potential.</li>
                        </ul>
                        <p>Whether you're a new player discovering Aion for the first time or a veteran reliving the golden era of Aion 4.6, Season 1 is the perfect opportunity to build your legend. Will you rise among the strongest warriors before the next season arrives?</p>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar" id="progress1"></div>
                    </div>
                </div>
            </div>
            <!-- Season 2 -->
            <div class="season-card" id="season2">
                <div class="timer-topbar">
                    <div class="timer-status-row">
                        <span class="status-badge"></span>
                    </div>
                    <div class="timer-row">
                        <span class="timer-label" id="timer-label2"></span>
                        <span class="timer" id="timer2"></span>
                    </div>
                </div>
                <div class="season-info">
                    <h2 class="season-title"><span class="season-icon">🔥</span> Season 2 - The Awakening</h2>
                    <div class="season-levels">
                        <span class="season-level">📢 Level Cap 55</span>
                        <span class="season-level">⏳ Duration: 90 Days</span>
                    </div>
                    <div class="season-content">
                        <p>Atreia expands as Season 2 arrives! With the level cap increased to 55, you now have access to new zones, more powerful gear, and challenging PvE and PvP content.</p>
                        <ul>
                            <h3>🔹 Important Information:</h3>
                            <li>❌ The new classes (Bard, Gunner, Aethertech) are NOT available yet! Only the six original classes (Warrior, Scout, Mage, Priest, Ranger, and Gladiator) can be played.</li>
                            <h3>🔹 What’s new in Season 2?</h3>
                            <li>✔️ Unlock the breathtaking regions of Inggison and Gelkmaros, filled with dangerous creatures and hidden treasures.</li>
                            <li>✔️ Explore the Beshmundir Temple, a challenging dungeon with formidable bosses and epic loot.</li>
                            <li>✔️ Stronger PvP battles as players gain new abilities and engage in Abyss fortress sieges.</li>
                        </ul>
                        <p>Season 2 is where the real challenges begin. The dungeons are tougher, the enemies stronger, and only those who prove their worth will thrive. Are you ready to step into the fire?</p>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar" id="progress2"></div>
                    </div>
                </div>
            </div>
            <!-- Season 3 -->
            <div class="season-card" id="season3">
                <div class="timer-topbar">
                    <div class="timer-status-row">
                        <span class="status-badge"></span>
                    </div>
                    <div class="timer-row">
                        <span class="timer-label" id="timer-label3"></span>
                        <span class="timer" id="timer3"></span>
                    </div>
                </div>
                <div class="season-info">
                    <h2 class="season-title"><span class="season-icon">⚔️</span> Season 3 - The War for Power</h2>
                    <div class="season-levels">
                        <span class="season-level">📢 Level Cap 60</span>
                        <span class="season-level">⏳ Duration: 90 Days</span>
                    </div>
                    <div class="season-content">
                        <p>Atreia is no longer the same. The balance of power shifts as the level cap reaches 60, opening the doors to even more dangerous territories and intense large-scale battles.</p>
                        <ul>
                            <h3>🔹 Important Information:</h3>
                            <li>✅ New class unlocked! The Bard joins the fight, bringing powerful healing and damage-dealing melodies to the battlefield.</li>
                            <h3>🔹 What’s new in Season 3?</h3>
                            <li>✔️ The mystical lands of Sarpan and Tiamaranta await those brave enough to conquer them.</li>
                            <li>✔️ New world bosses and fortress sieges will challenge even the strongest legions.</li>
                            <li>✔️ PvP warfare intensifies with new battlegrounds and mass PvP zones.</li>
                        </ul>
                        <p>With more power comes greater conflict. The battles will be bloodier, the victories more rewarding, and only the best will survive. Will you emerge as one of the true champions of Atreia?</p>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar" id="progress3"></div>
                    </div>
                </div>
            </div>
            <!-- Season 4 -->
            <div class="season-card" id="season4">
                <div class="timer-topbar">
                    <div class="timer-status-row">
                        <span class="status-badge"></span>
                    </div>
                    <div class="timer-row">
                        <span class="timer-label" id="timer-label4"></span>
                        <span class="timer" id="timer4"></span>
                    </div>
                </div>
                <div class="season-info">
                    <h2 class="season-title"><span class="season-icon">🌬️</span> Season 4 - The Ultimate Challenge</h2>
                    <div class="season-levels">
                        <span class="season-level">📢 Level Cap 65</span>
                        <span class="season-level">⏳ Duration: 90 Days</span>
                    </div>
                    <div class="season-content">
                        <p>The new season is here, and Atreia reaches its full potential. With the level cap at 65, you now have access to everything Aion 4.6 has to offer. The strongest warriors, the deadliest dungeons, and the most prestigious rewards await those who dare to claim them.</p>
                        <ul>
                            <h3>🔹 Important Information:</h3>
                            <li>✅ New classes unlocked! The Gunner and Aethertech are now available, adding more diversity to both PvP and PvE.</li>
                            <h3>🔹 What’s new in Season 4?</h3>
                            <li>✔️ The entire world is open to you – every dungeon, every battleground, every fortress is now accessible!</li>
                            <li>✔️ Face legendary bosses and uncover epic loot in the most difficult instances.</li>
                            <li>✔️ Dominate the battlefield in mass PvP wars, where only the strongest legions will prevail.</li>
                        </ul>
                        <p>This is your final test. Prove that you are one of the best, leave your mark on the server, and earn the rewards that only the strongest will ever see.<br>
                        Will you claim eternal glory in NeoAion? Your fate is in your hands.</p>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar" id="progress4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/gsap.min.js"></script>
    <script>
        // Season configuration for 2025
        const SEASON_DURATION = 90 * 24 * 60 * 60 * 1000;
        const FIRST_SEASON_2025_START = new Date(2025, 3, 26, 20, 0, 0);

        // Time formatting
        function formatTime(ms) {
            if (ms <= 0) return "00:00:00";
            const days = Math.floor(ms / (1000 * 60 * 60 * 24));
            const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((ms % (1000 * 60)) / 1000);
            if (days > 0) {
                return `${days}d ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // Update season display
        function updateSeasons() {
            const now = new Date();
            for (let i = 0; i < 4; i++) {
                const seasonStart = new Date(FIRST_SEASON_2025_START.getTime() + (i * SEASON_DURATION));
                const seasonEnd = new Date(seasonStart.getTime() + SEASON_DURATION);
                const seasonElement = document.getElementById(`season${i+1}`);
                // timer-topbar children
                const timerTopbar = seasonElement.querySelector('.timer-topbar');
                const badge = timerTopbar.querySelector('.status-badge');
                const timerLabel = timerTopbar.querySelector('.timer-label');
                const timer = timerTopbar.querySelector('.timer');
                const progressBar = seasonElement.querySelector('.progress-bar');

                if (now < seasonStart) {
                    badge.className = 'status-badge upcoming-badge';
                    badge.textContent = 'UPCOMING';
                    seasonElement.classList.remove('active');
                    timerLabel.textContent = "Starts in:";
                    timer.textContent = formatTime(seasonStart - now);
                    progressBar.style.width = '0%';
                }
                else if (now < seasonEnd) {
                    badge.className = 'status-badge active-badge';
                    badge.textContent = 'ACTIVE';
                    seasonElement.classList.add('active');
                    timerLabel.textContent = "Ends in:";
                    timer.textContent = formatTime(seasonEnd - now);
                    const progress = (now - seasonStart) / SEASON_DURATION * 100;
                    progressBar.style.width = `${progress}%`;
                }
                else {
                    badge.className = 'status-badge completed-badge';
                    badge.textContent = 'COMPLETED';
                    seasonElement.classList.remove('active');
                    timerLabel.textContent = "Ended on:";
                    timer.textContent = seasonEnd.toLocaleDateString('en-GB');
                    progressBar.style.width = '100%';
                }
            }
        }

        // Initialization
        document.addEventListener('DOMContentLoaded', () => {
            updateSeasons();
            setInterval(updateSeasons, 1000);

            // Enhanced card animations
            document.querySelectorAll('.season-card').forEach((card, idx) => {
                // Initial state
                card.style.opacity = 0;
                card.style.transform = 'translateY(60px) scale(0.95)';

                // Staggered entrance animation
                setTimeout(() => {
                    card.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                    card.style.opacity = 1;
                    card.style.transform = 'translateY(0) scale(1)';
                }, 300 + idx * 200);

                // Enhanced hover effects with GSAP
                card.addEventListener('mouseenter', () => {
                    gsap.to(card, {
                        scale: 1.02,
                        y: -8,
                        duration: 0.4,
                        ease: "power2.out",
                        boxShadow: '0 20px 60px rgba(0,0,0,0.8), 0 0 0 1px #FFD700, 0 0 20px rgba(255,215,0,0.5)'
                    });

                    // Animate progress bar on hover
                    const progressBar = card.querySelector('.progress-bar');
                    if (progressBar) {
                        gsap.to(progressBar, {
                            boxShadow: '0 0 25px rgba(255,215,0,0.8)',
                            duration: 0.3
                        });
                    }
                });

                card.addEventListener('mouseleave', () => {
                    gsap.to(card, {
                        scale: 1,
                        y: 0,
                        duration: 0.4,
                        ease: "power2.out",
                        boxShadow: '0 8px 32px rgba(0,0,0,0.8), 0 0 0 1px rgba(255,215,0,0.2)'
                    });

                    const progressBar = card.querySelector('.progress-bar');
                    if (progressBar) {
                        gsap.to(progressBar, {
                            boxShadow: '0 0 15px rgba(255,215,0,0.6)',
                            duration: 0.3
                        });
                    }
                });
            });

            // Add parallax effect to background
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const parallax = document.querySelector('body::before');
                if (parallax) {
                    document.body.style.backgroundPosition = `0 ${scrolled * 0.5}px`;
                }
            });

            // Add floating animation to season icons
            document.querySelectorAll('.season-icon').forEach(icon => {
                gsap.to(icon, {
                    y: -5,
                    duration: 2,
                    ease: "power2.inOut",
                    yoyo: true,
                    repeat: -1
                });
            });
        });
    </script>
</body>
</html>