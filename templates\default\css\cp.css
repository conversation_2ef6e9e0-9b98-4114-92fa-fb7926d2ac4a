.inline-block {
    display: inline-block;
}

.links {
    text-align:center;
}
.links li {
    display: inline;
    line-height: 30px;
}

.text-center {
    text-align: center;
}



.race {
    padding-left: 25px;
    line-height: 30px;
}
.race.elyos {
    background: url(../img/shop/race_0.gif) no-repeat 0 center;
}

.race.asmo {
    background: url(../img/shop/race_1.gif) no-repeat 0 center;
}

.dropdown-menu {
    min-width: 300px;
}

.item-desc {
    max-height: 7rem;
    overflow: auto;
    scrollbar-width: thin;
}

.qina-amount {
    line-height: 30px;
    font-weight: bold;
}

.list-items-lotto {
    list-style: none;
}

.lh-item {
    line-height: 32px;
}

.wheel-card {
    background: url(../img/wheel_bg.jpg) no-repeat;
    background-size: cover;
    min-height: 7rem;
    color: #fff;
}
.wheel-title {
    font-weight: bold;
    font-size: 1.5rem;
}
.wheel-card a {
    color: #fff;
}

.btn-chest {
    width:154px;
    height:50px;
    border: 0;
    z-index: 99999;
}
.btn-chest-ru {
    background: url(../img/btn-chest.png) no-repeat 0 center;
}

.btn-chest-en {
    background: url(../img/btn-chest-en.png) no-repeat 0 center;
}

.red {
    color: #ff0000;
}