/* Golden Theme for Aion Website */

/* Golden Theme Variables */
:root {
  --primary-gold: #d4af37;
  --dark-gold: #b8941f;
  --light-gold: #f4e4a6;
  --gold-gradient: linear-gradient(135deg, #2c1810 0%, #4a2c1a 25%, #6b3e2a 50%, #8b5a3c 75%, #a67c52 100%);
  --dark-bg: #1a1a1a;
  --darker-bg: #0d0d0d;
  --gold-text: #d4af37;
  --light-gold-text: #f4e4a6;
  --dark-gold-text: #b8941f;
  --gold-border: #8b5a3c;
  --gold-hover: #e6c547;
}

/* Background texture overlay */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('../img/golden-texture.svg') repeat;
  opacity: 0.2;
  z-index: -1;
  pointer-events: none;
}

/* Main body styling override */
html,
body {
  background: var(--gold-gradient) !important;
  background-attachment: fixed !important;
  background-size: cover !important;
  color: var(--light-gold-text) !important;
  min-height: 100vh;
}

/* Text color overrides */
h1, h2, h3, h4, h5, h6 {
  color: var(--primary-gold) !important;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

p {
  color: var(--light-gold-text) !important;
}

/* Link styling */
a {
  color: var(--gold-text) !important;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--gold-hover) !important;
  text-decoration: none;
}

/* Colored class override */
.colored {
  color: var(--gold-hover) !important;
}

/* Loader styling */
#loader-wrapper {
  background: var(--gold-gradient);
}

.loader-logo {
  color: var(--primary-gold) !important;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

.loader-text {
  color: var(--light-gold-text) !important;
}

#progress {
  background: linear-gradient(90deg, var(--dark-gold), var(--primary-gold), var(--gold-hover));
}

/* Navigation styling */
.navbar,
.navbar-light {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border-bottom: 2px solid var(--gold-border) !important;
  backdrop-filter: blur(10px);
  border: 1px solid var(--gold-border) !important;
}

/* Logo styling for homepage */
.navbar-brand img {
  max-width: 120px !important;
  height: auto !important;
  width: auto !important;
  object-fit: contain !important;
  filter: brightness(1.2) sepia(1) hue-rotate(35deg) saturate(1.5) !important;
}

.navbar.scrolled,
.navbar-light.scrolled {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.98) 0%, rgba(44, 24, 16, 0.98) 100%) !important;
  border: 1px solid var(--gold-border) !important;
}

.navbar-brand {
  color: var(--primary-gold) !important;
}

.navbar-brand img {
  filter: brightness(1.2) sepia(1) hue-rotate(35deg) saturate(1.5);
}

.nav-link {
  color: var(--light-gold-text) !important;
  transition: all 0.3s ease;
}

.navbar-light .navbar-nav .nav-link {
  color: var(--light-gold-text) !important;
}

.nav-link:hover,
.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link:active,
.navbar-light .navbar-nav .nav-link:focus {
  color: var(--primary-gold) !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.nav-item.active .nav-link {
  color: var(--primary-gold) !important;
  font-weight: bold;
}

.navbar-toggler {
  border-color: var(--gold-border) !important;
}

.navbar-toggler .icon-bar {
  background: var(--primary-gold) !important;
}

/* Header styling */
#main-header {
  position: relative;
  z-index: 1000;
}

/* Button styling */
.btn {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  border: 1px solid var(--gold-border) !important;
  color: var(--darker-bg) !important;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
  transition: all 0.3s ease;
}

.btn:hover {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--gold-hover) 100%) !important;
  color: var(--darker-bg) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(212, 175, 55, 0.3);
}

.btn-primary {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  border-color: var(--gold-border) !important;
}

/* Card styling */
.card {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border: 1px solid var(--gold-border) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.1);
  backdrop-filter: blur(10px);
}

.card-header {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  color: var(--darker-bg) !important;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
  border-bottom: 2px solid var(--gold-border) !important;
}

.card-body {
  color: var(--light-gold-text) !important;
}

/* Form styling */
.form-control {
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
  border-radius: 8px;
}

.form-control:focus {
  border-color: var(--primary-gold) !important;
  box-shadow: 0 0 10px rgba(212, 175, 55, 0.4) !important;
  background: rgba(13, 13, 13, 1) !important;
}

.form-control::placeholder {
  color: rgba(244, 228, 166, 0.6) !important;
}

/* Table styling */
.table {
  background: rgba(26, 26, 26, 0.9) !important;
  color: var(--light-gold-text) !important;
}

.table thead th {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  color: var(--darker-bg) !important;
  border-color: var(--gold-border) !important;
}

.table tbody tr:hover {
  background: rgba(212, 175, 55, 0.1) !important;
}

/* Footer styling */
#main-footer {
  background: linear-gradient(135deg, rgba(13, 13, 13, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%) !important;
  border-top: 2px solid var(--gold-border);
  color: var(--light-gold-text) !important;
}

#copyright {
  color: var(--light-gold-text) !important;
}

.footer-time-title,
.footer-time {
  color: var(--light-gold-text) !important;
}

/* Hero section styling */
.hero-caption h1 {
  color: var(--primary-gold) !important;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

/* Content containers */
.container {
  position: relative;
  z-index: 1;
}

/* Modal styling */
.modal-content {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.98) 0%, rgba(44, 24, 16, 0.98) 100%) !important;
  border: 2px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
}

.modal-header {
  border-bottom: 2px solid var(--gold-border) !important;
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
}

.modal-title {
  color: var(--darker-bg) !important;
}

/* Alert styling */
.alert {
  border: 1px solid var(--gold-border) !important;
  background: rgba(26, 26, 26, 0.9) !important;
  color: var(--light-gold-text) !important;
}

.alert-success {
  background: rgba(76, 175, 80, 0.2) !important;
  border-color: #4caf50 !important;
  color: #a5d6a7 !important;
}

.alert-danger {
  background: rgba(244, 67, 54, 0.2) !important;
  border-color: #f44336 !important;
  color: #ef9a9a !important;
}

/* Text utilities */
.text-muted {
  color: rgba(244, 228, 166, 0.7) !important;
}

.text-white {
  color: var(--light-gold-text) !important;
}

/* Badge styling */
.badge {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  color: var(--darker-bg) !important;
}

/* Dropdown styling */
.dropdown-menu {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.98) 0%, rgba(44, 24, 16, 0.98) 100%) !important;
  border: 1px solid var(--gold-border) !important;
}

.dropdown-item {
  color: var(--light-gold-text) !important;
}

.dropdown-item:hover {
  background: rgba(212, 175, 55, 0.2) !important;
  color: var(--primary-gold) !important;
}

/* Specific overrides for existing elements */
.button {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  color: var(--darker-bg) !important;
  border: 1px solid var(--gold-border) !important;
}

.button:hover {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--gold-hover) 100%) !important;
  color: var(--darker-bg) !important;
}

.button.secondary {
  background: rgba(26, 26, 26, 0.9) !important;
  color: var(--light-gold-text) !important;
  border: 1px solid var(--gold-border) !important;
}

.button.secondary:hover {
  background: rgba(212, 175, 55, 0.2) !important;
  color: var(--primary-gold) !important;
}

/* Game cards and content areas */
.game-card-right {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  color: var(--light-gold-text) !important;
  border: 1px solid var(--gold-border) !important;
}

.game-tags {
  background: rgba(26, 26, 26, 0.9) !important;
  color: var(--light-gold-text) !important;
  border-left: 2px solid var(--primary-gold) !important;
}

.job-card {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  color: var(--light-gold-text) !important;
  border-left: 2px solid var(--primary-gold) !important;
}

.score-card {
  background: rgba(26, 26, 26, 0.9) !important;
  color: var(--light-gold-text) !important;
  border: 1px solid var(--gold-border) !important;
}

.score-card:hover {
  background: rgba(212, 175, 55, 0.2) !important;
  color: var(--primary-gold) !important;
}

.steam-btn {
  background: rgba(26, 26, 26, 0.9) !important;
  color: var(--light-gold-text) !important;
  border: 1px solid var(--gold-border) !important;
}

.steam-btn:hover a {
  background: rgba(212, 175, 55, 0.2) !important;
  color: var(--primary-gold) !important;
}

.button-store {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  color: var(--darker-bg) !important;
  border: 1px solid var(--gold-border) !important;
}

.button-store:hover {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--gold-hover) 100%) !important;
  color: var(--darker-bg) !important;
}

/* Contact form */
#contactForm {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  color: var(--light-gold-text) !important;
  border: 1px solid var(--gold-border) !important;
}

/* Contact list styling */
#contact ul li i {
  background: rgba(26, 26, 26, 0.9) !important;
  color: var(--primary-gold) !important;
}

/* HR elements */
hr {
  background: linear-gradient(90deg, transparent, var(--primary-gold), transparent) !important;
  border: none !important;
  height: 2px !important;
}

/* Input field overrides */
input[type="text"],
input[type="email"],
input[type="password"],
textarea {
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
textarea:focus {
  border-color: var(--primary-gold) !important;
  box-shadow: 0 0 10px rgba(212, 175, 55, 0.4) !important;
}

/* Footer background override */
#main-footer,
#footer {
  background: linear-gradient(135deg, rgba(13, 13, 13, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%) !important;
  border-top: 2px solid var(--gold-border) !important;
}

/* Ensure all white backgrounds are converted */
*[style*="background: #FFF"],
*[style*="background: #FAFAFA"],
*[style*="background: #EEE"],
*[style*="background-color: #fff"],
*[style*="background-color: #FAFAFA"],
*[style*="background-color: #EEE"] {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  color: var(--light-gold-text) !important;
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .navbar-nav {
    background: rgba(26, 26, 26, 0.98) !important;
    border-radius: 8px;
    margin-top: 10px;
    padding: 10px;
  }

  .game-card-right {
    margin: 10px 0 !important;
    padding: 15px 20px !important;
  }

  .job-card {
    margin: 10px 0 !important;
    padding: 20px !important;
  }
}

@media (max-width: 767.98px) {
  :root {
    --gold-gradient: linear-gradient(180deg, #2c1810 0%, #4a2c1a 25%, #6b3e2a 50%, #8b5a3c 75%, #a67c52 100%);
  }

  .btn,
  .button {
    width: 100% !important;
    margin: 5px 0 !important;
  }

  .score-card {
    margin: 5px 0 !important;
    width: 100% !important;
  }

  .button-store {
    width: 100% !important;
    margin: 10px 0 !important;
  }

  #contactForm {
    padding: 15px !important;
    margin: 10px 0 !important;
  }
}

@media (max-width: 575.98px) {
  .container {
    padding-left: 10px;
    padding-right: 10px;
  }

  .game-card-right {
    padding: 10px !important;
  }

  .job-card {
    padding: 15px !important;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .btn:hover,
  .button:hover {
    transform: none !important;
  }

  .nav-link:hover {
    transform: none !important;
  }
}

/* Dark mode preference support */
@media (prefers-color-scheme: dark) {
  :root {
    --gold-gradient: linear-gradient(135deg, #1a1a1a 0%, #2c1810 25%, #4a2c1a 50%, #6b3e2a 75%, #8b5a3c 100%);
  }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Override specific style.css backgrounds */
.navbar-light {
  background-color: transparent !important;
}

/* Override any white or light backgrounds */
*[style*="background-color: rgba(255, 255, 255"],
*[style*="background-color: #ffffff"],
*[style*="background-color: #fff"],
*[style*="background: #fff"],
*[style*="background: #ffffff"],
*[style*="background: rgba(255, 255, 255"] {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  color: var(--light-gold-text) !important;
}

/* Specific game elements */
.game-card,
.game-item,
.content-card {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  color: var(--light-gold-text) !important;
  border: 1px solid var(--gold-border) !important;
}

/* Section backgrounds */
section {
  background: transparent !important;
}

#about,
#games,
#comunity,
#ranking,
#contact {
  background: transparent !important;
  color: var(--light-gold-text) !important;
}

/* Container backgrounds */
.container {
  background: transparent !important;
}

/* Override any remaining white text on dark backgrounds */
.text-dark {
  color: var(--light-gold-text) !important;
}

/* Ensure proper contrast for readability */
.bg-white {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  color: var(--light-gold-text) !important;
}

.bg-light {
  background: rgba(26, 26, 26, 0.8) !important;
  color: var(--light-gold-text) !important;
}

/* Text color overrides for better contrast */
.text-black,
.text-dark,
.text-muted {
  color: var(--light-gold-text) !important;
}

/* Fix black text issues */
* {
  color: inherit;
}

/* Ensure all text elements use golden colors */
span,
div,
p,
li,
td,
th {
  color: var(--light-gold-text) !important;
}

/* Override any remaining black text */
*[style*="color: #000"],
*[style*="color: black"],
*[style*="color: #222"],
*[style*="color: #333"] {
  color: var(--light-gold-text) !important;
}

/* Modern card styling improvements */
.card,
.content-card,
.info-card {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border: 2px solid var(--gold-border) !important;
  border-radius: 15px !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(212, 175, 55, 0.15),
    inset 0 1px 0 rgba(212, 175, 55, 0.2) !important;
  backdrop-filter: blur(15px) !important;
  transition: all 0.3s ease !important;
}

.card:hover,
.content-card:hover,
.info-card:hover {
  transform: translateY(-5px) !important;
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.5),
    0 0 30px rgba(212, 175, 55, 0.25),
    inset 0 1px 0 rgba(212, 175, 55, 0.3) !important;
}

/* Link color consistency */
a:not(.btn):not(.button) {
  color: var(--gold-text) !important;
}

a:not(.btn):not(.button):hover {
  color: var(--gold-hover) !important;
}

/* Shop-specific styling overrides */
.shop-container {
  background: transparent !important;
}

.shop-container .shop-header {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border: 1px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
}

.shop-container .shop-header a {
  color: var(--primary-gold) !important;
}

.shop-container .shop-header a:hover,
.shop-container .shop-header a:active {
  color: var(--gold-hover) !important;
}

.shop-container .shop-header .shop-title a {
  color: var(--light-gold-text) !important;
}

.shop-container .shop-header .shop-title a:hover,
.shop-container .shop-header .shop-title a:active {
  color: var(--primary-gold) !important;
}

.shop-container .shop-content {
  background: rgba(26, 26, 26, 0.9) !important;
  border-left: 1px solid var(--gold-border) !important;
  border-right: 1px solid var(--gold-border) !important;
  border-bottom: 1px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
}

.shops {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border: 1px solid var(--gold-border) !important;
  box-shadow: 0 0 10px 3px rgba(212, 175, 55, 0.2) !important;
}

.shops:hover {
  box-shadow: 0 0 15px 5px rgba(212, 175, 55, 0.3) !important;
  filter: brightness(120%) !important;
}

/* Additional shop elements */
.shop-item,
.shop-category {
  background: rgba(26, 26, 26, 0.9) !important;
  border: 1px solid var(--gold-border) !important;
  color: var(--light-gold-text) !important;
}

.shop-item:hover,
.shop-category:hover {
  background: rgba(212, 175, 55, 0.1) !important;
  border-color: var(--primary-gold) !important;
}

/* Ensure all important elements are styled */
.important,
.highlight {
  color: var(--primary-gold) !important;
  font-weight: bold;
}

/* Final catch-all for any missed white backgrounds */
div[style*="background:#fff"],
div[style*="background: #fff"],
div[style*="background-color:#fff"],
div[style*="background-color: #fff"],
span[style*="background:#fff"],
span[style*="background: #fff"] {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  color: var(--light-gold-text) !important;
}

/* Modern improvements */
/* Enhanced button styling */
.btn,
.button {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 50%, var(--gold-hover) 100%) !important;
  border: 2px solid var(--gold-border) !important;
  border-radius: 12px !important;
  color: var(--darker-bg) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  padding: 12px 24px !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3) !important;
}

.btn::before,
.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn:hover::before,
.button:hover::before {
  left: 100%;
}

.btn:hover,
.button:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4) !important;
  border-color: var(--gold-hover) !important;
}

/* Modern typography */
h1, h2, h3, h4, h5, h6 {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--gold-hover) 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  font-weight: 700 !important;
  text-shadow: none !important;
  position: relative !important;
}

/* Fallback for browsers that don't support background-clip */
@supports not (-webkit-background-clip: text) {
  h1, h2, h3, h4, h5, h6 {
    color: var(--primary-gold) !important;
    -webkit-text-fill-color: initial !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5) !important;
  }
}

/* Modern list styling */
ul li {
  position: relative !important;
  padding-left: 25px !important;
  color: var(--light-gold-text) !important;
}

ul li::before {
  content: '▶' !important;
  position: absolute !important;
  left: 0 !important;
  color: var(--primary-gold) !important;
  font-size: 12px !important;
  top: 2px !important;
}

/* Modern input styling */
input[type="text"],
input[type="email"],
input[type="password"],
textarea,
select {
  background: rgba(13, 13, 13, 0.9) !important;
  border: 2px solid var(--gold-border) !important;
  border-radius: 10px !important;
  color: var(--light-gold-text) !important;
  padding: 15px 20px !important;
  transition: all 0.3s ease !important;
  position: relative !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
textarea:focus,
select:focus {
  border-color: var(--primary-gold) !important;
  box-shadow:
    0 0 15px rgba(212, 175, 55, 0.4),
    inset 0 0 10px rgba(212, 175, 55, 0.1) !important;
  background: rgba(13, 13, 13, 1) !important;
  transform: translateY(-2px) !important;
}

/* Modern scrollbar styling */
::-webkit-scrollbar {
  width: 12px !important;
}

::-webkit-scrollbar-track {
  background: var(--darker-bg) !important;
  border-radius: 10px !important;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  border-radius: 10px !important;
  border: 2px solid var(--darker-bg) !important;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--gold-hover) 100%) !important;
}

/* Modern selection styling */
::selection {
  background: var(--primary-gold) !important;
  color: var(--darker-bg) !important;
}

::-moz-selection {
  background: var(--primary-gold) !important;
  color: var(--darker-bg) !important;
}

/* Enhanced glow effects */
.glow-effect {
  position: relative !important;
}

.glow-effect::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--primary-gold), var(--gold-hover), var(--primary-gold));
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glow-effect:hover::after {
  opacity: 0.7;
  animation: glow-pulse 2s infinite;
}

@keyframes glow-pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Modern loading animation improvements */
.loader-logo {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--gold-hover) 50%, var(--primary-gold) 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  font-size: 3rem !important;
  font-weight: 800 !important;
  text-shadow: none !important;
  animation: logo-glow 3s ease-in-out infinite !important;
}

@keyframes logo-glow {
  0%, 100% {
    filter: drop-shadow(0 0 10px rgba(212, 175, 55, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 20px rgba(212, 175, 55, 0.8));
  }
}

/* Modern progress bar */
#progress {
  background: linear-gradient(90deg, var(--primary-gold) 0%, var(--gold-hover) 100%) !important;
  box-shadow: 0 0 15px rgba(212, 175, 55, 0.6) !important;
  border-radius: 10px !important;
}

/* Enhanced table styling */
table {
  background: rgba(13, 13, 13, 0.95) !important;
  border-radius: 15px !important;
  overflow: hidden !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;
}

th {
  background: linear-gradient(135deg, var(--dark-gold) 0%, var(--primary-gold) 100%) !important;
  color: var(--darker-bg) !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  padding: 15px !important;
}

td {
  color: var(--light-gold-text) !important;
  padding: 12px 15px !important;
  border-bottom: 1px solid rgba(212, 175, 55, 0.2) !important;
}

tr:hover {
  background: rgba(212, 175, 55, 0.1) !important;
}

/* Modern alert/notification styling */
.alert,
.notification {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 24, 16, 0.95) 100%) !important;
  border: 2px solid var(--gold-border) !important;
  border-radius: 12px !important;
  color: var(--light-gold-text) !important;
  padding: 20px !important;
  margin: 15px 0 !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
}

.alert-success {
  border-color: #4caf50 !important;
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(16, 44, 24, 0.95) 100%) !important;
}

.alert-warning {
  border-color: #ff9800 !important;
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 32, 16, 0.95) 100%) !important;
}

.alert-danger {
  border-color: #f44336 !important;
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(44, 16, 16, 0.95) 100%) !important;
}

/* Remove blue play button icons from navigation */
.nav-item::before,
.nav-item::after,
.nav-link::before,
.nav-link::after {
  display: none !important;
}

/* Hide any feather icons or play button icons */
.nav-item .feather,
.nav-link .feather,
.nav-item [data-feather],
.nav-link [data-feather],
.nav-item .fa-play,
.nav-link .fa-play,
.nav-item .fa-play-circle,
.nav-link .fa-play-circle {
  display: none !important;
}

/* Remove any blue colored elements from navigation */
.navbar .nav-item *[style*="color: blue"],
.navbar .nav-item *[style*="color: #0000ff"],
.navbar .nav-item *[style*="color: #00f"],
.navbar .nav-link *[style*="color: blue"],
.navbar .nav-link *[style*="color: #0000ff"],
.navbar .nav-link *[style*="color: #00f"] {
  display: none !important;
}

/* Hide any SVG icons that might be blue */
.navbar svg[fill="blue"],
.navbar svg[fill="#0000ff"],
.navbar svg[fill="#00f"],
.navbar svg[stroke="blue"],
.navbar svg[stroke="#0000ff"],
.navbar svg[stroke="#00f"] {
  display: none !important;
}

/* Remove any pseudo-elements that might be creating blue icons */
.navbar-nav .nav-item:before,
.navbar-nav .nav-item:after,
.navbar-nav .nav-link:before,
.navbar-nav .nav-link:after {
  content: none !important;
  display: none !important;
}

/* Clean navigation styling without icons */
.navbar-nav .nav-item {
  position: relative !important;
}

.navbar-nav .nav-link {
  color: var(--light-gold-text) !important;
  font-weight: 500 !important;
  padding: 10px 15px !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
}

.navbar-nav .nav-link:hover {
  color: var(--primary-gold) !important;
  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5) !important;
}

.navbar-nav .nav-item.active .nav-link {
  color: var(--primary-gold) !important;
  font-weight: 600 !important;
}

/* Override any icon fonts or symbol fonts */
.navbar [class*="icon-"]:before,
.navbar [class*="fa-"]:before,
.navbar [class*="feather-"]:before {
  display: none !important;
}

/* Hide any Unicode symbols that might be blue play buttons */
.navbar .nav-item:before,
.navbar .nav-link:before {
  content: "" !important;
}

/* Ensure clean text-only navigation */
.navbar-nav .nav-link {
  font-family: inherit !important;
  line-height: 1.5 !important;
}

/* Additional aggressive removal of any blue elements */
.navbar *[style*="color:blue"],
.navbar *[style*="color:#0000ff"],
.navbar *[style*="color:#00f"],
.navbar *[style*="color: blue"],
.navbar *[style*="color: #0000ff"],
.navbar *[style*="color: #00f"],
.navbar .text-primary,
.navbar .text-info,
.navbar .btn-primary,
.navbar .btn-info,
.navbar .badge-primary,
.navbar .badge-info {
  display: none !important;
}

/* Remove any potential Unicode play symbols */
.navbar *:before,
.navbar *:after {
  content: "" !important;
}

/* Force remove any content that might contain play symbols */
.navbar [class*="play"],
.navbar [id*="play"],
.navbar [data-*="play"] {
  display: none !important;
}

/* Remove any potential CSS-generated content */
.navbar li:before,
.navbar li:after,
.navbar a:before,
.navbar a:after,
.navbar span:before,
.navbar span:after {
  content: none !important;
  display: none !important;
}

/* Override any external CSS that might be adding icons */
.navbar * {
  background-image: none !important;
}

/* Remove any potential Font Awesome or other icon fonts */
.navbar [class*="fa-"],
.navbar [class*="icon-"],
.navbar [class*="glyphicon-"],
.navbar [class*="feather-"] {
  display: none !important;
}

/* Clean slate for navigation items */
.navbar-nav .nav-item,
.navbar-nav .nav-link {
  background: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Remove any list-style that might be adding symbols */
.navbar ul,
.navbar li {
  list-style: none !important;
  list-style-type: none !important;
  list-style-image: none !important;
}

/* Final override for any remaining blue elements */
.navbar * {
  color: var(--light-gold-text) !important;
}

.navbar .nav-link:hover {
  color: var(--primary-gold) !important;
}
