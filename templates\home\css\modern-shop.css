/* Modern Shop CSS - Inspired by professional game shops */

/* Reset and base styles */
.shop-container {
    font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
    color: #333;
    background-color: #f8f9fa;
}

/* Main shop grid */
.shop-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

/* Item card */
.shop-item {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
}

.shop-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

/* Item image container */
.shop-item-image-container {
    position: relative;
    padding-top: 100%; /* 1:1 Aspect Ratio */
    background: linear-gradient(135deg, #f5f7fa, #e4e8f0);
    overflow: hidden;
}

.shop-item-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    padding: 10px;
}

/* Item details */
.shop-item-details {
    padding: 12px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.shop-item-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
    line-height: 1.3;
    height: 36px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* Price and buy section */
.shop-item-purchase {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 8px;
}

.shop-item-price {
    font-weight: 700;
    font-size: 16px;
    color: #333;
}

.shop-item-price .currency {
    font-size: 12px;
    color: #666;
    margin-left: 2px;
}

.shop-item-buy {
    background-color: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
    text-transform: uppercase;
}

.shop-item-buy:hover {
    background-color: #c0392b;
}

/* Badges */
.shop-item-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #e74c3c;
    color: white;
    font-size: 12px;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 4px;
    z-index: 2;
}

.shop-item-badge.discount {
    background: #2ecc71;
}

.shop-item-badge.new {
    background: #3498db;
}

.shop-item-badge.hot {
    background: #f39c12;
}

.shop-item-quantity {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 3px 8px;
    border-radius: 4px;
    z-index: 2;
}

/* Category header */
.shop-category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 30px 0 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.shop-category-title {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Shop filters */
.shop-filters {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.shop-filters-title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
}

.shop-filter-group {
    margin-bottom: 15px;
}

.shop-filter-group-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #555;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .shop-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 15px;
    }
    
    .shop-item-title {
        font-size: 13px;
        height: 34px;
    }
    
    .shop-item-price {
        font-size: 14px;
    }
    
    .shop-item-buy {
        padding: 5px 10px;
        font-size: 12px;
    }
}
